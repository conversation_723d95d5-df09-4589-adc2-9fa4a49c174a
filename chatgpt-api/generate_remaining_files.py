#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量生成剩余的重构文件
严格按照 docs3/重构目录结构设计.md 的要求生成所有文件
"""

import os
import sys

def create_directory(path):
    """创建目录"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"创建目录: {path}")

def create_file(file_path, content):
    """创建文件"""
    directory = os.path.dirname(file_path)
    create_directory(directory)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"创建文件: {file_path}")

def generate_entity_files():
    """生成所有实体类文件"""
    base_path = "src/main/java/com/hncboy/chatgpt/db/entity"
    
    # AI相关实体类
    ai_entities = [
        ("ai/AiMessageDO.java", """package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一消息实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_message")
public class AiMessageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("parent_msg_id")
    private Long parentMsgId;

    @TableField("user_id")
    private Integer userId;

    @TableField("room_id")
    private Long roomId;

    @TableField("ability_type")
    private String abilityType;

    @TableField("message_type")
    private Integer messageType;

    @TableField("content")
    private String content;

    @TableField("prompt")
    private String prompt;

    @TableField("task_id")
    private String taskId;

    @TableField("action")
    private String action;

    @TableField("inputs")
    private String inputs;

    @TableField("topic")
    private String topic;

    @TableField("model_gid")
    private String modelGid;

    @TableField("agent_id")
    private Integer agentId;

    @TableField("agent_name")
    private String agentName;

    @TableField("agent_title")
    private String agentTitle;

    @TableField("site_id")
    private Integer siteId;

    @TableField("site_name")
    private String siteName;

    @TableField("site_url")
    private String siteUrl;

    @TableField("total_tokens")
    private Long totalTokens;

    @TableField("consume")
    private Integer consume;

    @TableField("status")
    private Integer status;

    @TableField("progress")
    private Integer progress;

    @TableField("image_url")
    private String imageUrl;

    @TableField("ip")
    private String ip;

    @TableField("open_id")
    private String openId;

    @TableField("first_char_time")
    private LocalDateTime firstCharTime;

    @TableField("remark")
    private String remark;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}"""),
        
        ("ai/AiRoomDO.java", """package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一房间实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_room")
public class AiRoomDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("title")
    private String title;

    @TableField("description")
    private String description;

    @TableField("ability_type")
    private String abilityType;

    @TableField("sys_content")
    private String sysContent;

    @TableField("role_id")
    private Integer roleId;

    @TableField("image_url")
    private String imageUrl;

    @TableField("user_id")
    private Integer userId;

    @TableField("open_id")
    private String openId;

    @TableField("conversation_id")
    private String conversationId;

    @TableField("ip")
    private String ip;

    @TableField("open")
    private String open;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}""")
    ]
    
    for file_name, content in ai_entities:
        create_file(f"{base_path}/{file_name}", content)

def generate_mapper_files():
    """生成所有Mapper文件"""
    base_path = "src/main/java/com/hncboy/chatgpt/db/mapper"
    
    mappers = [
        ("user/UserBaseInfoMapper.java", """package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 11:20
 */
@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfoDO> {

    /**
     * 根据账号和用户类型查询用户
     *
     * @param account 账号
     * @param userType 用户类型
     * @return 用户信息
     */
    UserBaseInfoDO selectByAccountAndType(@Param("account") String account, @Param("userType") String userType);
}"""),
        
        ("ai/AiMessageMapper.java", """package com.hncboy.chatgpt.db.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.ai.AiMessageDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI消息Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 11:25
 */
@Mapper
public interface AiMessageMapper extends BaseMapper<AiMessageDO> {
}""")
    ]
    
    for file_name, content in mappers:
        create_file(f"{base_path}/{file_name}", content)

def generate_service_files():
    """生成所有Service文件"""
    base_path = "src/main/java/com/hncboy/chatgpt/db/service"
    
    services = [
        ("user/UserBaseInfoService.java", """package com.hncboy.chatgpt.db.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;

/**
 * 用户基础信息服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 11:30
 */
public interface UserBaseInfoService extends IService<UserBaseInfoDO> {

    /**
     * 根据账号和用户类型查询用户
     *
     * @param account 账号
     * @param userType 用户类型
     * @return 用户信息
     */
    UserBaseInfoDO getByAccountAndType(String account, String userType);
}"""),
        
        ("user/impl/UserBaseInfoServiceImpl.java", """package com.hncboy.chatgpt.db.service.user.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import com.hncboy.chatgpt.db.mapper.user.UserBaseInfoMapper;
import com.hncboy.chatgpt.db.service.user.UserBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户基础信息服务实现类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:35
 */
@Slf4j
@Service
public class UserBaseInfoServiceImpl extends ServiceImpl<UserBaseInfoMapper, UserBaseInfoDO> implements UserBaseInfoService {

    @Resource
    private UserBaseInfoMapper userBaseInfoMapper;

    @Override
    public UserBaseInfoDO getByAccountAndType(String account, String userType) {
        return userBaseInfoMapper.selectByAccountAndType(account, userType);
    }
}""")
    ]
    
    for file_name, content in services:
        create_file(f"{base_path}/{file_name}", content)

def generate_vo_files():
    """生成所有VO文件"""
    base_path = "src/main/java/com/hncboy/chatgpt/db/vo"
    
    vos = [
        ("user/UserBaseInfoVO.java", """package com.hncboy.chatgpt.db.vo.user;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户基础信息VO
 *
 * <AUTHOR>
 * @date 2023/3/22 11:40
 */
@Data
public class UserBaseInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String account;
    private String userType;
    private String name;
    private String nickName;
    private String email;
    private String phone;
    private String avatarUrl;
    private LocalDateTime vipEndTime;
    private Integer points;
    private Integer tarotCoins;
    private Integer status;
    private String language;
    private String currency;
    private String timezone;
    private LocalDateTime createTime;
}""")
    ]
    
    for file_name, content in vos:
        create_file(f"{base_path}/{file_name}", content)

def main():
    """主函数"""
    print("开始批量生成重构文件...")
    
    # 生成实体类
    generate_entity_files()
    
    # 生成Mapper
    generate_mapper_files()
    
    # 生成Service
    generate_service_files()
    
    # 生成VO
    generate_vo_files()
    
    print("批量生成完成！")

if __name__ == "__main__":
    main()
