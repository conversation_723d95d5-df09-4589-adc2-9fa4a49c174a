server:
  port: 3082
  servlet:
    context-path: /super/ai
spring:
  # 环境 dev|test|prod
  profiles:
    active: @spring.profiles.active@
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
      enabled: true
  http:
    multipart:
      enabled: true
      max-file-size: 30MB
      max-request-size: 30MB
wx:
  miniapp:
    configs:
      - appid: wxfe365e97c4eb6f9f
        secret: e494427b392b2e1f5adfbe61e828dbd9
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
#  mp:
#    configs:
#      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
#        secret: e54bde3b6a97b435cf43526f3f5e0fb5 # 公众号的appsecret
#        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
#        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值

  pay:
    appId: wx734d3d8a6888e18f #微信公众号或者小程序等的appid
    mchId: 1683747767 #微信支付商户号
    mchKey: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付商户密钥
    subAppId: #服务商模式下的子商户公众账号ID
    subMchId: #服务商模式下的子商户号
    keyPath: C://Users//fdsr04//Desktop//zxl//文档//微信支付//支付证书//apiclient_cert.p12 #p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
    privateKeyPath: C://Users//fdsr04//Desktop//zxl//文档//微信支付//支付证书//apiclient_key.pem
    privateCertPath: C://Users//fdsr04//Desktop//zxl//文档//微信支付//支付证书//apiclient_cert.pem #
    notifyUrl: http://fdsr.ss5.tunnelfrp.com/super/ai/notify/order # 支付回调地址
    apiV3Key: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付V3密钥


    # 支付宝配置
############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: SUPER-AI
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: -1
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true

# rocketmq
#rocketmq:
#  name-server: ************:9876
#  producer:
#    group: my-group
#  consumer:
#    group: my-group
#    topic: super-ai-msg-topic

# 支付宝配置
ali-pay:
  # 支付宝应用ID
  appId: '2021004134628072'
  # 支付宝公钥
  alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1wMxA6LbFTmKM/yqCMHzfu28l42ZEgeFjfQ+XnhBKLAtda/xKSCBm3+T8fSOgGwYBEsEE2NVdCBrcSH44FZkQvcYfiVRd41YxlqvBEZPPGmEm1NIyHNSoCFyVc/UCML2lrKt5VhOujZSVm/ifDorxRSbh5Ph/rD9G86G6Gx3x5ZMAcTaTKhZ3ibVTV/K2c14O4iP8kkqNwj3W8xQ/RQeW3RGs9jbl22Ey17G7lGY+NYBcu3psf9MWOMJ4TjPIuOBmZlFwHAYaosXB4xij0HLutW+bo0mcgUtCbbWsITsBrHrEFPnYPKEHw3eG/0Zijj04xHREN28r0JeQMFd918xwIDAQAB'
  # 私钥
  privateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCJwXTAZ2w7Lp71Jt3EDhdJpbt+BtUOA84FI6h2mfJ+vAfsfE4iKjpxtxWDcUS2BTTbBeItyCfhFMPbI29ZI624DUPDTbmqB9rDQm0k0xkza9XDRJ6bWmlNXB8QqkrI8kPEh/2QZWd/8vYocZlTqMyeQyMSPLpp3PNIHHpSdqw6aSa7FUUdbytnw779cgsjtA4Wr/kwASbdh+FtA0XuUeZzKAjcz4FWh5E9DzdgAI/kkwotzL5fzd+CLxEUkM4OJJnNAfjS/IWVohr0nV0REO5QQDOY4g7HelRHvgozEpR9pp2eeKRmJwSjDxG786yE58Zjpv1yB1aTTg0AKYOwARLDAgMBAAECggEAcy7M/mv7tWiABR/gYiQeGLh5EyGHV9aalvV/0NtcRdfXEEfH1Q1WHOsnE+bLux6TWWeL/RfVMIB6W2s/mVsZ0EFXHaCJ08AJjSanuBjCg5pw/HsN23w3fDXAy+B385mOTHAg9nGznup6JMsHl0oW/WXJl2ArxoS27t7Q8M7Fp3g0WklkVXnefP+Z0Bb2oUueDUj3UMcqPhtauL2yWSrRwWzOmpgRmPyjaIiHsnyHQnWhlJlx6QWvFZidGYTqmMpRIyjKRGXYDML7KNEkBsBn+ZIPprarwPXAGy8DkSCCAvw+QK51M5SuVVP8gRRzwitTPtn8pPItj108/UC/iZRCMQKBgQDTuHDBlwicOHfsnGs+mNO+D6oYnZFmPuFCZmi78rVgXNghE2rPdYK6qzEzwQIflj3Q6aGMY0TbCUKE1kiTLzmN64JP1tVFeuSZtJzhQJymgk/G7sMmFWWi1EwzSjJvyRSU9WqYnjTQkb3ZFV8WEuz05nOJT4K9jOmxkRDCDiVkzQKBgQCmkOzbW7p0NM7xgNsdfhfhgmhAS+HZbgxgWYsK48oksLpdMuCUAzSP+/Y/9yN3weW4I6rmCB95TkEL3UA0FFlMh8PSq3HH2htqc/pU7XHd0oIGwwAvh5unWKur8rH0HzvMOQ+822sWurPQTtKa9Ie11O4I1K78hmlh3CUTnn7VzwKBgQCwPu2+ehg0090hHuU36sj5gti6PRD46IdkWxP8wYKzgRZ6Ekvr/4qgEjqXNqip2swCyTouP8TmAcNaCXfI7psexVppVBGeETuTqIKgEMVW50u58rCJV26QKeJ1H+L0o5N6nQHromec6HX97IiHO6H+DIlJWOMhH9i6dGRa5qPCUQKBgEU2MzVzCCcBSnxPbrzyiDnMVkVhQpd2gu4GRyBfRr+4L7hKS8c1DcJGkCxEJRi+GRZKu9iZx3pAagbkCKmbSv/h6hy/4KeKmZnCV2Kyx33E2wK0RH97edIvpiXs0sT9tXtGuqNfCUCIk+8a0ahRs5J6x+4jchK8R03CjCV61nlBAoGASGbES/2wbYv5Oe9McDR6bep6wyf7wDQauK+YSjnZakKoxFEEZRPI8QLVhZzUA+1CUtHoWGnKgvqL+KiYt7AqNwEW5jUgef5r9TC34gHfAraP4EZ38yVacgWFZDm3yf+5amkhRIB11YubWbDqC/2vYnxOT+1DinAub8PWiO24MTM='
  # 域名
  domain: 'https://zns.zjfdsr.com/api'


facebook:
  app-id: ****************
  app-secret: 0cc0b221ca4e86e1979eebd8a692bd58
  redirect-uri: https://vn.alwzc.com/api/auth/facebook/callback

google:
  client-id: ************-jfnuqrqu587b850p4bbh5irsgcu61v2d.apps.googleusercontent.com
  client-secret: 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
  local-domain : https://tarot.alwzc.com

se-pay:
  bankName: MB
  accountNumber: *************
  se-secret: s9$K9@Qm#7zP!2xL*5FvE&3RgY8%WbT6

