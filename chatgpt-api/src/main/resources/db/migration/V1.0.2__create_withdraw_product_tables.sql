-- =====================================================
-- 超级智能社重构数据库表结构创建脚本 - 提现功能和产品表
-- 作者: wuqm
-- 时间: 2025-01-12
-- 版本: V1.0.2
-- 说明: 创建提现功能相关表和产品相关表
-- =====================================================

-- =====================================================
-- 6. 提现功能相关表 (3张表) - 新增独立提现功能
-- =====================================================

-- 提现申请表 (新增，支持多种提现方式)
CREATE TABLE `withdraw_application` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `application_no` varchar(64) NOT NULL COMMENT '申请单号',
    `withdraw_type` varchar(20) NOT NULL COMMENT '提现类型 ALIPAY:支付宝 WECHAT:微信 BANK:银行卡',
    `withdraw_account` varchar(100) NOT NULL COMMENT '提现账户',
    `account_name` varchar(50) NOT NULL COMMENT '账户姓名',
    `bank_name` varchar(100) DEFAULT NULL COMMENT '银行名称(银行卡提现)',
    `bank_branch` varchar(100) DEFAULT NULL COMMENT '开户行(银行卡提现)',
    `amount` decimal(15,2) NOT NULL COMMENT '提现金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `fee` decimal(15,2) DEFAULT 0.00 COMMENT '手续费',
    `actual_amount` decimal(15,2) NOT NULL COMMENT '实际到账金额',
    `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态 PENDING:待审核 APPROVED:已通过 REJECTED:已拒绝 PROCESSING:处理中 COMPLETED:已完成 FAILED:失败',
    `apply_reason` varchar(500) DEFAULT NULL COMMENT '申请原因',
    `reject_reason` varchar(500) DEFAULT NULL COMMENT '拒绝原因',
    `process_remark` varchar(500) DEFAULT NULL COMMENT '处理备注',
    `process_user` varchar(50) DEFAULT NULL COMMENT '处理人',
    `process_time` datetime DEFAULT NULL COMMENT '处理时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `third_party_order_no` varchar(64) DEFAULT NULL COMMENT '第三方订单号',
    `third_party_result` text COMMENT '第三方处理结果(JSON)',
    `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_application_no` (`application_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_withdraw_type` (`withdraw_type`),
    KEY `idx_status` (`status`),
    KEY `idx_process_time` (`process_time`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 提现配置表 (新增，用于配置提现规则)
CREATE TABLE `withdraw_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `withdraw_type` varchar(20) NOT NULL COMMENT '提现类型 ALIPAY WECHAT BANK',
    `min_amount` decimal(15,2) DEFAULT 0.00 COMMENT '最小提现金额',
    `max_amount` decimal(15,2) DEFAULT 999999.99 COMMENT '最大提现金额',
    `daily_limit` decimal(15,2) DEFAULT NULL COMMENT '每日限额',
    `monthly_limit` decimal(15,2) DEFAULT NULL COMMENT '每月限额',
    `fee_type` varchar(20) DEFAULT 'FIXED' COMMENT '手续费类型 FIXED:固定 PERCENT:百分比',
    `fee_value` decimal(10,4) DEFAULT 0.0000 COMMENT '手续费值',
    `min_fee` decimal(15,2) DEFAULT 0.00 COMMENT '最小手续费',
    `max_fee` decimal(15,2) DEFAULT NULL COMMENT '最大手续费',
    `auto_approve` tinyint(1) DEFAULT 0 COMMENT '是否自动审核 0:否 1:是',
    `auto_approve_limit` decimal(15,2) DEFAULT NULL COMMENT '自动审核限额',
    `working_days_only` tinyint(1) DEFAULT 0 COMMENT '是否仅工作日处理 0:否 1:是',
    `process_start_time` time DEFAULT NULL COMMENT '处理开始时间',
    `process_end_time` time DEFAULT NULL COMMENT '处理结束时间',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_withdraw_type` (`withdraw_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表';

-- 提现交易记录表 (新增，记录所有提现相关的资金变动)
CREATE TABLE `withdraw_transaction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `application_id` bigint(20) NOT NULL COMMENT '提现申请ID',
    `transaction_type` varchar(20) NOT NULL COMMENT '交易类型 FREEZE:冻结 UNFREEZE:解冻 DEDUCT:扣减 REFUND:退款',
    `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `balance_before` decimal(15,2) NOT NULL COMMENT '交易前余额',
    `balance_after` decimal(15,2) NOT NULL COMMENT '交易后余额',
    `frozen_before` decimal(15,2) DEFAULT 0.00 COMMENT '交易前冻结金额',
    `frozen_after` decimal(15,2) DEFAULT 0.00 COMMENT '交易后冻结金额',
    `transaction_no` varchar(64) NOT NULL COMMENT '交易流水号',
    `description` varchar(255) DEFAULT NULL COMMENT '交易描述',
    `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_transaction_no` (`transaction_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_application_id` (`application_id`),
    KEY `idx_transaction_type` (`transaction_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现交易记录表';

-- =====================================================
-- 7. 产品相关表 (1张表) - 保留原有产品表结构
-- =====================================================

-- 产品表 (保留原product结构，增强多业务场景支持)
CREATE TABLE `product` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `product_name` varchar(100) NOT NULL COMMENT '产品名称',
    `product_code` varchar(50) DEFAULT NULL COMMENT '产品编码',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `product_type` varchar(20) NOT NULL COMMENT '产品类型 POINTS:塔罗币 COINS:积分 VIP:会员 TIMES:次数',
    `description` varchar(500) DEFAULT NULL COMMENT '产品描述',
    `original_price` decimal(15,2) NOT NULL COMMENT '原价',
    `sale_price` decimal(15,2) NOT NULL COMMENT '售价',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `product_value` int(11) NOT NULL COMMENT '产品价值(积分数量/次数等)',
    `bonus_value` int(11) DEFAULT 0 COMMENT '赠送价值',
    `total_value` int(11) NOT NULL COMMENT '总价值',
    `vip_days` int(11) DEFAULT NULL COMMENT 'VIP天数(VIP产品专用)',
    `product_image` varchar(255) DEFAULT NULL COMMENT '产品图片',
    `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门 0:否 1:是',
    `is_recommended` tinyint(1) DEFAULT 0 COMMENT '是否推荐 0:否 1:是',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:上架 0:下架',
    `sale_start_time` datetime DEFAULT NULL COMMENT '销售开始时间',
    `sale_end_time` datetime DEFAULT NULL COMMENT '销售结束时间',
    `stock_quantity` int(11) DEFAULT -1 COMMENT '库存数量(-1表示无限)',
    `sold_quantity` int(11) DEFAULT 0 COMMENT '已售数量',
    `limit_per_user` int(11) DEFAULT -1 COMMENT '每用户限购数量(-1表示无限)',
    `tags` varchar(255) DEFAULT NULL COMMENT '产品标签(逗号分隔)',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_product_code` (`product_code`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_product_type` (`product_type`),
    KEY `idx_currency` (`currency`),
    KEY `idx_status` (`status`),
    KEY `idx_is_hot` (`is_hot`),
    KEY `idx_is_recommended` (`is_recommended`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_sale_time` (`sale_start_time`, `sale_end_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- =====================================================
-- 8. 系统配置表 (1张表) - 保留原有系统配置
-- =====================================================

-- 系统配置表 (保留原sys_config结构，用于系统级配置)
CREATE TABLE `sys_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `config_name` varchar(100) NOT NULL COMMENT '配置名称',
    `config_key` varchar(100) NOT NULL COMMENT '配置键名',
    `config_value` varchar(5000) DEFAULT NULL COMMENT '配置键值',
    `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型 STRING:字符串 NUMBER:数字 BOOLEAN:布尔 JSON:JSON对象',
    `module` varchar(50) DEFAULT 'SYSTEM' COMMENT '所属模块 SYSTEM:系统 TAROT:塔罗 AI:AI PAYMENT:支付 WITHDRAW:提现',
    `description` varchar(500) DEFAULT NULL COMMENT '配置描述',
    `is_system` tinyint(1) DEFAULT 0 COMMENT '是否系统内置 0:否 1:是',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_config_key` (`config_key`),
    KEY `idx_config_name` (`config_name`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_module` (`module`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- =====================================================
-- 创建索引优化和约束
-- =====================================================

-- 为用户基础信息表添加外键约束
ALTER TABLE `user_base_info` ADD CONSTRAINT `fk_user_commission` FOREIGN KEY (`commission_id`) REFERENCES `commission_identity` (`id`) ON DELETE SET NULL;

-- 为用户联合登录表添加外键约束
ALTER TABLE `user_joint_login` ADD CONSTRAINT `fk_joint_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;

-- 为支付订单表添加外键约束
ALTER TABLE `pay_order` ADD CONSTRAINT `fk_pay_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;
ALTER TABLE `pay_order` ADD CONSTRAINT `fk_pay_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE SET NULL;

-- 为AI消息表添加外键约束
ALTER TABLE `ai_message` ADD CONSTRAINT `fk_ai_msg_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;
ALTER TABLE `ai_message` ADD CONSTRAINT `fk_ai_msg_room` FOREIGN KEY (`room_id`) REFERENCES `ai_room` (`id`) ON DELETE CASCADE;
ALTER TABLE `ai_message` ADD CONSTRAINT `fk_ai_msg_agent` FOREIGN KEY (`agent_id`) REFERENCES `ai_agent` (`id`) ON DELETE SET NULL;

-- 为AI房间表添加外键约束
ALTER TABLE `ai_room` ADD CONSTRAINT `fk_ai_room_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;

-- 为AI路由表添加外键约束
ALTER TABLE `ai_router` ADD CONSTRAINT `fk_ai_router_model` FOREIGN KEY (`model_id`) REFERENCES `ai_model` (`id`) ON DELETE CASCADE;
ALTER TABLE `ai_router` ADD CONSTRAINT `fk_ai_router_config` FOREIGN KEY (`config_id`) REFERENCES `ai_router_config` (`id`) ON DELETE CASCADE;

-- 为塔罗解读记录表添加外键约束
ALTER TABLE `tarot_reading_record` ADD CONSTRAINT `fk_tarot_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;
ALTER TABLE `tarot_reading_record` ADD CONSTRAINT `fk_tarot_spread` FOREIGN KEY (`spread_id`) REFERENCES `tarot_spread` (`id`) ON DELETE SET NULL;

-- 为提现申请表添加外键约束
ALTER TABLE `withdraw_application` ADD CONSTRAINT `fk_withdraw_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;

-- 为提现交易记录表添加外键约束
ALTER TABLE `withdraw_transaction` ADD CONSTRAINT `fk_withdraw_trans_user` FOREIGN KEY (`user_id`) REFERENCES `user_base_info` (`id`) ON DELETE CASCADE;
ALTER TABLE `withdraw_transaction` ADD CONSTRAINT `fk_withdraw_trans_app` FOREIGN KEY (`application_id`) REFERENCES `withdraw_application` (`id`) ON DELETE CASCADE;
