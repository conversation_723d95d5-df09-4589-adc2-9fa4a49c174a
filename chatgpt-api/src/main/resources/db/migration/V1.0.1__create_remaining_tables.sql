-- =====================================================
-- 超级智能社重构数据库表结构创建脚本 - 剩余表
-- 作者: wuqm
-- 时间: 2025-01-12
-- 版本: V1.0.1
-- 说明: 创建塔罗牌、系统功能、提现功能等剩余表结构
-- =====================================================

-- =====================================================
-- 4. 塔罗牌相关表 (3张表) - 保留原有结构
-- =====================================================

-- 塔罗牌阵表 (保留原tarot_spread结构)
CREATE TABLE `tarot_spread` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) DEFAULT NULL COMMENT '牌阵名称',
    `description` varchar(500) DEFAULT NULL COMMENT '牌阵描述',
    `card_count` int(11) DEFAULT NULL COMMENT '牌数',
    `image_url` varchar(255) DEFAULT NULL COMMENT '牌阵图片',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗牌阵表';

-- 塔罗牌义表 (保留原tarot_card_meaning结构)
CREATE TABLE `tarot_card_meaning` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) DEFAULT NULL COMMENT '塔罗牌名称',
    `meaning` text COMMENT '牌的基本含义',
    `guidance_text` text COMMENT '指引文字',
    `advice` text COMMENT '正位建议',
    `discouraged` text COMMENT '逆位警示',
    `card_front_url` varchar(255) DEFAULT NULL COMMENT '塔罗牌正面图片',
    `card_back_url` varchar(255) DEFAULT NULL COMMENT '塔罗牌背面图片',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗牌义表';

-- 塔罗解读记录表 (保留原tarot_reading_record结构)
CREATE TABLE `tarot_reading_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `spread_id` bigint(20) DEFAULT NULL COMMENT '牌阵ID',
    `question` varchar(500) DEFAULT NULL COMMENT '问题',
    `draw_result` text COMMENT '抽牌结果(JSON格式)',
    `answer` text COMMENT 'AI解读结果',
    `interpretation_mode` int(2) DEFAULT 0 COMMENT '解读模式 0:抽牌 1:自选',
    `status` int(2) DEFAULT 0 COMMENT '解读状态 0:未回答 1:已回答',
    `open_id` varchar(64) DEFAULT NULL COMMENT '微信openId',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_spread_id` (`spread_id`),
    KEY `idx_open_id` (`open_id`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗解读记录表';

-- =====================================================
-- 5. 系统功能相关表 (8张表)
-- =====================================================

-- 统一国际化表 (合并tarot_i18n，支持所有模块的国际化)
CREATE TABLE `i18n_message` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `lang` varchar(20) NOT NULL COMMENT '语言代码 zh_CN en_US vi_VN',
    `code` text NOT NULL COMMENT '编码，同一段文字的不同语言翻译的code必须相同',
    `value` text NOT NULL COMMENT '翻译值',
    `module` varchar(50) DEFAULT 'COMMON' COMMENT '模块 COMMON:通用 TAROT:塔罗 AI:AI CHAT:对话',
    `category` varchar(50) DEFAULT NULL COMMENT '分类',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_lang_code` (`lang`, `code`(100)),
    KEY `idx_module` (`module`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一国际化表';

-- 用户登录历史表 (基于sys_logininfor增强)
CREATE TABLE `user_login_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `login_account` varchar(100) DEFAULT NULL COMMENT '登录账号',
    `login_type` varchar(20) DEFAULT NULL COMMENT '登录类型 WECHAT:微信 PHONE:手机 EMAIL:邮箱',
    `joint_login_id` bigint(20) DEFAULT NULL COMMENT '联合登录ID',
    `client_type` varchar(20) DEFAULT NULL COMMENT '客户端类型 WEB:网页 MOBILE:手机 APP:应用',
    `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
    `device` varchar(100) DEFAULT NULL COMMENT '设备信息',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `location` varchar(255) DEFAULT NULL COMMENT '登录地点',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '退出时间',
    `session_duration` int(11) DEFAULT NULL COMMENT '会话时长(秒)',
    `status` varchar(10) DEFAULT 'SUCCESS' COMMENT '登录状态 SUCCESS:成功 FAILED:失败',
    `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_type` (`login_type`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录历史表';

-- 广告位管理表 (原promotion_info增强，使用adv缩写)
CREATE TABLE `adv_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title` varchar(100) NOT NULL COMMENT '广告标题',
    `description` varchar(500) DEFAULT NULL COMMENT '广告描述',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `adv_type` varchar(50) NOT NULL COMMENT '广告类型 MOBILE_BANNER:移动端横幅 HOME_POSTER:首页海报 POPUP:弹窗广告',
    `adv_image_url` varchar(200) DEFAULT NULL COMMENT '广告图片URL',
    `logo_url` varchar(200) DEFAULT NULL COMMENT 'LOGO图标URL',
    `h5_url` varchar(200) DEFAULT NULL COMMENT 'H5活动页面URL',
    `detail_url` varchar(200) DEFAULT NULL COMMENT '广告详情链接',
    `related_product_id` int(11) DEFAULT NULL COMMENT '关联产品ID',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `status` int(2) DEFAULT 0 COMMENT '状态 0:启用 1:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `click_count` int(11) DEFAULT 0 COMMENT '点击次数',
    `view_count` int(11) DEFAULT 0 COMMENT '展示次数',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_adv_type` (`adv_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告位管理表';

-- 充值记录表 (原recharge_log增强业务场景)
CREATE TABLE `recharge_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `order_no` varchar(64) DEFAULT NULL COMMENT '关联订单号',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `recharge_type` varchar(20) DEFAULT NULL COMMENT '充值类型 POINTS:塔罗币 COINS:积分 VIP:会员',
    `amount` decimal(15,2) NOT NULL COMMENT '充值金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `recharge_count` int(11) DEFAULT NULL COMMENT '充值数量',
    `bonus_count` int(11) DEFAULT 0 COMMENT '赠送数量',
    `total_count` int(11) DEFAULT NULL COMMENT '总数量',
    `pay_channel` varchar(20) DEFAULT NULL COMMENT '支付渠道',
    `status` int(2) DEFAULT 0 COMMENT '状态 0:待处理 1:成功 2:失败',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- AI敏感词表 (原sensitive_word，归入AI模块)
CREATE TABLE `ai_sensitive_word` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `word` varchar(255) NOT NULL COMMENT '敏感词内容',
    `word_type` varchar(20) DEFAULT 'COMMON' COMMENT '敏感词类型 COMMON:通用 POLITICAL:政治 VIOLENCE:暴力 SEXUAL:色情',
    `ability_type` varchar(20) DEFAULT 'ALL' COMMENT '适用能力类型 ALL:全部 CHAT:对话 DRAW:绘画 WRITE:写作',
    `filter_level` int(2) DEFAULT 1 COMMENT '过滤级别 1:低 2:中 3:高',
    `replacement` varchar(255) DEFAULT '***' COMMENT '替换内容',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 2:停用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    KEY `idx_word` (`word`),
    KEY `idx_word_type` (`word_type`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI敏感词表';

-- 分享信息表 (原share_info，分析后确定为分享链接管理)
CREATE TABLE `share_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '分享用户ID',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `share_type` varchar(20) NOT NULL COMMENT '分享类型 READING:解读结果 CHAT:对话内容 DRAW:绘画作品',
    `content_id` bigint(20) DEFAULT NULL COMMENT '内容ID',
    `share_title` varchar(255) DEFAULT NULL COMMENT '分享标题',
    `share_description` varchar(500) DEFAULT NULL COMMENT '分享描述',
    `share_image` varchar(255) DEFAULT NULL COMMENT '分享图片',
    `share_url` varchar(500) DEFAULT NULL COMMENT '分享链接',
    `share_code` varchar(32) DEFAULT NULL COMMENT '分享码',
    `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:有效 0:失效',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_share_code` (`share_code`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_share_type` (`share_type`),
    KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享信息表';

-- 首页配置表 (保留原home_config，用于首页内容配置)
CREATE TABLE `home_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `config_type` varchar(50) NOT NULL COMMENT '配置类型 BANNER:轮播图 NOTICE:公告 FEATURE:功能推荐',
    `title` varchar(255) DEFAULT NULL COMMENT '标题',
    `content` text COMMENT '内容',
    `image_url` varchar(255) DEFAULT NULL COMMENT '图片URL',
    `link_url` varchar(255) DEFAULT NULL COMMENT '链接URL',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页配置表';

-- 异常日志表 (保留原exception_log，用于系统异常记录)
CREATE TABLE `exception_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
    `method` varchar(100) DEFAULT NULL COMMENT '方法名称',
    `exception_type` varchar(100) DEFAULT NULL COMMENT '异常类型',
    `exception_message` text COMMENT '异常信息',
    `stack_trace` longtext COMMENT '异常堆栈',
    `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
    `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `request_params` text COMMENT '请求参数',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `status` int(2) DEFAULT 0 COMMENT '处理状态 0:未处理 1:已处理',
    `handle_user` varchar(50) DEFAULT NULL COMMENT '处理人',
    `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
    `handle_remark` varchar(255) DEFAULT NULL COMMENT '处理备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_module` (`module`),
    KEY `idx_exception_type` (`exception_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常日志表';
