# 超级智能社重构后配置文件
# 作者: wuqm
# 时间: 2025-01-12
# 说明: 重构后的完整配置，支持多语言、多币种、统一支付、统一登录等功能

server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: chatgpt-api-refactor
  
  # 环境配置
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************
    username: root
    password: 123456
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.chatgpt.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.chatgpt: DEBUG
    org.springframework: INFO
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/chatgpt-api.log
    max-size: 100MB
    max-history: 30

# JustAuth第三方登录配置
justauth:
  wechat:
    client-id: wxfe365e97c4eb6f9f
    client-secret: e494427b392b2e1f5adfbe61e828dbd9
    redirect-uri: http://localhost:8080/api/auth/oauth/wechat/callback
  google:
    client-id: your_google_client_id
    client-secret: your_google_client_secret
    redirect-uri: http://localhost:8080/api/auth/oauth/google/callback
  facebook:
    client-id: your_facebook_client_id
    client-secret: your_facebook_client_secret
    redirect-uri: http://localhost:8080/api/auth/oauth/facebook/callback

# 统一支付配置
pay:
  alipay:
    app-id: your_alipay_app_id
    private-key: your_alipay_private_key
    public-key: your_alipay_public_key
    notify-url: http://localhost:8080/api/pay/alipay/notify
    return-url: http://localhost:8080/api/pay/alipay/return
    sign-type: RSA2
    sandbox: false
  wechat:
    app-id: wx734d3d8a6888e18f
    mch-id: 1683747767
    key: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy
    notify-url: http://localhost:8080/api/pay/wechat/notify
    return-url: http://localhost:8080/api/pay/wechat/return
    sandbox: false

# 缓存配置
cache:
  caffeine:
    maximum-size: 10000
    expire-after-write: 300
  redis:
    default-ttl: 3600
    null-values: false

# AI配置
ai:
  openai:
    api-key: your_openai_api_key
    base-url: https://api.openai.com
    timeout: 30000
  claude:
    api-key: your_claude_api_key
    base-url: https://api.anthropic.com
    timeout: 30000

# 短信配置
sms:
  provider: aliyun
  aliyun:
    access-key-id: your_access_key_id
    access-key-secret: your_access_key_secret
    sign-name: 超级智能社
    template-code: SMS_123456789

# 文件存储配置
file:
  storage:
    type: local
    local:
      path: ./uploads
    oss:
      endpoint: your_oss_endpoint
      access-key-id: your_access_key_id
      access-key-secret: your_access_key_secret
      bucket-name: your_bucket_name

# 系统配置
system:
  name: 超级智能社
  version: 2.0.0
  author: wuqm
  description: 基于AI的多功能智能服务平台
  
# Swagger配置
swagger:
  enabled: true
  title: 超级智能社API文档
  description: 超级智能社重构后的API接口文档
  version: 2.0.0
  contact:
    name: wuqm
    email: <EMAIL>
  base-package: com.chatgpt.controller

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized

# Sa-Token配置
sa-token:
  token-name: satoken
  timeout: 2592000
  activity-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

# 业务场景配置
business:
  scenes:
    - code: tarot
      name: 塔罗
      description: 塔罗牌占卜解读
      enabled: true
    - code: zns
      name: 智能社
      description: AI智能对话
      enabled: true
    - code: chatoi
      name: 对话
      description: AI对话服务
      enabled: true

# 支持的语言配置
languages:
  default: zh_CN
  supported:
    - code: zh_CN
      name: 中文简体
      enabled: true
    - code: zh_TW
      name: 中文繁体
      enabled: true
    - code: en_US
      name: English
      enabled: true
    - code: vi_VN
      name: Tiếng Việt
      enabled: true
    - code: ja_JP
      name: 日本語
      enabled: false
    - code: ko_KR
      name: 한국어
      enabled: false

# 支持的币种配置
currencies:
  default: CNY
  supported:
    - code: CNY
      name: 人民币
      symbol: ¥
      enabled: true
    - code: USD
      name: 美元
      symbol: $
      enabled: true
    - code: VND
      name: 越南盾
      symbol: ₫
      enabled: true
