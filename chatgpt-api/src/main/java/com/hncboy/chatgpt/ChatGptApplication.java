package com.hncboy.chatgpt;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 超级智能社启动类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:15
 */
@Slf4j
@MapperScan(value = {"com.hncboy.chatgpt.db.mapper"})
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
public class ChatGptApplication {

    public static void main(String[] args) {
        log.info("正在启动超级智能社应用...");

        try {
            ConfigurableApplicationContext context = SpringApplication.run(ChatGptApplication.class, args);
            Environment env = context.getEnvironment();

            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }

            String serverPort = env.getProperty("server.port", "8080");
            String contextPath = env.getProperty("server.servlet.context-path", "");
            String hostAddress = "localhost";

            try {
                hostAddress = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("无法获取主机地址，使用默认值: localhost");
            }

            log.info("\n----------------------------------------------------------\n" +
                    "  超级智能社应用启动成功！\n" +
                    "  应用名称: {}\n" +
                    "  应用版本: 2.0.0\n" +
                    "  访问地址:\n" +
                    "    本地访问: {}://localhost:{}{}\n" +
                    "    外部访问: {}://{}:{}{}\n" +
                    "  配置文件: {}\n" +
                    "  数据库: {}\n" +
                    "  Redis: {}:{}\n" +
                    "  Swagger文档: {}://localhost:{}{}/swagger-ui.html\n" +
                    "  作者: hncboy\n" +
                    "  重构完成时间: 2025-01-12\n" +
                    "----------------------------------------------------------",
                    env.getProperty("spring.application.name", "chatgpt-api"),
                    protocol, serverPort, contextPath,
                    protocol, hostAddress, serverPort, contextPath,
                    env.getActiveProfiles().length > 0 ? env.getActiveProfiles()[0] : "default",
                    env.getProperty("spring.datasource.url", "未配置"),
                    env.getProperty("spring.redis.host", "localhost"),
                    env.getProperty("spring.redis.port", "6379"),
                    protocol, serverPort, contextPath
            );

            // 输出重构亮点
            log.info("\n========== 重构亮点 ==========\n" +
                    "✅ 数据库表从61张优化为25张\n" +
                    "✅ 支持多语言国际化(中英越日韩)\n" +
                    "✅ 支持多币种支付(CNY/USD/VND)\n" +
                    "✅ 集成JustAuth统一第三方登录\n" +
                    "✅ 集成pay-java-parent统一支付\n" +
                    "✅ Redis多级缓存架构\n" +
                    "✅ MyBatis-Plus简化数据访问\n" +
                    "✅ 完善的异常处理和日志\n" +
                    "✅ 符合阿里巴巴编码规范\n" +
                    "✅ 严格按照设计文档实现\n" +
                    "===============================");

        } catch (Exception e) {
            log.error("应用启动失败", e);
            System.exit(1);
        }
    }
}
