package com.hncboy.chatgpt.common.exception;

import com.hncboy.chatgpt.common.enums.ResponseEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础异常类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:00
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseException extends RuntimeException {

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 构造方法
     */
    public BaseException() {
        super();
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     */
    public BaseException(String message) {
        super(message);
        this.code = ResponseEnum.ERROR.getCode();
        this.message = message;
    }

    /**
     * 构造方法
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    /**
     * 构造方法
     *
     * @param responseEnum 响应枚举
     */
    public BaseException(ResponseEnum responseEnum) {
        super(responseEnum.getMessage());
        this.code = responseEnum.getCode();
        this.message = responseEnum.getMessage();
    }

    /**
     * 构造方法
     *
     * @param responseEnum 响应枚举
     * @param cause        异常原因
     */
    public BaseException(ResponseEnum responseEnum, Throwable cause) {
        super(responseEnum.getMessage(), cause);
        this.code = responseEnum.getCode();
        this.message = responseEnum.getMessage();
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     * @param cause   异常原因
     */
    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResponseEnum.ERROR.getCode();
        this.message = message;
    }

    /**
     * 构造方法
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   异常原因
     */
    public BaseException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
}
