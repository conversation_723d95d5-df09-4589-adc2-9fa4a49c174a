package com.hncboy.chatgpt.common.util;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Bean工具类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:40
 */
public class BeanUtil {

    /**
     * 复制属性
     *
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        BeanUtils.copyProperties(source, target);
    }

    /**
     * 复制属性，忽略空值
     *
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyPropertiesIgnoreNull(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
    }

    /**
     * 复制属性到新对象
     *
     * @param source      源对象
     * @param targetClass 目标类型
     * @param <T>         泛型
     * @return 新对象
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("复制属性失败", e);
        }
    }

    /**
     * 复制属性到新对象，忽略空值
     *
     * @param source      源对象
     * @param targetClass 目标类型
     * @param <T>         泛型
     * @return 新对象
     */
    public static <T> T copyPropertiesIgnoreNull(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target, getNullPropertyNames(source));
            return target;
        } catch (Exception e) {
            throw new RuntimeException("复制属性失败", e);
        }
    }

    /**
     * 复制列表
     *
     * @param sourceList  源列表
     * @param targetClass 目标类型
     * @param <T>         泛型
     * @return 新列表
     */
    public static <T> List<T> copyList(List<?> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        return sourceList.stream()
                .map(source -> copyProperties(source, targetClass))
                .collect(Collectors.toList());
    }

    /**
     * 复制列表，忽略空值
     *
     * @param sourceList  源列表
     * @param targetClass 目标类型
     * @param <T>         泛型
     * @return 新列表
     */
    public static <T> List<T> copyListIgnoreNull(List<?> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }
        return sourceList.stream()
                .map(source -> copyPropertiesIgnoreNull(source, targetClass))
                .collect(Collectors.toList());
    }

    /**
     * 获取空属性名称数组
     *
     * @param source 源对象
     * @return 空属性名称数组
     */
    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    /**
     * 对象转Map
     *
     * @param obj 对象
     * @return Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }
        
        Map<String, Object> map = new HashMap<>();
        BeanWrapper beanWrapper = new BeanWrapperImpl(obj);
        PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();
        
        for (PropertyDescriptor property : propertyDescriptors) {
            String key = property.getName();
            if (!"class".equals(key)) {
                Object value = beanWrapper.getPropertyValue(key);
                map.put(key, value);
            }
        }
        return map;
    }

    /**
     * Map转对象
     *
     * @param map         Map
     * @param targetClass 目标类型
     * @param <T>         泛型
     * @return 对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> targetClass) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        
        try {
            T target = targetClass.newInstance();
            BeanWrapper beanWrapper = new BeanWrapperImpl(target);
            
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (beanWrapper.isWritableProperty(key)) {
                    beanWrapper.setPropertyValue(key, value);
                }
            }
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Map转对象失败", e);
        }
    }

    /**
     * 判断对象是否为空
     *
     * @param obj 对象
     * @return 是否为空
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        
        BeanWrapper beanWrapper = new BeanWrapperImpl(obj);
        PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();
        
        for (PropertyDescriptor property : propertyDescriptors) {
            String key = property.getName();
            if (!"class".equals(key)) {
                Object value = beanWrapper.getPropertyValue(key);
                if (value != null) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 判断对象是否不为空
     *
     * @param obj 对象
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }
}
