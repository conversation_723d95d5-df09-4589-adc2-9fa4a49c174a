package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 语言枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 10:05
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum {

    /**
     * 中文简体
     */
    ZH_CN("zh_CN", "中文简体", "Chinese Simplified"),

    /**
     * 中文繁体
     */
    ZH_TW("zh_TW", "中文繁體", "Chinese Traditional"),

    /**
     * 英语
     */
    EN_US("en_US", "English", "English"),

    /**
     * 越南语
     */
    VI_VN("vi_VN", "Tiếng Việt", "Vietnamese"),

    /**
     * 日语
     */
    JA_JP("ja_JP", "日本語", "Japanese"),

    /**
     * 韩语
     */
    KO_KR("ko_KR", "한국어", "Korean");

    /**
     * 语言编码
     */
    private final String code;

    /**
     * 本地名称
     */
    private final String localName;

    /**
     * 英文名称
     */
    private final String englishName;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 语言枚举
     */
    public static LanguageEnum getByCode(String code) {
        for (LanguageEnum languageEnum : values()) {
            if (languageEnum.getCode().equals(code)) {
                return languageEnum;
            }
        }
        return ZH_CN;
    }

    /**
     * 是否为中文
     *
     * @return 是否为中文
     */
    public boolean isChinese() {
        return this == ZH_CN || this == ZH_TW;
    }

    /**
     * 是否为英文
     *
     * @return 是否为英文
     */
    public boolean isEnglish() {
        return this == EN_US;
    }

    /**
     * 是否为亚洲语言
     *
     * @return 是否为亚洲语言
     */
    public boolean isAsianLanguage() {
        return this == ZH_CN || this == ZH_TW || this == JA_JP || this == KO_KR || this == VI_VN;
    }
}
