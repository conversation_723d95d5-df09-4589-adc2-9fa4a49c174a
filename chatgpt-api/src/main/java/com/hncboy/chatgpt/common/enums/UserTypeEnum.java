package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:35
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum {

    /**
     * 智能社
     */
    ZNS("zns", "智能社"),

    /**
     * 塔罗牌
     */
    TAROT("tarot", "塔罗牌"),

    /**
     * AI对话
     */
    CHATOI("chatoi", "AI对话");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 用户类型枚举
     */
    public static UserTypeEnum getByCode(String code) {
        for (UserTypeEnum userTypeEnum : values()) {
            if (userTypeEnum.getCode().equals(code)) {
                return userTypeEnum;
            }
        }
        return ZNS;
    }

    /**
     * 是否为有效的用户类型
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        for (UserTypeEnum userTypeEnum : values()) {
            if (userTypeEnum.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
