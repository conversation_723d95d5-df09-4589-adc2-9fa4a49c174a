package com.hncboy.chatgpt.common.exception;

import com.hncboy.chatgpt.common.enums.ResponseEnum;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:05
 */
public class BusinessException extends BaseException {

    /**
     * 构造方法
     */
    public BusinessException() {
        super();
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
    }

    /**
     * 构造方法
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BusinessException(Integer code, String message) {
        super(code, message);
    }

    /**
     * 构造方法
     *
     * @param responseEnum 响应枚举
     */
    public BusinessException(ResponseEnum responseEnum) {
        super(responseEnum);
    }

    /**
     * 构造方法
     *
     * @param responseEnum 响应枚举
     * @param cause        异常原因
     */
    public BusinessException(ResponseEnum responseEnum, Throwable cause) {
        super(responseEnum, cause);
    }

    /**
     * 构造方法
     *
     * @param message 错误消息
     * @param cause   异常原因
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 构造方法
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   异常原因
     */
    public BusinessException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
