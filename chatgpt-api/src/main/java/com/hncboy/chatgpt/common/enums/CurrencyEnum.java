package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 币种枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 10:10
 */
@Getter
@AllArgsConstructor
public enum CurrencyEnum {

    /**
     * 人民币
     */
    CNY("CNY", "人民币", "¥", 2),

    /**
     * 美元
     */
    USD("USD", "美元", "$", 2),

    /**
     * 越南盾
     */
    VND("VND", "越南盾", "₫", 0),

    /**
     * 欧元
     */
    EUR("EUR", "欧元", "€", 2),

    /**
     * 日元
     */
    JPY("JPY", "日元", "¥", 0),

    /**
     * 韩元
     */
    KRW("KRW", "韩元", "₩", 0);

    /**
     * 币种编码
     */
    private final String code;

    /**
     * 币种名称
     */
    private final String name;

    /**
     * 币种符号
     */
    private final String symbol;

    /**
     * 小数位数
     */
    private final Integer decimalPlaces;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 币种枚举
     */
    public static CurrencyEnum getByCode(String code) {
        for (CurrencyEnum currencyEnum : values()) {
            if (currencyEnum.getCode().equals(code)) {
                return currencyEnum;
            }
        }
        return CNY;
    }

    /**
     * 是否为主要币种
     *
     * @return 是否为主要币种
     */
    public boolean isMajorCurrency() {
        return this == CNY || this == USD || this == EUR;
    }

    /**
     * 是否为亚洲币种
     *
     * @return 是否为亚洲币种
     */
    public boolean isAsianCurrency() {
        return this == CNY || this == VND || this == JPY || this == KRW;
    }

    /**
     * 是否有小数位
     *
     * @return 是否有小数位
     */
    public boolean hasDecimalPlaces() {
        return decimalPlaces > 0;
    }
}
