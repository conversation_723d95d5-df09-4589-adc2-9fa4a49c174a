package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 提现状态枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:55
 */
@Getter
@AllArgsConstructor
public enum WithdrawStatusEnum {

    /**
     * 待审核
     */
    PENDING("PENDING", "待审核"),

    /**
     * 审核通过
     */
    APPROVED("APPROVED", "审核通过"),

    /**
     * 审核拒绝
     */
    REJECTED("REJECTED", "审核拒绝"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 提现状态枚举
     */
    public static WithdrawStatusEnum getByCode(String code) {
        for (WithdrawStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 是否为终态
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == FAILED;
    }

    /**
     * 是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == APPROVED;
    }
}
