package com.hncboy.chatgpt.common.constant;

/**
 * 应用常量
 *
 * <AUTHOR>
 * @date 2023/3/22 09:20
 */
public class ApplicationConstant {

    /**
     * 应用名称
     */
    public static final String APPLICATION_NAME = "chatgpt-api";

    /**
     * 应用版本
     */
    public static final String APPLICATION_VERSION = "2.0.0";

    /**
     * 应用描述
     */
    public static final String APPLICATION_DESCRIPTION = "超级智能社 - 基于AI的多功能智能服务平台";

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大页大小
     */
    public static final int MAX_PAGE_SIZE = 100;

    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;

    /**
     * 失败状态码
     */
    public static final int ERROR_CODE = 500;

    /**
     * 未授权状态码
     */
    public static final int UNAUTHORIZED_CODE = 401;

    /**
     * 禁止访问状态码
     */
    public static final int FORBIDDEN_CODE = 403;

    /**
     * 资源不存在状态码
     */
    public static final int NOT_FOUND_CODE = 404;

    /**
     * 参数错误状态码
     */
    public static final int BAD_REQUEST_CODE = 400;

    /**
     * 默认语言
     */
    public static final String DEFAULT_LANGUAGE = "zh_CN";

    /**
     * 默认币种
     */
    public static final String DEFAULT_CURRENCY = "CNY";

    /**
     * 默认时区
     */
    public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

    /**
     * 系统用户ID
     */
    public static final Integer SYSTEM_USER_ID = 0;

    /**
     * 系统用户名
     */
    public static final String SYSTEM_USER_NAME = "system";

    /**
     * 匿名用户ID
     */
    public static final Integer ANONYMOUS_USER_ID = -1;

    /**
     * 匿名用户名
     */
    public static final String ANONYMOUS_USER_NAME = "anonymous";

    /**
     * 默认头像
     */
    public static final String DEFAULT_AVATAR = "/static/images/default-avatar.png";

    /**
     * 业务场景
     */
    public static class BizScene {
        /**
         * 塔罗牌
         */
        public static final String TAROT = "tarot";

        /**
         * 智能社
         */
        public static final String ZNS = "zns";

        /**
         * AI对话
         */
        public static final String CHATOI = "chatoi";
    }

    /**
     * 用户状态
     */
    public static class UserStatus {
        /**
         * 正常
         */
        public static final Integer NORMAL = 0;

        /**
         * 禁用
         */
        public static final Integer DISABLED = 1;

        /**
         * 注销
         */
        public static final Integer DELETED = 2;
    }

    /**
     * 删除标识
     */
    public static class DeleteFlag {
        /**
         * 未删除
         */
        public static final Integer NOT_DELETED = 0;

        /**
         * 已删除
         */
        public static final Integer DELETED = 1;
    }

    /**
     * 启用状态
     */
    public static class EnableStatus {
        /**
         * 启用
         */
        public static final Integer ENABLED = 1;

        /**
         * 禁用
         */
        public static final Integer DISABLED = 0;
    }

    /**
     * 性别
     */
    public static class Gender {
        /**
         * 未知
         */
        public static final Integer UNKNOWN = 0;

        /**
         * 男
         */
        public static final Integer MALE = 1;

        /**
         * 女
         */
        public static final Integer FEMALE = 2;
    }

    /**
     * 是否标识
     */
    public static class YesNo {
        /**
         * 否
         */
        public static final String NO = "0";

        /**
         * 是
         */
        public static final String YES = "1";
    }

    /**
     * 文件上传相关
     */
    public static class Upload {
        /**
         * 最大文件大小 10MB
         */
        public static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

        /**
         * 允许的图片格式
         */
        public static final String[] ALLOWED_IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

        /**
         * 允许的文档格式
         */
        public static final String[] ALLOWED_DOCUMENT_EXTENSIONS = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};

        /**
         * 允许的音频格式
         */
        public static final String[] ALLOWED_AUDIO_EXTENSIONS = {"mp3", "wav", "flac", "aac", "ogg"};

        /**
         * 允许的视频格式
         */
        public static final String[] ALLOWED_VIDEO_EXTENSIONS = {"mp4", "avi", "mov", "wmv", "flv", "mkv"};
    }

    /**
     * 缓存相关
     */
    public static class Cache {
        /**
         * 默认过期时间（秒）
         */
        public static final long DEFAULT_EXPIRE_TIME = 3600;

        /**
         * 短期过期时间（秒）
         */
        public static final long SHORT_EXPIRE_TIME = 300;

        /**
         * 长期过期时间（秒）
         */
        public static final long LONG_EXPIRE_TIME = 86400;

        /**
         * 永不过期
         */
        public static final long NEVER_EXPIRE = -1;
    }

    /**
     * 正则表达式
     */
    public static class Regex {
        /**
         * 手机号正则
         */
        public static final String PHONE = "^1[3-9]\\d{9}$";

        /**
         * 邮箱正则
         */
        public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

        /**
         * 身份证号正则
         */
        public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

        /**
         * 密码正则（8-20位，包含字母和数字）
         */
        public static final String PASSWORD = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*#?&]{8,20}$";

        /**
         * 用户名正则（4-20位，字母、数字、下划线）
         */
        public static final String USERNAME = "^[a-zA-Z0-9_]{4,20}$";

        /**
         * 昵称正则（2-20位，中文、字母、数字）
         */
        public static final String NICKNAME = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,20}$";
    }
}
