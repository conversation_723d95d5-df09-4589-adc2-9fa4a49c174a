package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:45
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    /**
     * 支付宝
     */
    ALIPAY("ALIPAY", "支付宝"),

    /**
     * 微信支付
     */
    WECHAT("WECHAT", "微信支付"),

    /**
     * 越南momo
     */
    MOMO("MOMO", "越南momo"),

    /**
     * 越南sepay
     */
    SEPAY("SEPAY", "越南sepay");

    /**
     * 渠道编码
     */
    private final String code;

    /**
     * 渠道名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 支付渠道枚举
     */
    public static PayChannelEnum getByCode(String code) {
        for (PayChannelEnum payChannelEnum : values()) {
            if (payChannelEnum.getCode().equals(code)) {
                return payChannelEnum;
            }
        }
        return null;
    }

    /**
     * 是否为国内支付渠道
     *
     * @return 是否为国内支付渠道
     */
    public boolean isDomestic() {
        return this == ALIPAY || this == WECHAT;
    }

    /**
     * 是否为国外支付渠道
     *
     * @return 是否为国外支付渠道
     */
    public boolean isForeign() {
        return this == MOMO || this == SEPAY;
    }
}
