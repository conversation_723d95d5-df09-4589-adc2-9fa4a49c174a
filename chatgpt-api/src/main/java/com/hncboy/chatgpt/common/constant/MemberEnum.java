package com.hncboy.chatgpt.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会员枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:20
 */
@Getter
@AllArgsConstructor
public enum MemberEnum {

    /**
     * 普通用户
     */
    NORMAL(0, "普通用户"),

    /**
     * VIP用户
     */
    VIP(1, "VIP用户"),

    /**
     * SVIP用户
     */
    SVIP(2, "SVIP用户"),

    /**
     * 永久VIP
     */
    PERMANENT_VIP(3, "永久VIP");

    /**
     * 会员类型
     */
    private final Integer type;

    /**
     * 会员名称
     */
    private final String name;

    /**
     * 根据类型获取会员枚举
     *
     * @param type 会员类型
     * @return 会员枚举
     */
    public static MemberEnum getByType(Integer type) {
        for (MemberEnum memberEnum : values()) {
            if (memberEnum.getType().equals(type)) {
                return memberEnum;
            }
        }
        return NORMAL;
    }

    /**
     * 是否为VIP用户
     *
     * @param type 会员类型
     * @return 是否为VIP用户
     */
    public static boolean isVip(Integer type) {
        return type != null && type > 0;
    }

    /**
     * 是否为SVIP用户
     *
     * @param type 会员类型
     * @return 是否为SVIP用户
     */
    public static boolean isSvip(Integer type) {
        return type != null && type >= 2;
    }
}
