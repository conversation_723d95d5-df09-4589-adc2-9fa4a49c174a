package com.hncboy.chatgpt.common.constant;

/**
 * Redis常量
 *
 * <AUTHOR>
 * @date 2023/3/22 09:25
 */
public class RedisConstant {

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    /**
     * 应用前缀
     */
    private static final String APP_PREFIX = "chatgpt";

    /**
     * 用户相关
     */
    public static class User {
        /**
         * 用户信息缓存前缀
         */
        public static final String USER_INFO = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "info" + SEPARATOR;

        /**
         * 用户登录信息缓存前缀
         */
        public static final String USER_LOGIN = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "login" + SEPARATOR;

        /**
         * 用户权限缓存前缀
         */
        public static final String USER_PERMISSION = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "permission" + SEPARATOR;

        /**
         * 用户在线状态前缀
         */
        public static final String USER_ONLINE = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "online" + SEPARATOR;

        /**
         * 用户签到记录前缀
         */
        public static final String USER_CHECKIN = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "checkin" + SEPARATOR;

        /**
         * 用户积分缓存前缀
         */
        public static final String USER_POINTS = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "points" + SEPARATOR;

        /**
         * 用户VIP信息前缀
         */
        public static final String USER_VIP = APP_PREFIX + SEPARATOR + "user" + SEPARATOR + "vip" + SEPARATOR;
    }

    /**
     * 认证相关
     */
    public static class Auth {
        /**
         * 短信验证码前缀
         */
        public static final String SMS_CODE = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "sms" + SEPARATOR;

        /**
         * 邮箱验证码前缀
         */
        public static final String EMAIL_CODE = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "email" + SEPARATOR;

        /**
         * 登录失败次数前缀
         */
        public static final String LOGIN_FAIL = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "fail" + SEPARATOR;

        /**
         * 第三方登录状态前缀
         */
        public static final String OAUTH_STATE = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "oauth" + SEPARATOR;

        /**
         * 刷新令牌前缀
         */
        public static final String REFRESH_TOKEN = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "refresh" + SEPARATOR;

        /**
         * 访问令牌前缀
         */
        public static final String ACCESS_TOKEN = APP_PREFIX + SEPARATOR + "auth" + SEPARATOR + "access" + SEPARATOR;
    }

    /**
     * AI相关
     */
    public static class Ai {
        /**
         * AI对话缓存前缀
         */
        public static final String CHAT_CONTEXT = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "chat" + SEPARATOR;

        /**
         * AI模型配置前缀
         */
        public static final String MODEL_CONFIG = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "model" + SEPARATOR;

        /**
         * AI智能体配置前缀
         */
        public static final String AGENT_CONFIG = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "agent" + SEPARATOR;

        /**
         * AI提示词缓存前缀
         */
        public static final String PROMPT_CACHE = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "prompt" + SEPARATOR;

        /**
         * AI绘画任务前缀
         */
        public static final String DRAW_TASK = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "draw" + SEPARATOR;

        /**
         * AI写作任务前缀
         */
        public static final String WRITE_TASK = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "write" + SEPARATOR;

        /**
         * AI音乐任务前缀
         */
        public static final String MUSIC_TASK = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "music" + SEPARATOR;

        /**
         * AI敏感词缓存前缀
         */
        public static final String SENSITIVE_WORD = APP_PREFIX + SEPARATOR + "ai" + SEPARATOR + "sensitive" + SEPARATOR;
    }

    /**
     * 支付相关
     */
    public static class Pay {
        /**
         * 支付订单缓存前缀
         */
        public static final String ORDER_CACHE = APP_PREFIX + SEPARATOR + "pay" + SEPARATOR + "order" + SEPARATOR;

        /**
         * 支付渠道配置前缀
         */
        public static final String CHANNEL_CONFIG = APP_PREFIX + SEPARATOR + "pay" + SEPARATOR + "channel" + SEPARATOR;

        /**
         * 支付回调锁前缀
         */
        public static final String CALLBACK_LOCK = APP_PREFIX + SEPARATOR + "pay" + SEPARATOR + "callback" + SEPARATOR;

        /**
         * 支付限流前缀
         */
        public static final String PAY_LIMIT = APP_PREFIX + SEPARATOR + "pay" + SEPARATOR + "limit" + SEPARATOR;
    }

    /**
     * 塔罗牌相关
     */
    public static class Tarot {
        /**
         * 塔罗牌阵缓存前缀
         */
        public static final String SPREAD_CACHE = APP_PREFIX + SEPARATOR + "tarot" + SEPARATOR + "spread" + SEPARATOR;

        /**
         * 塔罗牌义缓存前缀
         */
        public static final String CARD_MEANING = APP_PREFIX + SEPARATOR + "tarot" + SEPARATOR + "meaning" + SEPARATOR;

        /**
         * 塔罗解读记录前缀
         */
        public static final String READING_RECORD = APP_PREFIX + SEPARATOR + "tarot" + SEPARATOR + "reading" + SEPARATOR;

        /**
         * 塔罗解读锁前缀
         */
        public static final String READING_LOCK = APP_PREFIX + SEPARATOR + "tarot" + SEPARATOR + "lock" + SEPARATOR;
    }

    /**
     * 提现相关
     */
    public static class Withdraw {
        /**
         * 提现申请缓存前缀
         */
        public static final String APPLICATION_CACHE = APP_PREFIX + SEPARATOR + "withdraw" + SEPARATOR + "app" + SEPARATOR;

        /**
         * 提现配置缓存前缀
         */
        public static final String CONFIG_CACHE = APP_PREFIX + SEPARATOR + "withdraw" + SEPARATOR + "config" + SEPARATOR;

        /**
         * 提现处理锁前缀
         */
        public static final String PROCESS_LOCK = APP_PREFIX + SEPARATOR + "withdraw" + SEPARATOR + "lock" + SEPARATOR;

        /**
         * 提现限额缓存前缀
         */
        public static final String LIMIT_CACHE = APP_PREFIX + SEPARATOR + "withdraw" + SEPARATOR + "limit" + SEPARATOR;
    }

    /**
     * 系统相关
     */
    public static class System {
        /**
         * 系统配置缓存前缀
         */
        public static final String CONFIG_CACHE = APP_PREFIX + SEPARATOR + "system" + SEPARATOR + "config" + SEPARATOR;

        /**
         * 国际化消息缓存前缀
         */
        public static final String I18N_MESSAGE = APP_PREFIX + SEPARATOR + "system" + SEPARATOR + "i18n" + SEPARATOR;

        /**
         * 广告信息缓存前缀
         */
        public static final String ADV_INFO = APP_PREFIX + SEPARATOR + "system" + SEPARATOR + "adv" + SEPARATOR;

        /**
         * 首页配置缓存前缀
         */
        public static final String HOME_CONFIG = APP_PREFIX + SEPARATOR + "system" + SEPARATOR + "home" + SEPARATOR;

        /**
         * 分享信息缓存前缀
         */
        public static final String SHARE_INFO = APP_PREFIX + SEPARATOR + "system" + SEPARATOR + "share" + SEPARATOR;
    }

    /**
     * 限流相关
     */
    public static class RateLimit {
        /**
         * API限流前缀
         */
        public static final String API_LIMIT = APP_PREFIX + SEPARATOR + "limit" + SEPARATOR + "api" + SEPARATOR;

        /**
         * 用户操作限流前缀
         */
        public static final String USER_LIMIT = APP_PREFIX + SEPARATOR + "limit" + SEPARATOR + "user" + SEPARATOR;

        /**
         * IP限流前缀
         */
        public static final String IP_LIMIT = APP_PREFIX + SEPARATOR + "limit" + SEPARATOR + "ip" + SEPARATOR;
    }

    /**
     * 锁相关
     */
    public static class Lock {
        /**
         * 分布式锁前缀
         */
        public static final String DISTRIBUTED_LOCK = APP_PREFIX + SEPARATOR + "lock" + SEPARATOR + "distributed" + SEPARATOR;

        /**
         * 业务锁前缀
         */
        public static final String BUSINESS_LOCK = APP_PREFIX + SEPARATOR + "lock" + SEPARATOR + "business" + SEPARATOR;
    }

    /**
     * 构建用户信息缓存Key
     *
     * @param userId 用户ID
     * @return 缓存Key
     */
    public static String buildUserInfoKey(Integer userId) {
        return User.USER_INFO + userId;
    }

    /**
     * 构建用户登录缓存Key
     *
     * @param userId 用户ID
     * @return 缓存Key
     */
    public static String buildUserLoginKey(Integer userId) {
        return User.USER_LOGIN + userId;
    }

    /**
     * 构建短信验证码Key
     *
     * @param phone 手机号
     * @return 缓存Key
     */
    public static String buildSmsCodeKey(String phone) {
        return Auth.SMS_CODE + phone;
    }

    /**
     * 构建邮箱验证码Key
     *
     * @param email 邮箱
     * @return 缓存Key
     */
    public static String buildEmailCodeKey(String email) {
        return Auth.EMAIL_CODE + email;
    }

    /**
     * 构建AI对话上下文Key
     *
     * @param userId 用户ID
     * @param roomId 房间ID
     * @return 缓存Key
     */
    public static String buildChatContextKey(Integer userId, Long roomId) {
        return Ai.CHAT_CONTEXT + userId + SEPARATOR + roomId;
    }

    /**
     * 构建支付订单缓存Key
     *
     * @param orderNo 订单号
     * @return 缓存Key
     */
    public static String buildPayOrderKey(String orderNo) {
        return Pay.ORDER_CACHE + orderNo;
    }

    /**
     * 构建分布式锁Key
     *
     * @param lockName 锁名称
     * @return 锁Key
     */
    public static String buildDistributedLockKey(String lockName) {
        return Lock.DISTRIBUTED_LOCK + lockName;
    }
}
