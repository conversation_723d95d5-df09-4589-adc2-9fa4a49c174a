package com.hncboy.chatgpt.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * HTTP工具类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:45
 */
@Slf4j
public class HttpUtil {

    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    /**
     * GET请求
     *
     * @param url   请求URL
     * @param clazz 返回类型
     * @param <T>   泛型
     * @return 响应结果
     */
    public static <T> T get(String url, Class<T> clazz) {
        try {
            ResponseEntity<T> response = REST_TEMPLATE.getForEntity(url, clazz);
            return response.getBody();
        } catch (Exception e) {
            log.error("GET请求失败: url={}", url, e);
            return null;
        }
    }

    /**
     * GET请求带参数
     *
     * @param url    请求URL
     * @param params 请求参数
     * @param clazz  返回类型
     * @param <T>    泛型
     * @return 响应结果
     */
    public static <T> T get(String url, Map<String, Object> params, Class<T> clazz) {
        try {
            ResponseEntity<T> response = REST_TEMPLATE.getForEntity(url, clazz, params);
            return response.getBody();
        } catch (Exception e) {
            log.error("GET请求失败: url={}, params={}", url, params, e);
            return null;
        }
    }

    /**
     * POST请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @param clazz 返回类型
     * @param <T>  泛型
     * @return 响应结果
     */
    public static <T> T post(String url, Object body, Class<T> clazz) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> entity = new HttpEntity<>(body, headers);
            ResponseEntity<T> response = REST_TEMPLATE.postForEntity(url, entity, clazz);
            return response.getBody();
        } catch (Exception e) {
            log.error("POST请求失败: url={}, body={}", url, body, e);
            return null;
        }
    }

    /**
     * POST请求带头部
     *
     * @param url     请求URL
     * @param body    请求体
     * @param headers 请求头
     * @param clazz   返回类型
     * @param <T>     泛型
     * @return 响应结果
     */
    public static <T> T post(String url, Object body, HttpHeaders headers, Class<T> clazz) {
        try {
            HttpEntity<Object> entity = new HttpEntity<>(body, headers);
            ResponseEntity<T> response = REST_TEMPLATE.postForEntity(url, entity, clazz);
            return response.getBody();
        } catch (Exception e) {
            log.error("POST请求失败: url={}, body={}, headers={}", url, body, headers, e);
            return null;
        }
    }

    /**
     * PUT请求
     *
     * @param url  请求URL
     * @param body 请求体
     * @param clazz 返回类型
     * @param <T>  泛型
     * @return 响应结果
     */
    public static <T> T put(String url, Object body, Class<T> clazz) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> entity = new HttpEntity<>(body, headers);
            ResponseEntity<T> response = REST_TEMPLATE.exchange(url, HttpMethod.PUT, entity, clazz);
            return response.getBody();
        } catch (Exception e) {
            log.error("PUT请求失败: url={}, body={}", url, body, e);
            return null;
        }
    }

    /**
     * DELETE请求
     *
     * @param url   请求URL
     * @param clazz 返回类型
     * @param <T>   泛型
     * @return 响应结果
     */
    public static <T> T delete(String url, Class<T> clazz) {
        try {
            ResponseEntity<T> response = REST_TEMPLATE.exchange(url, HttpMethod.DELETE, null, clazz);
            return response.getBody();
        } catch (Exception e) {
            log.error("DELETE请求失败: url={}", url, e);
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = ip.indexOf(',');
            if (index != -1) {
                return ip.substring(0, index);
            } else {
                return ip;
            }
        }
        
        ip = request.getHeader("X-Real-IP");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("Proxy-Client-IP");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StringUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取用户代理
     *
     * @param request HTTP请求
     * @return 用户代理
     */
    public static String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * 判断是否为Ajax请求
     *
     * @param request HTTP请求
     * @return 是否为Ajax请求
     */
    public static boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }

    /**
     * 判断是否为移动端请求
     *
     * @param request HTTP请求
     * @return 是否为移动端请求
     */
    public static boolean isMobileRequest(HttpServletRequest request) {
        String userAgent = getUserAgent(request);
        if (StringUtil.isEmpty(userAgent)) {
            return false;
        }
        
        userAgent = userAgent.toLowerCase();
        return userAgent.contains("mobile") || 
               userAgent.contains("android") || 
               userAgent.contains("iphone") || 
               userAgent.contains("ipad") || 
               userAgent.contains("windows phone");
    }

    /**
     * 构建查询参数字符串
     *
     * @param params 参数Map
     * @return 查询参数字符串
     */
    public static String buildQueryString(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }
}
