package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI能力类型枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:50
 */
@Getter
@AllArgsConstructor
public enum AiAbilityTypeEnum {

    /**
     * 对话
     */
    CHAT("CHAT", "对话"),

    /**
     * 绘画
     */
    DRAW("DRAW", "绘画"),

    /**
     * 写作
     */
    WRITE("WRITE", "写作"),

    /**
     * 音乐
     */
    MUSIC("MUSIC", "音乐");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return AI能力类型枚举
     */
    public static AiAbilityTypeEnum getByCode(String code) {
        for (AiAbilityTypeEnum abilityTypeEnum : values()) {
            if (abilityTypeEnum.getCode().equals(code)) {
                return abilityTypeEnum;
            }
        }
        return CHAT;
    }

    /**
     * 是否为文本类型
     *
     * @return 是否为文本类型
     */
    public boolean isTextType() {
        return this == CHAT || this == WRITE;
    }

    /**
     * 是否为媒体类型
     *
     * @return 是否为媒体类型
     */
    public boolean isMediaType() {
        return this == DRAW || this == MUSIC;
    }
}
