package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 10:00
 */
@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    /**
     * 用户消息
     */
    USER(1, "用户消息"),

    /**
     * AI回复
     */
    AI(2, "AI回复"),

    /**
     * 系统消息
     */
    SYSTEM(3, "系统消息"),

    /**
     * 错误消息
     */
    ERROR(4, "错误消息");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 消息类型枚举
     */
    public static MessageTypeEnum getByCode(Integer code) {
        for (MessageTypeEnum messageTypeEnum : values()) {
            if (messageTypeEnum.getCode().equals(code)) {
                return messageTypeEnum;
            }
        }
        return USER;
    }

    /**
     * 是否为用户消息
     *
     * @return 是否为用户消息
     */
    public boolean isUserMessage() {
        return this == USER;
    }

    /**
     * 是否为AI消息
     *
     * @return 是否为AI消息
     */
    public boolean isAiMessage() {
        return this == AI;
    }
}
