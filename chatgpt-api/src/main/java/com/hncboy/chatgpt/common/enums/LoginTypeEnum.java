package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录类型枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:40
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    /**
     * 微信登录
     */
    WECHAT("WECHAT", "微信登录"),

    /**
     * Google登录
     */
    GOOGLE("GOOGLE", "Google登录"),

    /**
     * Facebook登录
     */
    FACEBOOK("FACEBOOK", "Facebook登录"),

    /**
     * 手机号登录
     */
    PHONE("PHONE", "手机号登录"),

    /**
     * 邮箱登录
     */
    EMAIL("EMAIL", "邮箱登录"),

    /**
     * 指纹登录
     */
    FINGERPRINT("FINGERPRINT", "指纹登录");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 登录类型枚举
     */
    public static LoginTypeEnum getByCode(String code) {
        for (LoginTypeEnum loginTypeEnum : values()) {
            if (loginTypeEnum.getCode().equals(code)) {
                return loginTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否为第三方登录
     *
     * @return 是否为第三方登录
     */
    public boolean isThirdParty() {
        return this == WECHAT || this == GOOGLE || this == FACEBOOK;
    }

    /**
     * 是否为本地登录
     *
     * @return 是否为本地登录
     */
    public boolean isLocal() {
        return this == PHONE || this == EMAIL;
    }

    /**
     * 是否为生物识别登录
     *
     * @return 是否为生物识别登录
     */
    public boolean isBiometric() {
        return this == FINGERPRINT;
    }
}
