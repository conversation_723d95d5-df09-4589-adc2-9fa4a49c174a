package com.hncboy.chatgpt.common.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.hncboy.chatgpt.common.enums.ResponseEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一返回结果类
 *
 * <AUTHOR>
 * @date 2023/3/22 10:15
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 私有构造方法
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 私有构造方法
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     */
    private Result(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功返回
     *
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> success() {
        return new Result<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getMessage(), null);
    }

    /**
     * 成功返回
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 结果
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(ResponseEnum.SUCCESS.getCode(), message, null);
    }

    /**
     * 成功返回
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResponseEnum.SUCCESS.getCode(), ResponseEnum.SUCCESS.getMessage(), data);
    }

    /**
     * 成功返回
     *
     * @param data    数据
     * @param message 消息
     * @param <T>     数据类型
     * @return 结果
     */
    public static <T> Result<T> success(T data, String message) {
        return new Result<>(ResponseEnum.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败返回
     *
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> error() {
        return new Result<>(ResponseEnum.ERROR.getCode(), ResponseEnum.ERROR.getMessage(), null);
    }

    /**
     * 失败返回
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ResponseEnum.ERROR.getCode(), message, null);
    }

    /**
     * 失败返回
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return 结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 失败返回
     *
     * @param responseEnum 响应枚举
     * @param <T>          数据类型
     * @return 结果
     */
    public static <T> Result<T> error(ResponseEnum responseEnum) {
        return new Result<>(responseEnum.getCode(), responseEnum.getMessage(), null);
    }

    /**
     * 失败返回
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 结果
     */
    public static <T> Result<T> error(Integer code, String message, T data) {
        return new Result<>(code, message, data);
    }

    /**
     * 根据响应枚举返回结果
     *
     * @param responseEnum 响应枚举
     * @param data         数据
     * @param <T>          数据类型
     * @return 结果
     */
    public static <T> Result<T> result(ResponseEnum responseEnum, T data) {
        return new Result<>(responseEnum.getCode(), responseEnum.getMessage(), data);
    }

    /**
     * 根据条件返回结果
     *
     * @param condition 条件
     * @param <T>       数据类型
     * @return 结果
     */
    public static <T> Result<T> result(boolean condition) {
        return condition ? success() : error();
    }

    /**
     * 根据条件返回结果
     *
     * @param condition    条件
     * @param successMsg   成功消息
     * @param errorMsg     失败消息
     * @param <T>          数据类型
     * @return 结果
     */
    public static <T> Result<T> result(boolean condition, String successMsg, String errorMsg) {
        return condition ? success(successMsg) : error(errorMsg);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return ResponseEnum.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 设置请求ID
     *
     * @param requestId 请求ID
     * @return 结果
     */
    public Result<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
}
