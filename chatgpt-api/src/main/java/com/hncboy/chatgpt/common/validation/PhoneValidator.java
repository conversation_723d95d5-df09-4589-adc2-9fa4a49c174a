package com.hncboy.chatgpt.common.validation;

import com.hncboy.chatgpt.common.util.StringUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 手机号验证器
 *
 * <AUTHOR>
 * @date 2023/3/22 10:40
 */
public class PhoneValidator implements ConstraintValidator<Phone, String> {

    @Override
    public void initialize(Phone constraintAnnotation) {
        // 初始化方法，可以获取注解参数
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果值为空，由@NotNull等注解处理
        if (StringUtil.isEmpty(value)) {
            return true;
        }
        return StringUtil.isValidPhone(value);
    }
}
