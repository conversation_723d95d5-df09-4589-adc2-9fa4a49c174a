package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应枚举
 *
 * <AUTHOR>
 * @date 2023/3/22 09:30
 */
@Getter
@AllArgsConstructor
public enum ResponseEnum {

    /**
     * 成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 失败
     */
    ERROR(500, "操作失败"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 禁止访问
     */
    FORBIDDEN(403, "禁止访问"),

    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求超时
     */
    REQUEST_TIMEOUT(408, "请求超时"),

    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),

    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    /**
     * 网关超时
     */
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 用户相关错误
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_DISABLED(1002, "用户已被禁用"),
    USER_DELETED(1003, "用户已被删除"),
    USER_PASSWORD_ERROR(1004, "密码错误"),
    USER_PHONE_EXISTS(1005, "手机号已存在"),
    USER_EMAIL_EXISTS(1006, "邮箱已存在"),
    USER_ACCOUNT_EXISTS(1007, "账号已存在"),
    USER_NOT_LOGIN(1008, "用户未登录"),
    USER_LOGIN_EXPIRED(1009, "登录已过期"),
    USER_PERMISSION_DENIED(1010, "权限不足"),

    // 验证码相关错误
    VERIFY_CODE_ERROR(1101, "验证码错误"),
    VERIFY_CODE_EXPIRED(1102, "验证码已过期"),
    VERIFY_CODE_SEND_FAIL(1103, "验证码发送失败"),
    VERIFY_CODE_SEND_FREQUENT(1104, "验证码发送过于频繁"),

    // 第三方登录相关错误
    OAUTH_LOGIN_FAIL(1201, "第三方登录失败"),
    OAUTH_CALLBACK_ERROR(1202, "第三方登录回调错误"),
    OAUTH_TOKEN_INVALID(1203, "第三方登录令牌无效"),
    OAUTH_USER_INFO_ERROR(1204, "获取第三方用户信息失败"),

    // 支付相关错误
    PAY_ORDER_NOT_FOUND(2001, "支付订单不存在"),
    PAY_ORDER_STATUS_ERROR(2002, "支付订单状态错误"),
    PAY_ORDER_EXPIRED(2003, "支付订单已过期"),
    PAY_CHANNEL_NOT_SUPPORT(2004, "不支持的支付渠道"),
    PAY_AMOUNT_ERROR(2005, "支付金额错误"),
    PAY_CALLBACK_VERIFY_FAIL(2006, "支付回调验证失败"),
    PAY_CREATE_ORDER_FAIL(2007, "创建支付订单失败"),
    PAY_QUERY_ORDER_FAIL(2008, "查询支付订单失败"),

    // 产品相关错误
    PRODUCT_NOT_FOUND(2101, "产品不存在"),
    PRODUCT_OFF_SHELF(2102, "产品已下架"),
    PRODUCT_STOCK_INSUFFICIENT(2103, "产品库存不足"),
    PRODUCT_PURCHASE_LIMIT(2104, "产品购买数量超限"),
    PRODUCT_PRICE_CHANGED(2105, "产品价格已变更"),

    // AI相关错误
    AI_MODEL_NOT_FOUND(3001, "AI模型不存在"),
    AI_MODEL_DISABLED(3002, "AI模型已禁用"),
    AI_REQUEST_FAIL(3003, "AI请求失败"),
    AI_RESPONSE_ERROR(3004, "AI响应错误"),
    AI_QUOTA_INSUFFICIENT(3005, "AI配额不足"),
    AI_CONTENT_FILTERED(3006, "内容被过滤"),
    AI_AGENT_NOT_FOUND(3007, "AI智能体不存在"),
    AI_ROOM_NOT_FOUND(3008, "AI房间不存在"),
    AI_MESSAGE_NOT_FOUND(3009, "AI消息不存在"),

    // 塔罗牌相关错误
    TAROT_SPREAD_NOT_FOUND(4001, "塔罗牌阵不存在"),
    TAROT_CARD_NOT_FOUND(4002, "塔罗牌不存在"),
    TAROT_READING_NOT_FOUND(4003, "塔罗解读记录不存在"),
    TAROT_POINTS_INSUFFICIENT(4004, "塔罗币不足"),
    TAROT_READING_FAIL(4005, "塔罗解读失败"),

    // 提现相关错误
    WITHDRAW_CONFIG_NOT_FOUND(5001, "提现配置不存在"),
    WITHDRAW_AMOUNT_TOO_SMALL(5002, "提现金额过小"),
    WITHDRAW_AMOUNT_TOO_LARGE(5003, "提现金额过大"),
    WITHDRAW_BALANCE_INSUFFICIENT(5004, "余额不足"),
    WITHDRAW_DAILY_LIMIT_EXCEEDED(5005, "超出每日提现限额"),
    WITHDRAW_MONTHLY_LIMIT_EXCEEDED(5006, "超出每月提现限额"),
    WITHDRAW_APPLICATION_NOT_FOUND(5007, "提现申请不存在"),
    WITHDRAW_STATUS_ERROR(5008, "提现状态错误"),
    WITHDRAW_PROCESS_FAIL(5009, "提现处理失败"),

    // 文件上传相关错误
    FILE_UPLOAD_FAIL(6001, "文件上传失败"),
    FILE_SIZE_EXCEEDED(6002, "文件大小超限"),
    FILE_TYPE_NOT_SUPPORT(6003, "不支持的文件类型"),
    FILE_NOT_FOUND(6004, "文件不存在"),
    FILE_DELETE_FAIL(6005, "文件删除失败"),

    // 缓存相关错误
    CACHE_GET_FAIL(7001, "缓存获取失败"),
    CACHE_SET_FAIL(7002, "缓存设置失败"),
    CACHE_DELETE_FAIL(7003, "缓存删除失败"),

    // 数据库相关错误
    DATABASE_ERROR(8001, "数据库操作失败"),
    DATA_NOT_FOUND(8002, "数据不存在"),
    DATA_ALREADY_EXISTS(8003, "数据已存在"),
    DATA_CONSTRAINT_VIOLATION(8004, "数据约束违反"),

    // 系统相关错误
    SYSTEM_CONFIG_NOT_FOUND(9001, "系统配置不存在"),
    SYSTEM_MAINTENANCE(9002, "系统维护中"),
    SYSTEM_BUSY(9003, "系统繁忙，请稍后重试"),
    RATE_LIMIT_EXCEEDED(9004, "请求频率超限"),
    CONCURRENT_LIMIT_EXCEEDED(9005, "并发数超限"),

    // 国际化相关错误
    I18N_MESSAGE_NOT_FOUND(9101, "国际化消息不存在"),
    LANGUAGE_NOT_SUPPORT(9102, "不支持的语言"),
    CURRENCY_NOT_SUPPORT(9103, "不支持的币种"),

    // 业务场景相关错误
    BIZ_SCENE_NOT_SUPPORT(9201, "不支持的业务场景"),
    BIZ_SCENE_DISABLED(9202, "业务场景已禁用");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 响应枚举
     */
    public static ResponseEnum getByCode(Integer code) {
        for (ResponseEnum responseEnum : values()) {
            if (responseEnum.getCode().equals(code)) {
                return responseEnum;
            }
        }
        return ERROR;
    }

    /**
     * 是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return SUCCESS.getCode().equals(this.code);
    }

    /**
     * 是否失败
     *
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }
}
