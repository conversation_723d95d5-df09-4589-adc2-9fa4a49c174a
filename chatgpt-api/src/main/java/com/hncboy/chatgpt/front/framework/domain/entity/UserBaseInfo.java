package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户基础信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_base_info")
public class UserBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 账号(手机号或其他账号)
     */
    private String account;


    /**
     * 用户名
     */
    private String name;


    /**
     * 昵称
     */
    private String nickName;


    /**
     * 密码
     */
    private String password;


    /**
     * 最后登录时间
     */
    private LocalDateTime loginTime;


    /**
     * IP地址
     */
    private String ip;


    /**
     * 头像
     */
    private String headSculpture;


    /**
     * 地址
     */
    private String address;


    /**
     * 邮箱
     */
    private String email;


    /**
     * 状态0正常1禁用
     */
    private Integer status;


    /**
     * 剩余可用次数(充值)
     */
    private Integer useNum;


    /**
     * 免费可用次数(赠送)
     */
    private Integer freeNum;

    /**
     * 每日免费次数
     */
    private Integer dailyFreeTime;

    /**
     * 绘画次数
     */
    private Integer drawNum;

    /**
     * 音乐创作次数
     */
    private Integer musicNum;

    /**
     * 写作作次数
     */
    private Integer writeNum;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 会员到期时间
     */
    private LocalDateTime vipEndTime;


    /**
     * 邀请人Id
     */
    private Integer parentId;

    /**
     * 积分
     */
    private Integer points;


    /**
     * 微信openId
     */
    private String openId;


    /**
     * 是否首次登陆
     */
    private String firstStatus;

    /**
     * 塔罗币
     */
    private Integer tarotCoins;
    /**
     * 用户类型 zns:智能社 tarot:塔罗
     */
    private String userType;

    private Integer commissionId;

    /**
     * 新增字段：是否删除（注销）0存在，1删除
     */
    private byte deleted;

    private Long usersId;
}
