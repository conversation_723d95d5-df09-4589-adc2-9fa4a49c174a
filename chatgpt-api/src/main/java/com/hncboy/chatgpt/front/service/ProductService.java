package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.product.ProductDO;

import java.util.List;

/**
 * 产品服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 15:50
 */
public interface ProductService extends IService<ProductDO> {

    /**
     * 获取可用产品列表
     *
     * @param bizScene 业务场景
     * @return 产品列表
     */
    List<ProductDO> listAvailableProducts(String bizScene);

    /**
     * 根据产品类型获取产品列表
     *
     * @param productType 产品类型
     * @return 产品列表
     */
    List<ProductDO> listByProductType(String productType);

    /**
     * 获取推荐产品列表
     *
     * @param bizScene 业务场景
     * @return 推荐产品列表
     */
    List<ProductDO> listRecommendedProducts(String bizScene);

    /**
     * 获取热门产品列表
     *
     * @param bizScene 业务场景
     * @return 热门产品列表
     */
    List<ProductDO> listHotProducts(String bizScene);

    /**
     * 获取限时产品列表
     *
     * @param bizScene 业务场景
     * @return 限时产品列表
     */
    List<ProductDO> listLimitedTimeProducts(String bizScene);

    /**
     * 减少产品库存
     *
     * @param productId 产品ID
     * @param quantity  减少数量
     * @return 是否成功
     */
    boolean decreaseStock(Long productId, int quantity);

    /**
     * 增加产品库存
     *
     * @param productId 产品ID
     * @param quantity  增加数量
     * @return 是否成功
     */
    boolean increaseStock(Long productId, int quantity);

    /**
     * 检查产品是否可购买
     *
     * @param productId 产品ID
     * @param quantity  购买数量
     * @return 是否可购买
     */
    boolean canPurchase(Long productId, int quantity);

    /**
     * 更新产品销量
     *
     * @param productId 产品ID
     * @param quantity  销售数量
     * @return 是否成功
     */
    boolean updateSoldQuantity(Long productId, int quantity);
}
