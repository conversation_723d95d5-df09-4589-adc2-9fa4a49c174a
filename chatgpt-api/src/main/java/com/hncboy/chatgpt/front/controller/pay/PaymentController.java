package com.hncboy.chatgpt.front.controller.pay;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.db.entity.pay.PayOrderDO;
import com.hncboy.chatgpt.db.entity.product.ProductDO;
import com.hncboy.chatgpt.front.service.PayOrderService;
import com.hncboy.chatgpt.front.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 支付控制器
 *
 * <AUTHOR>
 * @date 2023/3/22 15:30
 */
@Slf4j
@RestController
@RequestMapping("/api/payment")
@Tag(name = "支付管理", description = "支付订单、产品管理等支付相关接口")
public class PaymentController {

    @Resource
    private PayOrderService payOrderService;

    @Resource
    private ProductService productService;

    /**
     * 获取产品列表
     *
     * @param bizScene 业务场景
     * @return 产品列表
     */
    @GetMapping("/products")
    @Operation(summary = "获取产品列表", description = "根据业务场景获取可购买的产品列表")
    public Result<List<ProductDO>> getProducts(@Parameter(description = "业务场景") @RequestParam(required = false) String bizScene) {
        log.info("获取产品列表: bizScene={}", bizScene);
        
        try {
            List<ProductDO> products = productService.listAvailableProducts(bizScene);
            log.info("获取产品列表成功: bizScene={}, count={}", bizScene, products.size());
            return Result.success(products);
        } catch (Exception e) {
            log.error("获取产品列表失败: bizScene={}", bizScene, e);
            return Result.error("获取产品列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取产品详情
     *
     * @param productId 产品ID
     * @return 产品详情
     */
    @GetMapping("/products/{productId}")
    @Operation(summary = "获取产品详情", description = "根据产品ID获取产品详细信息")
    public Result<ProductDO> getProductDetail(@Parameter(description = "产品ID") @PathVariable Long productId) {
        log.info("获取产品详情: productId={}", productId);
        
        try {
            ProductDO product = productService.getById(productId);
            if (product == null || !product.canPurchase()) {
                return Result.error("产品不存在或不可购买");
            }
            
            log.info("获取产品详情成功: productId={}, productName={}", productId, product.getProductName());
            return Result.success(product);
        } catch (Exception e) {
            log.error("获取产品详情失败: productId={}", productId, e);
            return Result.error("获取产品详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建支付订单
     *
     * @param request     创建请求
     * @param httpRequest HTTP请求
     * @return 支付订单
     */
    @PostMapping("/orders")
    @Operation(summary = "创建支付订单", description = "创建支付订单并返回支付参数")
    public Result<CreateOrderResponse> createOrder(@Valid @RequestBody CreateOrderRequest request,
                                                   HttpServletRequest httpRequest) {
        log.info("创建支付订单: request={}", request);
        
        try {
            // 验证产品信息
            ProductDO product = productService.getById(request.getProductId());
            if (product == null || !product.canPurchase()) {
                return Result.error("产品不存在或不可购买");
            }
            
            // 验证购买数量
            int quantity = request.getQuantity() != null ? request.getQuantity() : 1;
            if (!product.isValidPurchaseQuantity(quantity)) {
                return Result.error("购买数量不合法");
            }
            
            // 计算订单金额
            BigDecimal totalAmount = product.getPrice().multiply(BigDecimal.valueOf(quantity));
            
            // 创建支付订单
            PayOrderDO payOrder = new PayOrderDO();
            payOrder.setOrderNo(generateOrderNo());
            payOrder.setUserId(request.getUserId());
            payOrder.setProductId(request.getProductId());
            payOrder.setProductName(product.getProductName());
            payOrder.setQuantity(quantity);
            payOrder.setUnitPrice(product.getPrice());
            payOrder.setTotalAmount(totalAmount);
            payOrder.setPayAmount(totalAmount);
            payOrder.setPayChannel(request.getPayChannel());
            payOrder.setPayMethod(request.getPayMethod());
            payOrder.setBizScene(request.getBizScene());
            payOrder.setCurrency(product.getCurrency());
            payOrder.setStatus("PENDING");
            payOrder.setClientIp(getClientIp(httpRequest));
            payOrder.setUserAgent(httpRequest.getHeader("User-Agent"));
            payOrder.setExpireTime(LocalDateTime.now().plusHours(2)); // 2小时过期
            
            // 保存订单
            payOrderService.save(payOrder);
            
            // TODO: 调用支付服务创建支付
            String payData = createPayment(payOrder);
            
            // 构建响应
            CreateOrderResponse response = new CreateOrderResponse();
            response.setOrderNo(payOrder.getOrderNo());
            response.setAmount(totalAmount);
            response.setPayData(payData);
            response.setPayMethod(request.getPayMethod());
            response.setExpireTime(payOrder.getExpireTime().toEpochSecond(java.time.ZoneOffset.of("+8")) * 1000);
            
            log.info("创建支付订单成功: orderNo={}, amount={}", payOrder.getOrderNo(), totalAmount);
            return Result.success(response);
        } catch (Exception e) {
            log.error("创建支付订单失败: request={}", request, e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     *
     * @param orderNo 订单号
     * @return 订单状态
     */
    @GetMapping("/orders/{orderNo}")
    @Operation(summary = "查询支付订单状态", description = "根据订单号查询支付订单状态")
    public Result<PayOrderDO> getOrderStatus(@Parameter(description = "订单号") @PathVariable String orderNo) {
        log.info("查询支付订单状态: orderNo={}", orderNo);
        
        try {
            PayOrderDO payOrder = payOrderService.getByOrderNo(orderNo);
            if (payOrder == null) {
                return Result.error("订单不存在");
            }
            
            log.info("查询支付订单状态成功: orderNo={}, status={}", orderNo, payOrder.getStatus());
            return Result.success(payOrder);
        } catch (Exception e) {
            log.error("查询支付订单状态失败: orderNo={}", orderNo, e);
            return Result.error("查询订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消支付订单
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    @PostMapping("/orders/{orderNo}/cancel")
    @Operation(summary = "取消支付订单", description = "取消未支付的订单")
    public Result<Void> cancelOrder(@Parameter(description = "订单号") @PathVariable String orderNo) {
        log.info("取消支付订单: orderNo={}", orderNo);
        
        try {
            boolean success = payOrderService.cancelOrder(orderNo);
            if (success) {
                log.info("取消支付订单成功: orderNo={}", orderNo);
                return Result.success();
            } else {
                return Result.error("取消订单失败，订单可能已支付或不存在");
            }
        } catch (Exception e) {
            log.error("取消支付订单失败: orderNo={}", orderNo, e);
            return Result.error("取消订单失败: " + e.getMessage());
        }
    }

    /**
     * 支付回调处理
     *
     * @param channel 支付渠道
     * @param params  回调参数
     * @return 回调响应
     */
    @PostMapping("/callback/{channel}")
    @Operation(summary = "支付回调处理", description = "处理第三方支付平台的回调通知")
    public String paymentCallback(@Parameter(description = "支付渠道") @PathVariable String channel,
                                  @RequestParam Map<String, Object> params) {
        log.info("支付回调处理: channel={}, params={}", channel, params);
        
        try {
            boolean success = payOrderService.handlePaymentCallback(channel, params);
            if (success) {
                log.info("支付回调处理成功: channel={}", channel);
                return "success";
            } else {
                log.error("支付回调处理失败: channel={}", channel);
                return "failure";
            }
        } catch (Exception e) {
            log.error("支付回调处理异常: channel={}", channel, e);
            return "failure";
        }
    }

    /**
     * 获取用户订单列表
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   页大小
     * @return 订单列表
     */
    @GetMapping("/orders")
    @Operation(summary = "获取用户订单列表", description = "分页获取用户的支付订单列表")
    public Result<List<PayOrderDO>> getUserOrders(@Parameter(description = "用户ID") @RequestParam Integer userId,
                                                   @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
                                                   @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取用户订单列表: userId={}, page={}, size={}", userId, page, size);
        
        try {
            List<PayOrderDO> orders = payOrderService.listUserOrders(userId, page, size);
            log.info("获取用户订单列表成功: userId={}, count={}", userId, orders.size());
            return Result.success(orders);
        } catch (Exception e) {
            log.error("获取用户订单列表失败: userId={}", userId, e);
            return Result.error("获取订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    private String generateOrderNo() {
        return "PAY" + System.currentTimeMillis() + IdUtil.randomUUID().substring(0, 8).toUpperCase();
    }

    /**
     * 获取客户端IP
     *
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 创建支付
     *
     * @param payOrder 支付订单
     * @return 支付数据
     */
    private String createPayment(PayOrderDO payOrder) {
        // TODO: 根据支付渠道调用对应的支付服务
        // 这里返回模拟数据
        switch (payOrder.getPayMethod()) {
            case "QR":
                return "https://qr.alipay.com/bax08431lqjhgxzlvhvb00a7";
            case "H5":
                return "https://mclient.alipay.com/h5continue.htm?h5_route_token=xxx";
            case "APP":
                return "{\"app_id\":\"xxx\",\"biz_content\":\"xxx\"}";
            default:
                return "payment-data";
        }
    }

    /**
     * 创建订单请求
     */
    public static class CreateOrderRequest {
        @NotNull(message = "产品ID不能为空")
        @Parameter(description = "产品ID")
        private Long productId;
        
        @NotNull(message = "用户ID不能为空")
        @Parameter(description = "用户ID")
        private Integer userId;
        
        @NotBlank(message = "支付渠道不能为空")
        @Parameter(description = "支付渠道: ALIPAY-支付宝, WECHAT-微信")
        private String payChannel;
        
        @NotBlank(message = "支付方式不能为空")
        @Parameter(description = "支付方式: QR-二维码, H5-手机网页, APP-应用内")
        private String payMethod;
        
        @Parameter(description = "业务场景")
        private String bizScene;
        
        @Parameter(description = "购买数量")
        private Integer quantity;
        
        // getters and setters
        public Long getProductId() { return productId; }
        public void setProductId(Long productId) { this.productId = productId; }
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        public String getPayChannel() { return payChannel; }
        public void setPayChannel(String payChannel) { this.payChannel = payChannel; }
        public String getPayMethod() { return payMethod; }
        public void setPayMethod(String payMethod) { this.payMethod = payMethod; }
        public String getBizScene() { return bizScene; }
        public void setBizScene(String bizScene) { this.bizScene = bizScene; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        
        @Override
        public String toString() {
            return "CreateOrderRequest{" +
                    "productId=" + productId +
                    ", userId=" + userId +
                    ", payChannel='" + payChannel + '\'' +
                    ", payMethod='" + payMethod + '\'' +
                    ", bizScene='" + bizScene + '\'' +
                    ", quantity=" + quantity +
                    '}';
        }
    }

    /**
     * 创建订单响应
     */
    public static class CreateOrderResponse {
        @Parameter(description = "订单号")
        private String orderNo;
        
        @Parameter(description = "支付金额")
        private BigDecimal amount;
        
        @Parameter(description = "支付数据")
        private String payData;
        
        @Parameter(description = "支付方式")
        private String payMethod;
        
        @Parameter(description = "过期时间戳")
        private Long expireTime;
        
        // getters and setters
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getPayData() { return payData; }
        public void setPayData(String payData) { this.payData = payData; }
        public String getPayMethod() { return payMethod; }
        public void setPayMethod(String payMethod) { this.payMethod = payMethod; }
        public Long getExpireTime() { return expireTime; }
        public void setExpireTime(Long expireTime) { this.expireTime = expireTime; }
    }
}
