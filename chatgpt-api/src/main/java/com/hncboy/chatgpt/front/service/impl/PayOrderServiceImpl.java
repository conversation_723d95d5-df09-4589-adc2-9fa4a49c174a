package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.pay.PayOrderDO;
import com.hncboy.chatgpt.db.mapper.pay.PayOrderMapper;
import com.hncboy.chatgpt.front.service.PayOrderService;
import com.hncboy.chatgpt.front.service.ProductService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 支付订单服务实现类
 *
 * <AUTHOR>
 * @date 2023/3/22 16:00
 */
@Slf4j
@Service
public class PayOrderServiceImpl extends ServiceImpl<PayOrderMapper, PayOrderDO> implements PayOrderService {

    @Resource
    private PayOrderMapper payOrderMapper;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    @Resource
    private ProductService productService;

    @Override
    public PayOrderDO getByOrderNo(String orderNo) {
        log.info("根据订单号查询订单: orderNo={}", orderNo);
        
        LambdaQueryWrapper<PayOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrderDO::getOrderNo, orderNo);
        
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String orderNo) {
        log.info("取消订单: orderNo={}", orderNo);
        
        try {
            PayOrderDO payOrder = getByOrderNo(orderNo);
            if (payOrder == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                return false;
            }
            
            if (!payOrder.canCancel()) {
                log.warn("订单不能取消: orderNo={}, status={}", orderNo, payOrder.getStatus());
                return false;
            }
            
            // 更新订单状态
            payOrder.setStatus("CANCELLED");
            payOrder.setCancelTime(LocalDateTime.now());
            updateById(payOrder);
            
            log.info("取消订单成功: orderNo={}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("取消订单失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentCallback(String channel, Map<String, Object> params) {
        log.info("处理支付回调: channel={}, params={}", channel, params);
        
        try {
            // 获取订单号
            String orderNo = getOrderNoFromParams(channel, params);
            if (StrUtil.isBlank(orderNo)) {
                log.error("无法从回调参数中获取订单号: channel={}", channel);
                return false;
            }
            
            // 查询订单
            PayOrderDO payOrder = getByOrderNo(orderNo);
            if (payOrder == null) {
                log.error("订单不存在: orderNo={}", orderNo);
                return false;
            }
            
            // 验证支付状态
            boolean isPaid = getPayStatusFromParams(channel, params);
            String transactionId = getTransactionIdFromParams(channel, params);
            
            if (isPaid) {
                // 支付成功
                return completePayment(orderNo, transactionId, payOrder.getPayAmount());
            } else {
                // 支付失败
                String failureCode = getFailureCodeFromParams(channel, params);
                String failureMsg = getFailureMsgFromParams(channel, params);
                return failPayment(orderNo, failureCode, failureMsg);
            }
        } catch (Exception e) {
            log.error("处理支付回调失败: channel={}", channel, e);
            return false;
        }
    }

    @Override
    public List<PayOrderDO> listUserOrders(Integer userId, Integer page, Integer size) {
        log.info("获取用户订单列表: userId={}, page={}, size={}", userId, page, size);
        
        Page<PayOrderDO> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<PayOrderDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrderDO::getUserId, userId);
        wrapper.orderByDesc(PayOrderDO::getCreateTime);
        
        IPage<PayOrderDO> result = page(pageParam, wrapper);
        return result.getRecords();
    }

    @Override
    public boolean updateOrderStatus(String orderNo, String status) {
        log.info("更新订单状态: orderNo={}, status={}", orderNo, status);
        
        try {
            PayOrderDO payOrder = getByOrderNo(orderNo);
            if (payOrder == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                return false;
            }
            
            payOrder.setStatus(status);
            updateById(payOrder);
            
            log.info("更新订单状态成功: orderNo={}, status={}", orderNo, status);
            return true;
        } catch (Exception e) {
            log.error("更新订单状态失败: orderNo={}, status={}", orderNo, status, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completePayment(String orderNo, String transactionId, BigDecimal paidAmount) {
        log.info("完成支付: orderNo={}, transactionId={}, paidAmount={}", orderNo, transactionId, paidAmount);
        
        try {
            PayOrderDO payOrder = getByOrderNo(orderNo);
            if (payOrder == null) {
                log.error("订单不存在: orderNo={}", orderNo);
                return false;
            }
            
            if (payOrder.isPaid()) {
                log.warn("订单已支付: orderNo={}", orderNo);
                return true;
            }
            
            // 更新订单状态
            payOrder.setStatus("PAID");
            payOrder.setTransactionId(transactionId);
            payOrder.setPaidAmount(paidAmount);
            payOrder.setPaidTime(LocalDateTime.now());
            updateById(payOrder);
            
            // 处理订单完成后的业务逻辑
            processOrderCompletion(payOrder);
            
            log.info("完成支付成功: orderNo={}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("完成支付失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    public boolean failPayment(String orderNo, String failureCode, String failureMsg) {
        log.info("支付失败: orderNo={}, failureCode={}, failureMsg={}", orderNo, failureCode, failureMsg);
        
        try {
            PayOrderDO payOrder = getByOrderNo(orderNo);
            if (payOrder == null) {
                log.error("订单不存在: orderNo={}", orderNo);
                return false;
            }
            
            payOrder.setStatus("FAILED");
            payOrder.setFailureCode(failureCode);
            payOrder.setFailureMsg(failureMsg);
            updateById(payOrder);
            
            log.info("支付失败处理成功: orderNo={}", orderNo);
            return true;
        } catch (Exception e) {
            log.error("支付失败处理失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    public boolean isOrderExpired(String orderNo) {
        PayOrderDO payOrder = getByOrderNo(orderNo);
        if (payOrder == null) {
            return true;
        }
        
        return payOrder.getExpireTime() != null && payOrder.getExpireTime().isBefore(LocalDateTime.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processOrderCompletion(PayOrderDO payOrder) {
        log.info("处理订单完成后的业务逻辑: orderNo={}", payOrder.getOrderNo());
        
        try {
            // 根据产品类型处理不同的业务逻辑
            String productType = payOrder.getProductType();
            Integer quantity = payOrder.getQuantity();
            Integer userId = payOrder.getUserId();
            
            switch (productType) {
                case "VIP":
                    // 处理VIP购买
                    // TODO: 实现VIP处理逻辑
                    break;
                case "POINTS":
                    // 处理积分购买
                    userBaseInfoService.addPoints(userId, quantity);
                    break;
                case "TAROT_COINS":
                    // 处理塔罗币购买
                    userBaseInfoService.addTarotCoins(userId, quantity);
                    break;
                case "TIMES":
                    // 处理次数购买
                    userBaseInfoService.addUserTimes(userId, quantity);
                    break;
                default:
                    log.warn("未知的产品类型: productType={}", productType);
                    break;
            }
            
            // 更新产品销量
            productService.updateSoldQuantity(payOrder.getProductId(), quantity);
            
            log.info("处理订单完成后的业务逻辑成功: orderNo={}", payOrder.getOrderNo());
            return true;
        } catch (Exception e) {
            log.error("处理订单完成后的业务逻辑失败: orderNo={}", payOrder.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 从回调参数中获取订单号
     */
    private String getOrderNoFromParams(String channel, Map<String, Object> params) {
        switch (channel.toUpperCase()) {
            case "ALIPAY":
                return (String) params.get("out_trade_no");
            case "WECHAT":
                return (String) params.get("out_trade_no");
            default:
                return null;
        }
    }

    /**
     * 从回调参数中获取支付状态
     */
    private boolean getPayStatusFromParams(String channel, Map<String, Object> params) {
        switch (channel.toUpperCase()) {
            case "ALIPAY":
                return "TRADE_SUCCESS".equals(params.get("trade_status"));
            case "WECHAT":
                return "SUCCESS".equals(params.get("result_code"));
            default:
                return false;
        }
    }

    /**
     * 从回调参数中获取第三方交易号
     */
    private String getTransactionIdFromParams(String channel, Map<String, Object> params) {
        switch (channel.toUpperCase()) {
            case "ALIPAY":
                return (String) params.get("trade_no");
            case "WECHAT":
                return (String) params.get("transaction_id");
            default:
                return null;
        }
    }

    /**
     * 从回调参数中获取失败代码
     */
    private String getFailureCodeFromParams(String channel, Map<String, Object> params) {
        switch (channel.toUpperCase()) {
            case "ALIPAY":
                return (String) params.get("sub_code");
            case "WECHAT":
                return (String) params.get("err_code");
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 从回调参数中获取失败消息
     */
    private String getFailureMsgFromParams(String channel, Map<String, Object> params) {
        switch (channel.toUpperCase()) {
            case "ALIPAY":
                return (String) params.get("sub_msg");
            case "WECHAT":
                return (String) params.get("err_code_des");
            default:
                return "支付失败";
        }
    }
}
