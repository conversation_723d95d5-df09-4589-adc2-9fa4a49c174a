package com.hncboy.chatgpt.front.controller.ai;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.db.entity.ai.AiMessageDO;
import com.hncboy.chatgpt.db.entity.ai.AiRoomDO;
import com.hncboy.chatgpt.db.service.ai.AiMessageService;
import com.hncboy.chatgpt.db.service.ai.AiRoomService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * AI对话控制器
 *
 * <AUTHOR>
 * @date 2023/3/22 16:30
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/chat")
@Tag(name = "AI对话", description = "AI对话房间和消息管理相关接口")
public class AiChatController {

    @Resource
    private AiRoomService aiRoomService;

    @Resource
    private AiMessageService aiMessageService;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    /**
     * 创建对话房间
     *
     * @param request     创建请求
     * @param httpRequest HTTP请求
     * @return 房间信息
     */
    @PostMapping("/rooms")
    @Operation(summary = "创建对话房间", description = "创建新的AI对话房间")
    public Result<AiRoomDO> createRoom(@Valid @RequestBody CreateRoomRequest request,
                                       HttpServletRequest httpRequest) {
        log.info("创建对话房间: request={}", request);
        
        try {
            // 检查用户是否存在
            if (!userBaseInfoService.existsById(request.getUserId())) {
                return Result.error("用户不存在");
            }
            
            // 创建房间
            AiRoomDO room = aiRoomService.createChatRoom(
                    request.getUserId(),
                    request.getTitle(),
                    request.getAbilityType(),
                    request.getBizScene(),
                    request.getAgentId()
            );
            
            if (room == null) {
                return Result.error("创建房间失败");
            }
            
            log.info("创建对话房间成功: roomId={}, userId={}", room.getId(), request.getUserId());
            return Result.success(room);
        } catch (Exception e) {
            log.error("创建对话房间失败: request={}", request, e);
            return Result.error("创建房间失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户房间列表
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param page        页码
     * @param size        页大小
     * @return 房间列表
     */
    @GetMapping("/rooms")
    @Operation(summary = "获取用户房间列表", description = "分页获取用户的对话房间列表")
    public Result<List<AiRoomDO>> getUserRooms(@Parameter(description = "用户ID") @RequestParam Integer userId,
                                                @Parameter(description = "能力类型") @RequestParam(required = false) String abilityType,
                                                @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
                                                @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取用户房间列表: userId={}, abilityType={}, page={}, size={}", userId, abilityType, page, size);
        
        try {
            Page<AiRoomDO> pageParam = new Page<>(page, size);
            List<AiRoomDO> rooms;
            
            if (StrUtil.isNotBlank(abilityType)) {
                rooms = aiRoomService.listByUserIdAndAbilityType(userId, abilityType);
            } else {
                rooms = aiRoomService.pageByUserId(pageParam, userId).getRecords();
            }
            
            log.info("获取用户房间列表成功: userId={}, count={}", userId, rooms.size());
            return Result.success(rooms);
        } catch (Exception e) {
            log.error("获取用户房间列表失败: userId={}", userId, e);
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取房间详情
     *
     * @param roomId 房间ID
     * @return 房间详情
     */
    @GetMapping("/rooms/{roomId}")
    @Operation(summary = "获取房间详情", description = "根据房间ID获取房间详细信息")
    public Result<AiRoomDO> getRoomDetail(@Parameter(description = "房间ID") @PathVariable Long roomId) {
        log.info("获取房间详情: roomId={}", roomId);
        
        try {
            AiRoomDO room = aiRoomService.getById(roomId);
            if (room == null) {
                return Result.error("房间不存在");
            }
            
            log.info("获取房间详情成功: roomId={}, title={}", roomId, room.getTitle());
            return Result.success(room);
        } catch (Exception e) {
            log.error("获取房间详情失败: roomId={}", roomId, e);
            return Result.error("获取房间详情失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息
     *
     * @param roomId      房间ID
     * @param request     消息请求
     * @param httpRequest HTTP请求
     * @return 消息响应
     */
    @PostMapping("/rooms/{roomId}/messages")
    @Operation(summary = "发送消息", description = "向指定房间发送消息")
    public Result<SendMessageResponse> sendMessage(@Parameter(description = "房间ID") @PathVariable Long roomId,
                                                   @Valid @RequestBody SendMessageRequest request,
                                                   HttpServletRequest httpRequest) {
        log.info("发送消息: roomId={}, request={}", roomId, request);
        
        try {
            // 检查房间是否存在
            AiRoomDO room = aiRoomService.getById(roomId);
            if (room == null) {
                return Result.error("房间不存在");
            }
            
            // 检查用户权限
            if (!room.getUserId().equals(request.getUserId())) {
                return Result.error("无权限访问该房间");
            }
            
            // 检查用户是否有足够的使用次数
            if (!userBaseInfoService.hasEnoughTimes(request.getUserId(), 1)) {
                return Result.error("使用次数不足，请充值");
            }
            
            // 创建用户消息
            AiMessageDO userMessage = aiMessageService.createUserMessage(roomId, request.getUserId(), request.getContent());
            if (userMessage == null) {
                return Result.error("创建用户消息失败");
            }
            
            // TODO: 调用AI服务生成回复
            String aiResponse = generateAiResponse(room, request.getContent());
            
            // 创建AI回复消息
            AiMessageDO aiMessage = aiMessageService.createAiMessage(roomId, request.getUserId(), aiResponse, userMessage.getId());
            if (aiMessage == null) {
                return Result.error("创建AI回复失败");
            }
            
            // 扣减用户使用次数
            userBaseInfoService.deductUserTimes(request.getUserId(), 1, 2); // 优先扣减免费次数
            
            // 更新房间最后消息信息
            aiRoomService.updateLastMessage(roomId, aiMessage.getCreateTime(), StrUtil.brief(aiResponse, 100));
            aiRoomService.incrementMessageCount(roomId);
            
            // 构建响应
            SendMessageResponse response = new SendMessageResponse();
            response.setUserMessage(userMessage);
            response.setAiMessage(aiMessage);
            
            log.info("发送消息成功: roomId={}, userMessageId={}, aiMessageId={}", roomId, userMessage.getId(), aiMessage.getId());
            return Result.success(response);
        } catch (Exception e) {
            log.error("发送消息失败: roomId={}, request={}", roomId, request, e);
            return Result.error("发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 获取消息历史
     *
     * @param roomId 房间ID
     * @param page   页码
     * @param size   页大小
     * @return 消息列表
     */
    @GetMapping("/rooms/{roomId}/messages")
    @Operation(summary = "获取消息历史", description = "分页获取房间的消息历史")
    public Result<List<AiMessageDO>> getMessages(@Parameter(description = "房间ID") @PathVariable Long roomId,
                                                  @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
                                                  @Parameter(description = "页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取消息历史: roomId={}, page={}, size={}", roomId, page, size);
        
        try {
            Page<AiMessageDO> pageParam = new Page<>(page, size);
            List<AiMessageDO> messages = aiMessageService.pageByRoomId(pageParam, roomId).getRecords();
            
            log.info("获取消息历史成功: roomId={}, count={}", roomId, messages.size());
            return Result.success(messages);
        } catch (Exception e) {
            log.error("获取消息历史失败: roomId={}", roomId, e);
            return Result.error("获取消息历史失败: " + e.getMessage());
        }
    }

    /**
     * 删除房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/rooms/{roomId}")
    @Operation(summary = "删除房间", description = "删除指定的对话房间")
    public Result<Void> deleteRoom(@Parameter(description = "房间ID") @PathVariable Long roomId,
                                   @Parameter(description = "用户ID") @RequestParam Integer userId) {
        log.info("删除房间: roomId={}, userId={}", roomId, userId);
        
        try {
            // 检查房间是否存在
            AiRoomDO room = aiRoomService.getById(roomId);
            if (room == null) {
                return Result.error("房间不存在");
            }
            
            // 检查用户权限
            if (!room.getUserId().equals(userId)) {
                return Result.error("无权限删除该房间");
            }
            
            // 删除房间消息
            aiMessageService.deleteByRoomId(roomId);
            
            // 删除房间
            aiRoomService.removeById(roomId);
            
            log.info("删除房间成功: roomId={}", roomId);
            return Result.success();
        } catch (Exception e) {
            log.error("删除房间失败: roomId={}", roomId, e);
            return Result.error("删除房间失败: " + e.getMessage());
        }
    }

    /**
     * 置顶房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 置顶结果
     */
    @PostMapping("/rooms/{roomId}/pin")
    @Operation(summary = "置顶房间", description = "置顶指定的对话房间")
    public Result<Void> pinRoom(@Parameter(description = "房间ID") @PathVariable Long roomId,
                                @Parameter(description = "用户ID") @RequestParam Integer userId) {
        log.info("置顶房间: roomId={}, userId={}", roomId, userId);
        
        try {
            boolean success = aiRoomService.pinRoom(roomId, userId);
            if (success) {
                log.info("置顶房间成功: roomId={}", roomId);
                return Result.success();
            } else {
                return Result.error("置顶房间失败");
            }
        } catch (Exception e) {
            log.error("置顶房间失败: roomId={}", roomId, e);
            return Result.error("置顶房间失败: " + e.getMessage());
        }
    }

    /**
     * 生成AI回复
     *
     * @param room    房间信息
     * @param content 用户消息内容
     * @return AI回复
     */
    private String generateAiResponse(AiRoomDO room, String content) {
        // TODO: 实现AI回复生成逻辑
        // 1. 获取房间上下文
        // 2. 构建提示词
        // 3. 调用AI模型
        // 4. 返回回复内容
        
        // 这里返回模拟回复
        return "这是AI的回复：" + content;
    }

    /**
     * 创建房间请求
     */
    public static class CreateRoomRequest {
        @NotNull(message = "用户ID不能为空")
        @Parameter(description = "用户ID")
        private Integer userId;
        
        @NotBlank(message = "房间标题不能为空")
        @Parameter(description = "房间标题")
        private String title;
        
        @NotBlank(message = "能力类型不能为空")
        @Parameter(description = "能力类型: CHAT-对话, DRAW-绘画, WRITE-写作, MUSIC-音乐")
        private String abilityType;
        
        @Parameter(description = "业务场景: tarot-塔罗, zns-智能社, chatoi-对话, ALL-通用")
        private String bizScene;
        
        @Parameter(description = "智能体ID")
        private Integer agentId;
        
        // getters and setters
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getAbilityType() { return abilityType; }
        public void setAbilityType(String abilityType) { this.abilityType = abilityType; }
        public String getBizScene() { return bizScene; }
        public void setBizScene(String bizScene) { this.bizScene = bizScene; }
        public Integer getAgentId() { return agentId; }
        public void setAgentId(Integer agentId) { this.agentId = agentId; }
        
        @Override
        public String toString() {
            return "CreateRoomRequest{" +
                    "userId=" + userId +
                    ", title='" + title + '\'' +
                    ", abilityType='" + abilityType + '\'' +
                    ", bizScene='" + bizScene + '\'' +
                    ", agentId=" + agentId +
                    '}';
        }
    }

    /**
     * 发送消息请求
     */
    public static class SendMessageRequest {
        @NotNull(message = "用户ID不能为空")
        @Parameter(description = "用户ID")
        private Integer userId;
        
        @NotBlank(message = "消息内容不能为空")
        @Parameter(description = "消息内容")
        private String content;
        
        @Parameter(description = "提示词")
        private String prompt;
        
        // getters and setters
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public String getPrompt() { return prompt; }
        public void setPrompt(String prompt) { this.prompt = prompt; }
        
        @Override
        public String toString() {
            return "SendMessageRequest{" +
                    "userId=" + userId +
                    ", content='" + StrUtil.brief(content, 50) + '\'' +
                    ", prompt='" + StrUtil.brief(prompt, 50) + '\'' +
                    '}';
        }
    }

    /**
     * 发送消息响应
     */
    public static class SendMessageResponse {
        @Parameter(description = "用户消息")
        private AiMessageDO userMessage;
        
        @Parameter(description = "AI回复消息")
        private AiMessageDO aiMessage;
        
        // getters and setters
        public AiMessageDO getUserMessage() { return userMessage; }
        public void setUserMessage(AiMessageDO userMessage) { this.userMessage = userMessage; }
        public AiMessageDO getAiMessage() { return aiMessage; }
        public void setAiMessage(AiMessageDO aiMessage) { this.aiMessage = aiMessage; }
    }
}
