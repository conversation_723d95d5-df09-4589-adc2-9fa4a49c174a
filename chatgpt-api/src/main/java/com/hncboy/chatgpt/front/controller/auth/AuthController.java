package com.hncboy.chatgpt.front.controller.auth;

import cn.hutool.core.util.StrUtil;
import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.common.validation.Phone;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 用户认证控制器
 *
 * <AUTHOR>
 * @date 2023/3/22 15:00
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Tag(name = "用户认证", description = "用户登录、注册、第三方登录等认证相关接口")
public class AuthController {

    @Resource
    private UserBaseInfoService userBaseInfoService;

    /**
     * 手机号登录
     *
     * @param request     登录请求
     * @param httpRequest HTTP请求
     * @return 登录结果
     */
    @PostMapping("/login/phone")
    @Operation(summary = "手机号登录", description = "通过手机号和验证码进行登录")
    public Result<LoginResponse> loginByPhone(@Valid @RequestBody PhoneLoginRequest request,
                                              HttpServletRequest httpRequest) {
        log.info("手机号登录: phone={}", request.getPhone());
        
        try {
            // TODO: 验证短信验证码
            
            // 查找或创建用户
            UserBaseInfoDO user = userBaseInfoService.findOrCreateByPhone(request.getPhone());
            if (user == null) {
                return Result.error("登录失败，请稍后重试");
            }
            
            // 更新登录信息
            String clientIp = getClientIp(httpRequest);
            user.updateLoginInfo(clientIp);
            userBaseInfoService.updateById(user);
            
            // 生成登录响应
            LoginResponse response = buildLoginResponse(user);
            
            log.info("手机号登录成功: userId={}, phone={}", user.getId(), request.getPhone());
            return Result.success(response);
        } catch (Exception e) {
            log.error("手机号登录失败: phone={}", request.getPhone(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 邮箱登录
     *
     * @param request     登录请求
     * @param httpRequest HTTP请求
     * @return 登录结果
     */
    @PostMapping("/login/email")
    @Operation(summary = "邮箱登录", description = "通过邮箱和验证码进行登录")
    public Result<LoginResponse> loginByEmail(@Valid @RequestBody EmailLoginRequest request,
                                              HttpServletRequest httpRequest) {
        log.info("邮箱登录: email={}", request.getEmail());
        
        try {
            // TODO: 验证邮箱验证码
            
            // 查找或创建用户
            UserBaseInfoDO user = userBaseInfoService.findOrCreateByEmail(request.getEmail());
            if (user == null) {
                return Result.error("登录失败，请稍后重试");
            }
            
            // 更新登录信息
            String clientIp = getClientIp(httpRequest);
            user.updateLoginInfo(clientIp);
            userBaseInfoService.updateById(user);
            
            // 生成登录响应
            LoginResponse response = buildLoginResponse(user);
            
            log.info("邮箱登录成功: userId={}, email={}", user.getId(), request.getEmail());
            return Result.success(response);
        } catch (Exception e) {
            log.error("邮箱登录失败: email={}", request.getEmail(), e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 发送短信验证码
     *
     * @param request 发送请求
     * @return 发送结果
     */
    @PostMapping("/sms/send")
    @Operation(summary = "发送短信验证码", description = "向指定手机号发送验证码")
    public Result<Void> sendSmsCode(@Valid @RequestBody SendSmsRequest request) {
        log.info("发送短信验证码: phone={}, type={}", request.getPhone(), request.getType());
        
        try {
            // TODO: 实现短信验证码发送逻辑
            // 1. 检查手机号格式
            // 2. 检查发送频率限制
            // 3. 生成验证码
            // 4. 调用短信服务发送
            // 5. 缓存验证码
            
            log.info("短信验证码发送成功: phone={}", request.getPhone());
            return Result.success();
        } catch (Exception e) {
            log.error("发送短信验证码失败: phone={}", request.getPhone(), e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     *
     * @param request 发送请求
     * @return 发送结果
     */
    @PostMapping("/email/send")
    @Operation(summary = "发送邮箱验证码", description = "向指定邮箱发送验证码")
    public Result<Void> sendEmailCode(@Valid @RequestBody SendEmailRequest request) {
        log.info("发送邮箱验证码: email={}, type={}", request.getEmail(), request.getType());
        
        try {
            // TODO: 实现邮箱验证码发送逻辑
            // 1. 检查邮箱格式
            // 2. 检查发送频率限制
            // 3. 生成验证码
            // 4. 调用邮件服务发送
            // 5. 缓存验证码
            
            log.info("邮箱验证码发送成功: email={}", request.getEmail());
            return Result.success();
        } catch (Exception e) {
            log.error("发送邮箱验证码失败: email={}", request.getEmail(), e);
            return Result.error("发送失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @param httpRequest HTTP请求
     * @return 登出结果
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户退出登录")
    public Result<Void> logout(HttpServletRequest httpRequest) {
        log.info("用户登出");
        
        try {
            // TODO: 实现登出逻辑
            // 1. 获取当前用户信息
            // 2. 清除登录状态
            // 3. 记录登出日志
            
            return Result.success();
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP
     *
     * @param request HTTP请求
     * @return 客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 构建登录响应
     *
     * @param user 用户信息
     * @return 登录响应
     */
    private LoginResponse buildLoginResponse(UserBaseInfoDO user) {
        LoginResponse response = new LoginResponse();
        response.setUserId(user.getId());
        response.setAccount(user.getAccount());
        response.setNickName(user.getNickName());
        response.setAvatarUrl(user.getAvatarUrl());
        response.setUserType(user.getUserType());
        response.setIsFirstLogin(user.isFirstLogin());
        
        // TODO: 生成JWT Token
        response.setToken("generated-jwt-token");
        response.setRefreshToken("generated-refresh-token");
        response.setExpiresIn(7200L); // 2小时
        
        return response;
    }

    /**
     * 手机号登录请求
     */
    public static class PhoneLoginRequest {
        @Phone
        @NotBlank(message = "手机号不能为空")
        @Parameter(description = "手机号")
        private String phone;
        
        @NotBlank(message = "验证码不能为空")
        @Parameter(description = "验证码")
        private String code;
        
        // getters and setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
    }

    /**
     * 邮箱登录请求
     */
    public static class EmailLoginRequest {
        @Email(message = "邮箱格式不正确")
        @NotBlank(message = "邮箱不能为空")
        @Parameter(description = "邮箱")
        private String email;
        
        @NotBlank(message = "验证码不能为空")
        @Parameter(description = "验证码")
        private String code;
        
        // getters and setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }
    }

    /**
     * 发送短信请求
     */
    public static class SendSmsRequest {
        @Phone
        @NotBlank(message = "手机号不能为空")
        @Parameter(description = "手机号")
        private String phone;
        
        @NotBlank(message = "类型不能为空")
        @Parameter(description = "类型: LOGIN-登录, REGISTER-注册, RESET_PASSWORD-重置密码")
        private String type;
        
        // getters and setters
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    /**
     * 发送邮箱请求
     */
    public static class SendEmailRequest {
        @Email(message = "邮箱格式不正确")
        @NotBlank(message = "邮箱不能为空")
        @Parameter(description = "邮箱")
        private String email;
        
        @NotBlank(message = "类型不能为空")
        @Parameter(description = "类型: LOGIN-登录, REGISTER-注册, RESET_PASSWORD-重置密码")
        private String type;
        
        // getters and setters
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    /**
     * 登录响应
     */
    public static class LoginResponse {
        @Parameter(description = "用户ID")
        private Integer userId;
        
        @Parameter(description = "账号")
        private String account;
        
        @Parameter(description = "昵称")
        private String nickName;
        
        @Parameter(description = "头像URL")
        private String avatarUrl;
        
        @Parameter(description = "用户类型")
        private String userType;
        
        @Parameter(description = "是否首次登录")
        private Boolean isFirstLogin;
        
        @Parameter(description = "访问令牌")
        private String token;
        
        @Parameter(description = "刷新令牌")
        private String refreshToken;
        
        @Parameter(description = "过期时间(秒)")
        private Long expiresIn;
        
        // getters and setters
        public Integer getUserId() { return userId; }
        public void setUserId(Integer userId) { this.userId = userId; }
        public String getAccount() { return account; }
        public void setAccount(String account) { this.account = account; }
        public String getNickName() { return nickName; }
        public void setNickName(String nickName) { this.nickName = nickName; }
        public String getAvatarUrl() { return avatarUrl; }
        public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
        public String getUserType() { return userType; }
        public void setUserType(String userType) { this.userType = userType; }
        public Boolean getIsFirstLogin() { return isFirstLogin; }
        public void setIsFirstLogin(Boolean isFirstLogin) { this.isFirstLogin = isFirstLogin; }
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
        public Long getExpiresIn() { return expiresIn; }
        public void setExpiresIn(Long expiresIn) { this.expiresIn = expiresIn; }
    }
}
