package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.pay.PayOrderDO;

import java.util.List;
import java.util.Map;

/**
 * 支付订单服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 15:45
 */
public interface PayOrderService extends IService<PayOrderDO> {

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 支付订单
     */
    PayOrderDO getByOrderNo(String orderNo);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @return 是否成功
     */
    boolean cancelOrder(String orderNo);

    /**
     * 处理支付回调
     *
     * @param channel 支付渠道
     * @param params  回调参数
     * @return 是否成功
     */
    boolean handlePaymentCallback(String channel, Map<String, Object> params);

    /**
     * 获取用户订单列表
     *
     * @param userId 用户ID
     * @param page   页码
     * @param size   页大小
     * @return 订单列表
     */
    List<PayOrderDO> listUserOrders(Integer userId, Integer page, Integer size);

    /**
     * 更新订单状态
     *
     * @param orderNo 订单号
     * @param status  状态
     * @return 是否成功
     */
    boolean updateOrderStatus(String orderNo, String status);

    /**
     * 完成支付
     *
     * @param orderNo       订单号
     * @param transactionId 第三方交易号
     * @param paidAmount    实付金额
     * @return 是否成功
     */
    boolean completePayment(String orderNo, String transactionId, java.math.BigDecimal paidAmount);

    /**
     * 支付失败
     *
     * @param orderNo     订单号
     * @param failureCode 失败代码
     * @param failureMsg  失败消息
     * @return 是否成功
     */
    boolean failPayment(String orderNo, String failureCode, String failureMsg);

    /**
     * 检查订单是否过期
     *
     * @param orderNo 订单号
     * @return 是否过期
     */
    boolean isOrderExpired(String orderNo);

    /**
     * 处理订单完成后的业务逻辑
     *
     * @param payOrder 支付订单
     * @return 是否成功
     */
    boolean processOrderCompletion(PayOrderDO payOrder);
}
