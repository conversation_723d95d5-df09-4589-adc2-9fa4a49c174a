package com.hncboy.chatgpt.db.entity.pay;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付渠道配置实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pay_channel_config")
@Schema(description = "支付渠道配置")
public class PayChannelConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 渠道编码
     */
    @TableField("channel_code")
    @Schema(description = "渠道编码")
    private String channelCode;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 渠道类型 ALIPAY:支付宝 WECHAT:微信 MOMO:越南momo SEPAY:越南sepay
     */
    @TableField("channel_type")
    @Schema(description = "渠道类型")
    private String channelType;

    /**
     * 支付方式 QR:二维码 H5:手机网页 APP:应用内 JSAPI:公众号
     */
    @TableField("pay_method")
    @Schema(description = "支付方式")
    private String payMethod;

    /**
     * 币种
     */
    @TableField("currency")
    @Schema(description = "币种")
    private String currency;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    @Schema(description = "商户ID")
    private String merchantId;

    /**
     * 应用ID
     */
    @TableField("app_id")
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 商户私钥
     */
    @TableField("private_key")
    @Schema(description = "商户私钥")
    private String privateKey;

    /**
     * 平台公钥
     */
    @TableField("public_key")
    @Schema(description = "平台公钥")
    private String publicKey;

    /**
     * API密钥
     */
    @TableField("api_key")
    @Schema(description = "API密钥")
    private String apiKey;

    /**
     * API密钥V3
     */
    @TableField("api_key_v3")
    @Schema(description = "API密钥V3")
    private String apiKeyV3;

    /**
     * 证书路径
     */
    @TableField("cert_path")
    @Schema(description = "证书路径")
    private String certPath;

    /**
     * 证书序列号
     */
    @TableField("cert_serial_no")
    @Schema(description = "证书序列号")
    private String certSerialNo;

    /**
     * 网关地址
     */
    @TableField("gateway_url")
    @Schema(description = "网关地址")
    private String gatewayUrl;

    /**
     * 异步通知地址
     */
    @TableField("notify_url")
    @Schema(description = "异步通知地址")
    private String notifyUrl;

    /**
     * 同步返回地址
     */
    @TableField("return_url")
    @Schema(description = "同步返回地址")
    private String returnUrl;

    /**
     * 签名类型 RSA RSA2 MD5
     */
    @TableField("sign_type")
    @Schema(description = "签名类型")
    private String signType;

    /**
     * 字符编码
     */
    @TableField("charset")
    @Schema(description = "字符编码")
    private String charset;

    /**
     * 最小支付金额
     */
    @TableField("min_amount")
    @Schema(description = "最小支付金额")
    private BigDecimal minAmount;

    /**
     * 最大支付金额
     */
    @TableField("max_amount")
    @Schema(description = "最大支付金额")
    private BigDecimal maxAmount;

    /**
     * 手续费率
     */
    @TableField("fee_rate")
    @Schema(description = "手续费率")
    private BigDecimal feeRate;

    /**
     * 固定手续费
     */
    @TableField("fixed_fee")
    @Schema(description = "固定手续费")
    private BigDecimal fixedFee;

    /**
     * 是否沙箱环境 1:是 0:否
     */
    @TableField("is_sandbox")
    @Schema(description = "是否沙箱环境")
    private Integer isSandbox;

    /**
     * 超时时间(分钟)
     */
    @TableField("timeout_minutes")
    @Schema(description = "超时时间")
    private Integer timeoutMinutes;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    @Schema(description = "重试次数")
    private Integer retryCount;

    /**
     * 权重(用于负载均衡)
     */
    @TableField("weight")
    @Schema(description = "权重")
    private Integer weight;

    /**
     * 优先级
     */
    @TableField("priority")
    @Schema(description = "优先级")
    private Integer priority;

    /**
     * 扩展配置(JSON)
     */
    @TableField("extra_config")
    @Schema(description = "扩展配置")
    private String extraConfig;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为沙箱环境
     *
     * @return true:沙箱环境 false:生产环境
     */
    public boolean isSandboxEnvironment() {
        return isSandbox != null && isSandbox == 1;
    }

    /**
     * 检查是否为支付宝渠道
     *
     * @return true:支付宝 false:其他渠道
     */
    public boolean isAlipayChannel() {
        return "ALIPAY".equals(channelType);
    }

    /**
     * 检查是否为微信渠道
     *
     * @return true:微信 false:其他渠道
     */
    public boolean isWechatChannel() {
        return "WECHAT".equals(channelType);
    }

    /**
     * 检查是否为越南momo渠道
     *
     * @return true:momo false:其他渠道
     */
    public boolean isMomoChannel() {
        return "MOMO".equals(channelType);
    }

    /**
     * 检查是否为越南sepay渠道
     *
     * @return true:sepay false:其他渠道
     */
    public boolean isSepayChannel() {
        return "SEPAY".equals(channelType);
    }

    /**
     * 检查金额是否在允许范围内
     *
     * @param amount 金额
     * @return true:在范围内 false:超出范围
     */
    public boolean isAmountInRange(BigDecimal amount) {
        if (amount == null) {
            return false;
        }
        if (minAmount != null && amount.compareTo(minAmount) < 0) {
            return false;
        }
        if (maxAmount != null && amount.compareTo(maxAmount) > 0) {
            return false;
        }
        return true;
    }

    /**
     * 计算手续费
     *
     * @param amount 支付金额
     * @return 手续费
     */
    public BigDecimal calculateFee(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal fee = BigDecimal.ZERO;
        
        // 计算比例手续费
        if (feeRate != null && feeRate.compareTo(BigDecimal.ZERO) > 0) {
            fee = fee.add(amount.multiply(feeRate).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        
        // 加上固定手续费
        if (fixedFee != null && fixedFee.compareTo(BigDecimal.ZERO) > 0) {
            fee = fee.add(fixedFee);
        }
        
        return fee;
    }

    /**
     * 计算实际到账金额
     *
     * @param amount 支付金额
     * @return 实际到账金额
     */
    public BigDecimal calculateActualAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal fee = calculateFee(amount);
        return amount.subtract(fee);
    }
}
