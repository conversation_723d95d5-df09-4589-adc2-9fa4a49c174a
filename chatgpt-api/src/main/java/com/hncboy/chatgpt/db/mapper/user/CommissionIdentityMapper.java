package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.CommissionIdentityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 分佣身份Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加分佣身份相关的自定义查询方法
 * 3. 支持时间有效性查询，start_time和end_time字段
 * 4. 支持分佣比例查询，percentage字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface CommissionIdentityMapper extends BaseMapper<CommissionIdentityDO> {

    /**
     * 根据用户信息ID查询分佣身份
     *
     * @param userInfoId 用户信息ID
     * @return 分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE user_info_id = #{userInfoId} ORDER BY create_time DESC LIMIT 1")
    CommissionIdentityDO selectByUserInfoId(@Param("userInfoId") Integer userInfoId);

    /**
     * 根据用户信息ID查询有效的分佣身份
     *
     * @param userInfoId 用户信息ID
     * @return 有效的分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE user_info_id = #{userInfoId} AND status = '0' " +
            "AND start_time <= NOW() AND end_time >= NOW() ORDER BY create_time DESC LIMIT 1")
    CommissionIdentityDO selectValidByUserInfoId(@Param("userInfoId") Integer userInfoId);

    /**
     * 根据openId查询分佣身份
     *
     * @param openId openId
     * @return 分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE open_id = #{openId} ORDER BY create_time DESC LIMIT 1")
    CommissionIdentityDO selectByOpenId(@Param("openId") String openId);

    /**
     * 根据openId查询有效的分佣身份
     *
     * @param openId openId
     * @return 有效的分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE open_id = #{openId} AND status = '0' " +
            "AND start_time <= NOW() AND end_time >= NOW() ORDER BY create_time DESC LIMIT 1")
    CommissionIdentityDO selectValidByOpenId(@Param("openId") String openId);

    /**
     * 根据邀请码查询分佣身份
     *
     * @param inviteCode 邀请码
     * @return 分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE invite_code = #{inviteCode}")
    CommissionIdentityDO selectByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 根据邀请码查询有效的分佣身份
     *
     * @param inviteCode 邀请码
     * @return 有效的分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE invite_code = #{inviteCode} AND status = '0' " +
            "AND start_time <= NOW() AND end_time >= NOW()")
    CommissionIdentityDO selectValidByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 根据类型查询分佣身份列表
     *
     * @param type 类型
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE type = #{type} ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByType(@Param("type") String type);

    /**
     * 根据编号查询分佣身份
     *
     * @param code 编号
     * @return 分佣身份
     */
    @Select("SELECT * FROM commission_identity WHERE code = #{code}")
    CommissionIdentityDO selectByCode(@Param("code") String code);

    /**
     * 根据状态查询分佣身份列表
     *
     * @param status 状态
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE status = #{status} ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByStatus(@Param("status") String status);

    /**
     * 查询有效的分佣身份列表
     *
     * @return 有效的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE status = '0' ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectValidIdentities();

    /**
     * 查询无效的分佣身份列表
     *
     * @return 无效的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE status = '1' ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectInvalidIdentities();

    /**
     * 查询在有效期内的分佣身份列表
     *
     * @return 在有效期内的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE start_time <= NOW() AND end_time >= NOW() ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectInValidPeriod();

    /**
     * 查询已过期的分佣身份列表
     *
     * @return 已过期的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE end_time < NOW() ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectExpiredIdentities();

    /**
     * 查询未生效的分佣身份列表
     *
     * @return 未生效的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE start_time > NOW() ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectNotEffectiveIdentities();

    /**
     * 查询可用的分佣身份列表(状态有效且在有效期内)
     *
     * @return 可用的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE status = '0' AND start_time <= NOW() AND end_time >= NOW() ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectAvailableIdentities();

    /**
     * 根据分佣比例查询分佣身份列表
     *
     * @param percentage 分佣比例
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE percentage = #{percentage} ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByPercentage(@Param("percentage") Integer percentage);

    /**
     * 根据分佣比例范围查询分佣身份列表
     *
     * @param minPercentage 最小分佣比例
     * @param maxPercentage 最大分佣比例
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE percentage BETWEEN #{minPercentage} AND #{maxPercentage} ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByPercentageRange(@Param("minPercentage") Integer minPercentage, @Param("maxPercentage") Integer maxPercentage);

    /**
     * 根据姓名查询分佣身份列表
     *
     * @param name 姓名
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByName(@Param("name") String name);

    /**
     * 根据手机号查询分佣身份列表
     *
     * @param phone 手机号
     * @return 分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE phone = #{phone} ORDER BY create_time DESC")
    List<CommissionIdentityDO> selectByPhone(@Param("phone") String phone);

    /**
     * 查询即将过期的分佣身份列表(7天内过期)
     *
     * @return 即将过期的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity WHERE status = '0' AND end_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) ORDER BY end_time ASC")
    List<CommissionIdentityDO> selectExpiringSoonIdentities();

    /**
     * 查询最近创建的分佣身份列表
     *
     * @param limit 限制数量
     * @return 最近创建的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity ORDER BY create_time DESC LIMIT #{limit}")
    List<CommissionIdentityDO> selectRecentIdentities(@Param("limit") Integer limit);

    /**
     * 查询分佣比例最高的分佣身份列表
     *
     * @param limit 限制数量
     * @return 分佣比例最高的分佣身份列表
     */
    @Select("SELECT * FROM commission_identity ORDER BY percentage DESC LIMIT #{limit}")
    List<CommissionIdentityDO> selectHighestPercentageIdentities(@Param("limit") Integer limit);

    /**
     * 统计分佣身份总数
     *
     * @return 分佣身份总数
     */
    @Select("SELECT COUNT(*) FROM commission_identity")
    Long countTotalIdentities();

    /**
     * 根据状态统计分佣身份数量
     *
     * @param status 状态
     * @return 分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE status = #{status}")
    Long countByStatus(@Param("status") String status);

    /**
     * 统计有效的分佣身份数量
     *
     * @return 有效的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE status = '0'")
    Long countValidIdentities();

    /**
     * 统计可用的分佣身份数量(状态有效且在有效期内)
     *
     * @return 可用的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE status = '0' AND start_time <= NOW() AND end_time >= NOW()")
    Long countAvailableIdentities();

    /**
     * 统计已过期的分佣身份数量
     *
     * @return 已过期的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE end_time < NOW()")
    Long countExpiredIdentities();

    /**
     * 统计即将过期的分佣身份数量(7天内过期)
     *
     * @return 即将过期的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE status = '0' AND end_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)")
    Long countExpiringSoonIdentities();

    /**
     * 根据类型统计分佣身份数量
     *
     * @param type 类型
     * @return 分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE type = #{type}")
    Long countByType(@Param("type") String type);

    /**
     * 根据分佣比例统计分佣身份数量
     *
     * @param percentage 分佣比例
     * @return 分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE percentage = #{percentage}")
    Long countByPercentage(@Param("percentage") Integer percentage);

    /**
     * 统计有邀请码的分佣身份数量
     *
     * @return 有邀请码的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE invite_code IS NOT NULL AND invite_code != ''")
    Long countWithInviteCode();

    /**
     * 统计有微信公众号链接的分佣身份数量
     *
     * @return 有微信公众号链接的分佣身份数量
     */
    @Select("SELECT COUNT(*) FROM commission_identity WHERE wx_mp_url IS NOT NULL AND wx_mp_url != ''")
    Long countWithWxMpUrl();

    /**
     * 查询平均分佣比例
     *
     * @return 平均分佣比例
     */
    @Select("SELECT IFNULL(AVG(percentage), 0) FROM commission_identity WHERE status = '0'")
    Double selectAveragePercentage();

    /**
     * 查询最高分佣比例
     *
     * @return 最高分佣比例
     */
    @Select("SELECT IFNULL(MAX(percentage), 0) FROM commission_identity WHERE status = '0'")
    Integer selectMaxPercentage();

    /**
     * 查询最低分佣比例
     *
     * @return 最低分佣比例
     */
    @Select("SELECT IFNULL(MIN(percentage), 0) FROM commission_identity WHERE status = '0'")
    Integer selectMinPercentage();
}
