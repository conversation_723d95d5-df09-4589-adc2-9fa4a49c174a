package com.hncboy.chatgpt.db.entity.tarot;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 塔罗牌含义实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tarot_card_meaning")
public class TarotCardMeaningDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 牌名称
     */
    @TableField("card_name")
    private String cardName;

    /**
     * 牌英文名称
     */
    @TableField("english_name")
    private String englishName;

    /**
     * 牌编号
     */
    @TableField("card_number")
    private Integer cardNumber;

    /**
     * 牌组类型 MAJOR:大阿卡纳 MINOR:小阿卡纳
     */
    @TableField("card_type")
    private String cardType;

    /**
     * 花色(小阿卡纳) WANDS:权杖 CUPS:圣杯 SWORDS:宝剑 PENTACLES:星币
     */
    @TableField("suit")
    private String suit;

    /**
     * 牌面图片
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 正位关键词
     */
    @TableField("upright_keywords")
    private String uprightKeywords;

    /**
     * 逆位关键词
     */
    @TableField("reversed_keywords")
    private String reversedKeywords;

    /**
     * 正位含义
     */
    @TableField("upright_meaning")
    private String uprightMeaning;

    /**
     * 逆位含义
     */
    @TableField("reversed_meaning")
    private String reversedMeaning;

    /**
     * 爱情含义
     */
    @TableField("love_meaning")
    private String loveMeaning;

    /**
     * 事业含义
     */
    @TableField("career_meaning")
    private String careerMeaning;

    /**
     * 财运含义
     */
    @TableField("money_meaning")
    private String moneyMeaning;

    /**
     * 健康含义
     */
    @TableField("health_meaning")
    private String healthMeaning;

    /**
     * 学业含义
     */
    @TableField("study_meaning")
    private String studyMeaning;

    /**
     * 人际关系含义
     */
    @TableField("relationship_meaning")
    private String relationshipMeaning;

    /**
     * 精神层面含义
     */
    @TableField("spiritual_meaning")
    private String spiritualMeaning;

    /**
     * 建议
     */
    @TableField("advice")
    private String advice;

    /**
     * 象征意义
     */
    @TableField("symbolism")
    private String symbolism;

    /**
     * 元素属性
     */
    @TableField("element")
    private String element;

    /**
     * 占星对应
     */
    @TableField("astrology")
    private String astrology;

    /**
     * 数字含义
     */
    @TableField("numerology")
    private String numerology;

    /**
     * 语言
     */
    @TableField("language")
    private String language;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为大阿卡纳
     *
     * @return true:大阿卡纳 false:小阿卡纳
     */
    public boolean isMajorArcana() {
        return "MAJOR".equals(cardType);
    }

    /**
     * 检查是否为小阿卡纳
     *
     * @return true:小阿卡纳 false:大阿卡纳
     */
    public boolean isMinorArcana() {
        return "MINOR".equals(cardType);
    }

    /**
     * 获取花色描述
     *
     * @return 花色描述
     */
    public String getSuitDesc() {
        if (suit == null) {
            return "";
        }
        switch (suit) {
            case "WANDS":
                return "权杖";
            case "CUPS":
                return "圣杯";
            case "SWORDS":
                return "宝剑";
            case "PENTACLES":
                return "星币";
            default:
                return suit;
        }
    }

    /**
     * 根据问题类型获取对应含义
     *
     * @param questionType 问题类型
     * @return 对应含义
     */
    public String getMeaningByQuestionType(String questionType) {
        if (questionType == null) {
            return uprightMeaning;
        }
        switch (questionType.toLowerCase()) {
            case "love":
                return loveMeaning;
            case "career":
                return careerMeaning;
            case "money":
                return moneyMeaning;
            case "health":
                return healthMeaning;
            case "study":
                return studyMeaning;
            case "relationship":
                return relationshipMeaning;
            case "spiritual":
                return spiritualMeaning;
            default:
                return uprightMeaning;
        }
    }
}
