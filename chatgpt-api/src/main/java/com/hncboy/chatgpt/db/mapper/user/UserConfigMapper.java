package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户配置Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加用户配置相关的自定义查询方法
 * 3. 支持多种配置类型查询：系统配置、用户配置、偏好设置、安全配置
 * 4. 支持业务场景查询，biz_scene字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserConfigMapper extends BaseMapper<UserConfigDO> {

    /**
     * 根据用户ID查询用户配置列表
     *
     * @param userId 用户ID
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和配置键查询用户配置
     *
     * @param userId 用户ID
     * @param configKey 配置键
     * @return 用户配置
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND config_key = #{configKey} AND deleted = 0")
    UserConfigDO selectByUserIdAndConfigKey(@Param("userId") Integer userId, @Param("configKey") String configKey);

    /**
     * 根据用户ID和配置类型查询用户配置列表
     *
     * @param userId 用户ID
     * @param configType 配置类型
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND config_type = #{configType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByUserIdAndConfigType(@Param("userId") Integer userId, @Param("configType") String configType);

    /**
     * 根据用户ID和业务场景查询用户配置列表
     *
     * @param userId 用户ID
     * @param bizScene 业务场景
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND (biz_scene = #{bizScene} OR biz_scene = 'ALL') AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByUserIdAndBizScene(@Param("userId") Integer userId, @Param("bizScene") String bizScene);

    /**
     * 根据用户ID和配置分组查询用户配置列表
     *
     * @param userId 用户ID
     * @param configGroup 配置分组
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND config_group = #{configGroup} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByUserIdAndConfigGroup(@Param("userId") Integer userId, @Param("configGroup") String configGroup);

    /**
     * 根据配置键查询用户配置列表
     *
     * @param configKey 配置键
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_key = #{configKey} AND deleted = 0 ORDER BY create_time DESC")
    List<UserConfigDO> selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 根据配置类型查询用户配置列表
     *
     * @param configType 配置类型
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_type = #{configType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByConfigType(@Param("configType") String configType);

    /**
     * 根据业务场景查询用户配置列表
     *
     * @param bizScene 业务场景
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE (biz_scene = #{bizScene} OR biz_scene = 'ALL') AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByBizScene(@Param("bizScene") String bizScene);

    /**
     * 根据配置分组查询用户配置列表
     *
     * @param configGroup 配置分组
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_group = #{configGroup} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByConfigGroup(@Param("configGroup") String configGroup);

    /**
     * 根据状态查询用户配置列表
     *
     * @param status 状态
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE status = #{status} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByStatus(@Param("status") Integer status);

    /**
     * 查询启用状态的用户配置列表
     *
     * @return 启用状态的用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE status = 1 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectEnabledConfigs();

    /**
     * 查询禁用状态的用户配置列表
     *
     * @return 禁用状态的用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE status = 0 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectDisabledConfigs();

    /**
     * 查询系统配置列表
     *
     * @return 系统配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_type = 'SYSTEM' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectSystemConfigs();

    /**
     * 查询用户配置列表
     *
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_type = 'USER' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectUserConfigs();

    /**
     * 查询偏好设置列表
     *
     * @return 偏好设置列表
     */
    @Select("SELECT * FROM user_config WHERE config_type = 'PREFERENCE' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectPreferenceConfigs();

    /**
     * 查询安全配置列表
     *
     * @return 安全配置列表
     */
    @Select("SELECT * FROM user_config WHERE config_type = 'SECURITY' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectSecurityConfigs();

    /**
     * 查询敏感配置列表
     *
     * @return 敏感配置列表
     */
    @Select("SELECT * FROM user_config WHERE is_sensitive = 1 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectSensitiveConfigs();

    /**
     * 查询非敏感配置列表
     *
     * @return 非敏感配置列表
     */
    @Select("SELECT * FROM user_config WHERE is_sensitive = 0 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectNonSensitiveConfigs();

    /**
     * 根据用户ID查询敏感配置列表
     *
     * @param userId 用户ID
     * @return 敏感配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND is_sensitive = 1 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectSensitiveConfigsByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID查询非敏感配置列表
     *
     * @param userId 用户ID
     * @return 非敏感配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND is_sensitive = 0 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectNonSensitiveConfigsByUserId(@Param("userId") Integer userId);

    /**
     * 根据配置版本查询用户配置列表
     *
     * @param version 配置版本
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE version = #{version} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByVersion(@Param("version") String version);

    /**
     * 根据用户ID和配置版本查询用户配置列表
     *
     * @param userId 用户ID
     * @param version 配置版本
     * @return 用户配置列表
     */
    @Select("SELECT * FROM user_config WHERE user_id = #{userId} AND version = #{version} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserConfigDO> selectByUserIdAndVersion(@Param("userId") Integer userId, @Param("version") String version);

    /**
     * 统计用户配置总数
     *
     * @return 用户配置总数
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE deleted = 0")
    Long countTotalConfigs();

    /**
     * 根据用户ID统计用户配置数量
     *
     * @param userId 用户ID
     * @return 用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE user_id = #{userId} AND deleted = 0")
    Long countByUserId(@Param("userId") Integer userId);

    /**
     * 根据配置类型统计用户配置数量
     *
     * @param configType 配置类型
     * @return 用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE config_type = #{configType} AND deleted = 0")
    Long countByConfigType(@Param("configType") String configType);

    /**
     * 根据业务场景统计用户配置数量
     *
     * @param bizScene 业务场景
     * @return 用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE (biz_scene = #{bizScene} OR biz_scene = 'ALL') AND deleted = 0")
    Long countByBizScene(@Param("bizScene") String bizScene);

    /**
     * 统计启用状态的用户配置数量
     *
     * @return 启用状态的用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE status = 1 AND deleted = 0")
    Long countEnabledConfigs();

    /**
     * 统计敏感配置数量
     *
     * @return 敏感配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE is_sensitive = 1 AND deleted = 0")
    Long countSensitiveConfigs();

    /**
     * 根据用户ID和配置类型统计用户配置数量
     *
     * @param userId 用户ID
     * @param configType 配置类型
     * @return 用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE user_id = #{userId} AND config_type = #{configType} AND deleted = 0")
    Long countByUserIdAndConfigType(@Param("userId") Integer userId, @Param("configType") String configType);

    /**
     * 根据用户ID和业务场景统计用户配置数量
     *
     * @param userId 用户ID
     * @param bizScene 业务场景
     * @return 用户配置数量
     */
    @Select("SELECT COUNT(*) FROM user_config WHERE user_id = #{userId} AND (biz_scene = #{bizScene} OR biz_scene = 'ALL') AND deleted = 0")
    Long countByUserIdAndBizScene(@Param("userId") Integer userId, @Param("bizScene") String bizScene);
}
