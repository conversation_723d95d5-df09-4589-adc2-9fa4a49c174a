package com.hncboy.chatgpt.db.entity.withdraw;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现交易记录实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_transaction")
public class WithdrawTransactionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易流水号
     */
    @TableField("transaction_no")
    private String transactionNo;

    /**
     * 提现申请ID
     */
    @TableField("application_id")
    private Long applicationId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 交易类型 FREEZE:冻结 UNFREEZE:解冻 DEDUCT:扣减 REFUND:退款
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 交易金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 交易前余额
     */
    @TableField("balance_before")
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    /**
     * 冻结金额变化
     */
    @TableField("frozen_change")
    private BigDecimal frozenChange;

    /**
     * 交易前冻结金额
     */
    @TableField("frozen_before")
    private BigDecimal frozenBefore;

    /**
     * 交易后冻结金额
     */
    @TableField("frozen_after")
    private BigDecimal frozenAfter;

    /**
     * 交易状态 SUCCESS:成功 FAILED:失败 PENDING:处理中
     */
    @TableField("status")
    private String status;

    /**
     * 交易描述
     */
    @TableField("description")
    private String description;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Integer operatorId;

    /**
     * 操作人类型 SYSTEM:系统 ADMIN:管理员 USER:用户
     */
    @TableField("operator_type")
    private String operatorType;

    /**
     * 关联订单号
     */
    @TableField("related_order_no")
    private String relatedOrderNo;

    /**
     * 第三方交易号
     */
    @TableField("third_party_trade_no")
    private String thirdPartyTradeNo;

    /**
     * 交易时间
     */
    @TableField("transaction_time")
    private LocalDateTime transactionTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 检查交易是否成功
     *
     * @return true:成功 false:失败
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 检查交易是否失败
     *
     * @return true:失败 false:成功
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查交易是否处理中
     *
     * @return true:处理中 false:已完成
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否为冻结交易
     *
     * @return true:冻结交易 false:其他交易
     */
    public boolean isFreezeTransaction() {
        return "FREEZE".equals(transactionType);
    }

    /**
     * 检查是否为解冻交易
     *
     * @return true:解冻交易 false:其他交易
     */
    public boolean isUnfreezeTransaction() {
        return "UNFREEZE".equals(transactionType);
    }

    /**
     * 检查是否为扣减交易
     *
     * @return true:扣减交易 false:其他交易
     */
    public boolean isDeductTransaction() {
        return "DEDUCT".equals(transactionType);
    }

    /**
     * 检查是否为退款交易
     *
     * @return true:退款交易 false:其他交易
     */
    public boolean isRefundTransaction() {
        return "REFUND".equals(transactionType);
    }

    /**
     * 检查是否为系统操作
     *
     * @return true:系统操作 false:人工操作
     */
    public boolean isSystemOperation() {
        return "SYSTEM".equals(operatorType);
    }

    /**
     * 检查是否为管理员操作
     *
     * @return true:管理员操作 false:其他操作
     */
    public boolean isAdminOperation() {
        return "ADMIN".equals(operatorType);
    }

    /**
     * 检查是否为用户操作
     *
     * @return true:用户操作 false:其他操作
     */
    public boolean isUserOperation() {
        return "USER".equals(operatorType);
    }

    /**
     * 获取交易类型描述
     *
     * @return 交易类型描述
     */
    public String getTransactionTypeDesc() {
        if (transactionType == null) {
            return "未知";
        }
        switch (transactionType) {
            case "FREEZE":
                return "冻结";
            case "UNFREEZE":
                return "解冻";
            case "DEDUCT":
                return "扣减";
            case "REFUND":
                return "退款";
            default:
                return transactionType;
        }
    }
}
