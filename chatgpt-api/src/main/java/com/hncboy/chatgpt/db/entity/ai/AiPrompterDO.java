package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI提示词实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_prompter")
public class AiPrompterDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 提示词标题
     */
    @TableField("title")
    private String title;

    /**
     * 提示词内容
     */
    @TableField("content")
    private String content;

    /**
     * 提示词描述
     */
    @TableField("description")
    private String description;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    private String abilityType;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 标签(JSON数组)
     */
    @TableField("tags")
    private String tags;

    /**
     * 语言
     */
    @TableField("language")
    private String language;

    /**
     * 是否公开 1:公开 0:私有
     */
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 创建者ID
     */
    @TableField("creator_id")
    private Integer creatorId;

    /**
     * 使用次数
     */
    @TableField("use_count")
    private Long useCount;

    /**
     * 点赞数
     */
    @TableField("like_count")
    private Long likeCount;

    /**
     * 收藏数
     */
    @TableField("favorite_count")
    private Long favoriteCount;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    private Integer sortWeight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否公开
     *
     * @return true:公开 false:私有
     */
    public boolean isPublicPrompter() {
        return isPublic != null && isPublic == 1;
    }

    /**
     * 增加使用次数
     */
    public void incrementUseCount() {
        this.useCount = (this.useCount == null ? 0 : this.useCount) + 1;
    }

    /**
     * 增加点赞数
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
    }

    /**
     * 减少点赞数
     */
    public void decrementLikeCount() {
        this.likeCount = (this.likeCount == null || this.likeCount <= 0) ? 0 : this.likeCount - 1;
    }

    /**
     * 增加收藏数
     */
    public void incrementFavoriteCount() {
        this.favoriteCount = (this.favoriteCount == null ? 0 : this.favoriteCount) + 1;
    }

    /**
     * 减少收藏数
     */
    public void decrementFavoriteCount() {
        this.favoriteCount = (this.favoriteCount == null || this.favoriteCount <= 0) ? 0 : this.favoriteCount - 1;
    }
}
