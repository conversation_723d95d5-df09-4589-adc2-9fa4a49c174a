package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户联合登录实体类
 *
 * 重构说明:
 * 1. 合并users表和wx_user_info表，统一管理第三方登录信息
 * 2. 保留推荐关系，referrer_id和lucky_coins字段从users表迁移
 * 3. 支持JustAuth第三方登录框架，统一OAuth认证流程
 * 4. 支持多种登录方式：微信、Google、Facebook、手机、邮箱、指纹等
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_joint_login")
@Schema(description = "用户联合登录")
public class UserJointLoginDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 用户ID(关联user_base_info.id)
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 登录类型 WECHAT:微信 GOOGLE:谷歌 FACEBOOK:脸书 PHONE:手机 EMAIL:邮箱 FINGERPRINT:指纹
     */
    @TableField("login_type")
    @Schema(description = "登录类型")
    private String loginType;

    /**
     * 第三方唯一标识
     */
    @TableField("third_party_id")
    @Schema(description = "第三方唯一标识")
    private String thirdPartyId;

    /**
     * 第三方用户名
     */
    @TableField("third_party_username")
    @Schema(description = "第三方用户名")
    private String thirdPartyUsername;

    /**
     * 第三方邮箱
     */
    @TableField("third_party_email")
    @Schema(description = "第三方邮箱")
    private String thirdPartyEmail;

    /**
     * 第三方头像
     */
    @TableField("third_party_avatar")
    @Schema(description = "第三方头像")
    private String thirdPartyAvatar;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    @Schema(description = "访问令牌")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    @Schema(description = "刷新令牌")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @TableField("expires_time")
    @Schema(description = "令牌过期时间")
    private LocalDateTime expiresTime;

    /**
     * 联合ID(微信unionId等)
     */
    @TableField("union_id")
    @Schema(description = "联合ID")
    private String unionId;

    /**
     * 应用ID
     */
    @TableField("app_id")
    @Schema(description = "应用ID")
    private String appId;

    /**
     * 推荐人ID(从users表迁移)
     */
    @TableField("referrer_id")
    @Schema(description = "推荐人ID")
    private Long referrerId;

    /**
     * 幸运币(从users表迁移)
     */
    @TableField("lucky_coins")
    @Schema(description = "幸运币")
    private Long luckyCoins;

    /**
     * 额外数据(JSON格式)
     */
    @TableField("extra_data")
    @Schema(description = "额外数据")
    private String extraData;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    @TableField("login_count")
    @Schema(description = "登录次数")
    private Integer loginCount;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查令牌是否过期
     *
     * @return true:已过期 false:未过期
     */
    public boolean isTokenExpired() {
        return expiresTime != null && expiresTime.isBefore(LocalDateTime.now());
    }

    /**
     * 检查状态是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo() {
        this.lastLoginTime = LocalDateTime.now();
        this.loginCount = (this.loginCount == null ? 0 : this.loginCount) + 1;
    }

    /**
     * 检查是否为微信登录
     *
     * @return true:微信登录 false:其他登录
     */
    public boolean isWechatLogin() {
        return "WECHAT".equals(loginType);
    }

    /**
     * 检查是否为Google登录
     *
     * @return true:Google登录 false:其他登录
     */
    public boolean isGoogleLogin() {
        return "GOOGLE".equals(loginType);
    }

    /**
     * 检查是否为Facebook登录
     *
     * @return true:Facebook登录 false:其他登录
     */
    public boolean isFacebookLogin() {
        return "FACEBOOK".equals(loginType);
    }

    /**
     * 检查是否为手机登录
     *
     * @return true:手机登录 false:其他登录
     */
    public boolean isPhoneLogin() {
        return "PHONE".equals(loginType);
    }

    /**
     * 检查是否为邮箱登录
     *
     * @return true:邮箱登录 false:其他登录
     */
    public boolean isEmailLogin() {
        return "EMAIL".equals(loginType);
    }

    /**
     * 检查是否为指纹登录
     *
     * @return true:指纹登录 false:其他登录
     */
    public boolean isFingerprintLogin() {
        return "FINGERPRINT".equals(loginType);
    }

    /**
     * 检查是否有推荐人
     *
     * @return true:有推荐人 false:无推荐人
     */
    public boolean hasReferrer() {
        return referrerId != null && referrerId > 0;
    }

    /**
     * 检查是否有幸运币
     *
     * @return true:有幸运币 false:无幸运币
     */
    public boolean hasLuckyCoins() {
        return luckyCoins != null && luckyCoins > 0;
    }

    /**
     * 更新令牌信息
     *
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresTime 过期时间
     */
    public void updateTokenInfo(String accessToken, String refreshToken, LocalDateTime expiresTime) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresTime = expiresTime;
    }

    /**
     * 获取登录类型描述
     *
     * @return 登录类型描述
     */
    public String getLoginTypeDescription() {
        if (loginType == null) {
            return "未知";
        }
        switch (loginType) {
            case "WECHAT":
                return "微信";
            case "GOOGLE":
                return "谷歌";
            case "FACEBOOK":
                return "脸书";
            case "PHONE":
                return "手机";
            case "EMAIL":
                return "邮箱";
            case "FINGERPRINT":
                return "指纹";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "启用";
            case 0:
                return "禁用";
            default:
                return "未知";
        }
    }

    /**
     * 设置为启用状态
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 设置为禁用状态
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 增加幸运币
     *
     * @param count 增加数量
     */
    public void addLuckyCoins(int count) {
        if (count > 0) {
            this.luckyCoins = (this.luckyCoins != null ? this.luckyCoins : 0L) + count;
        }
    }

    /**
     * 扣减幸运币
     *
     * @param count 扣减数量
     * @return 是否成功
     */
    public boolean deductLuckyCoins(int count) {
        if (count <= 0) {
            return false;
        }

        long currentCoins = this.luckyCoins != null ? this.luckyCoins : 0L;
        if (currentCoins < count) {
            return false;
        }

        this.luckyCoins = currentCoins - count;
        return true;
    }
}
