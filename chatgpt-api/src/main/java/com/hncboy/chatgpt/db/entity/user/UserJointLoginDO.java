package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户联合登录实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_joint_login")
public class UserJointLoginDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID(关联user_base_info.id)
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 登录类型 WECHAT:微信 GOOGLE:谷歌 FACEBOOK:脸书 PHONE:手机 EMAIL:邮箱 FINGERPRINT:指纹
     */
    @TableField("login_type")
    private String loginType;

    /**
     * 第三方唯一标识
     */
    @TableField("third_party_id")
    private String thirdPartyId;

    /**
     * 第三方用户名
     */
    @TableField("third_party_username")
    private String thirdPartyUsername;

    /**
     * 第三方邮箱
     */
    @TableField("third_party_email")
    private String thirdPartyEmail;

    /**
     * 第三方头像
     */
    @TableField("third_party_avatar")
    private String thirdPartyAvatar;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @TableField("expires_time")
    private LocalDateTime expiresTime;

    /**
     * 联合ID(微信unionId等)
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 应用ID
     */
    @TableField("app_id")
    private String appId;

    /**
     * 推荐人ID(从users表迁移)
     */
    @TableField("referrer_id")
    private Long referrerId;

    /**
     * 幸运币(从users表迁移)
     */
    @TableField("lucky_coins")
    private Long luckyCoins;

    /**
     * 额外数据(JSON格式)
     */
    @TableField("extra_data")
    private String extraData;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 登录次数
     */
    @TableField("login_count")
    private Integer loginCount;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查令牌是否过期
     *
     * @return true:已过期 false:未过期
     */
    public boolean isTokenExpired() {
        return expiresTime != null && expiresTime.isBefore(LocalDateTime.now());
    }

    /**
     * 检查状态是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo() {
        this.lastLoginTime = LocalDateTime.now();
        this.loginCount = (this.loginCount == null ? 0 : this.loginCount) + 1;
    }
}
