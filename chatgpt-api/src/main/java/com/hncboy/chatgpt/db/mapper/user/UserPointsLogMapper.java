package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserPointsLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户积分日志Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加积分日志相关的自定义查询方法
 * 3. 支持多种币种类型查询：积分、塔罗币
 * 4. 支持多种业务类型查询：签到、支付、退款、塔罗解读
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserPointsLogMapper extends BaseMapper<UserPointsLogDO> {

    /**
     * 根据用户ID查询积分日志列表
     *
     * @param userId 用户ID
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和变动类型查询积分日志列表
     *
     * @param userId 用户ID
     * @param pointsType 变动类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} AND points_type = #{pointsType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByUserIdAndPointsType(@Param("userId") Integer userId, @Param("pointsType") String pointsType);

    /**
     * 根据用户ID和币种类型查询积分日志列表
     *
     * @param userId 用户ID
     * @param currencyType 币种类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} AND currency_type = #{currencyType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByUserIdAndCurrencyType(@Param("userId") Integer userId, @Param("currencyType") String currencyType);

    /**
     * 根据用户ID和业务类型查询积分日志列表
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} AND business_type = #{businessType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByUserIdAndBusinessType(@Param("userId") Integer userId, @Param("businessType") String businessType);

    /**
     * 根据关联订单查询积分日志列表
     *
     * @param relOrder 关联订单
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE rel_order = #{relOrder} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByRelOrder(@Param("relOrder") String relOrder);

    /**
     * 根据变动类型查询积分日志列表
     *
     * @param pointsType 变动类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE points_type = #{pointsType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByPointsType(@Param("pointsType") String pointsType);

    /**
     * 根据币种类型查询积分日志列表
     *
     * @param currencyType 币种类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE currency_type = #{currencyType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByCurrencyType(@Param("currencyType") String currencyType);

    /**
     * 根据业务类型查询积分日志列表
     *
     * @param businessType 业务类型
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE business_type = #{businessType} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByBusinessType(@Param("businessType") String businessType);

    /**
     * 查询获得类型的积分日志列表
     *
     * @return 获得类型的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE points_type = 'EARN' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectEarnLogs();

    /**
     * 查询消费类型的积分日志列表
     *
     * @return 消费类型的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE points_type = 'CONSUME' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectConsumeLogs();

    /**
     * 查询塔罗币日志列表
     *
     * @return 塔罗币日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE currency_type = 'POINTS' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectTarotCoinsLogs();

    /**
     * 查询积分日志列表
     *
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE currency_type = 'COINS' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectCoinsLogs();

    /**
     * 查询签到业务的积分日志列表
     *
     * @return 签到业务的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE business_type = 'SIGN' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectSignLogs();

    /**
     * 查询支付业务的积分日志列表
     *
     * @return 支付业务的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE business_type = 'PAY' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectPayLogs();

    /**
     * 查询退款业务的积分日志列表
     *
     * @return 退款业务的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE business_type = 'REFUND' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectRefundLogs();

    /**
     * 查询塔罗解读业务的积分日志列表
     *
     * @return 塔罗解读业务的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE business_type = 'TAROT' ORDER BY create_time DESC")
    List<UserPointsLogDO> selectTarotLogs();

    /**
     * 根据用户ID查询最近的积分日志
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT #{limit}")
    List<UserPointsLogDO> selectRecentLogsByUserId(@Param("userId") Integer userId, @Param("limit") Integer limit);

    /**
     * 根据用户ID和币种类型统计积分总和
     *
     * @param userId 用户ID
     * @param currencyType 币种类型
     * @param pointsType 变动类型
     * @return 积分总和
     */
    @Select("SELECT IFNULL(SUM(CASE WHEN points_type = 'EARN' THEN points WHEN points_type = 'CONSUME' THEN -points ELSE 0 END), 0) " +
            "FROM user_points_log WHERE user_id = #{userId} AND currency_type = #{currencyType}")
    Long sumPointsByUserIdAndCurrencyType(@Param("userId") Integer userId, @Param("currencyType") String currencyType);

    /**
     * 根据用户ID统计获得的积分总和
     *
     * @param userId 用户ID
     * @param currencyType 币种类型
     * @return 获得的积分总和
     */
    @Select("SELECT IFNULL(SUM(points), 0) FROM user_points_log WHERE user_id = #{userId} AND currency_type = #{currencyType} AND points_type = 'EARN'")
    Long sumEarnPointsByUserIdAndCurrencyType(@Param("userId") Integer userId, @Param("currencyType") String currencyType);

    /**
     * 根据用户ID统计消费的积分总和
     *
     * @param userId 用户ID
     * @param currencyType 币种类型
     * @return 消费的积分总和
     */
    @Select("SELECT IFNULL(SUM(points), 0) FROM user_points_log WHERE user_id = #{userId} AND currency_type = #{currencyType} AND points_type = 'CONSUME'")
    Long sumConsumePointsByUserIdAndCurrencyType(@Param("userId") Integer userId, @Param("currencyType") String currencyType);

    /**
     * 统计积分日志总数
     *
     * @return 积分日志总数
     */
    @Select("SELECT COUNT(*) FROM user_points_log")
    Long countTotalLogs();

    /**
     * 根据用户ID统计积分日志数量
     *
     * @param userId 用户ID
     * @return 积分日志数量
     */
    @Select("SELECT COUNT(*) FROM user_points_log WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Integer userId);

    /**
     * 根据变动类型统计积分日志数量
     *
     * @param pointsType 变动类型
     * @return 积分日志数量
     */
    @Select("SELECT COUNT(*) FROM user_points_log WHERE points_type = #{pointsType}")
    Long countByPointsType(@Param("pointsType") String pointsType);

    /**
     * 根据币种类型统计积分日志数量
     *
     * @param currencyType 币种类型
     * @return 积分日志数量
     */
    @Select("SELECT COUNT(*) FROM user_points_log WHERE currency_type = #{currencyType}")
    Long countByCurrencyType(@Param("currencyType") String currencyType);

    /**
     * 根据业务类型统计积分日志数量
     *
     * @param businessType 业务类型
     * @return 积分日志数量
     */
    @Select("SELECT COUNT(*) FROM user_points_log WHERE business_type = #{businessType}")
    Long countByBusinessType(@Param("businessType") String businessType);

    /**
     * 根据用户ID和业务类型统计积分日志数量
     *
     * @param userId 用户ID
     * @param businessType 业务类型
     * @return 积分日志数量
     */
    @Select("SELECT COUNT(*) FROM user_points_log WHERE user_id = #{userId} AND business_type = #{businessType}")
    Long countByUserIdAndBusinessType(@Param("userId") Integer userId, @Param("businessType") String businessType);

    /**
     * 查询今日积分日志列表
     *
     * @return 今日积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE DATE(create_time) = CURDATE() ORDER BY create_time DESC")
    List<UserPointsLogDO> selectTodayLogs();

    /**
     * 根据用户ID查询今日积分日志列表
     *
     * @param userId 用户ID
     * @return 今日积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} AND DATE(create_time) = CURDATE() ORDER BY create_time DESC")
    List<UserPointsLogDO> selectTodayLogsByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和日期范围查询积分日志列表
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 积分日志列表
     */
    @Select("SELECT * FROM user_points_log WHERE user_id = #{userId} AND DATE(create_time) BETWEEN #{startDate} AND #{endDate} ORDER BY create_time DESC")
    List<UserPointsLogDO> selectByUserIdAndDateRange(@Param("userId") Integer userId, @Param("startDate") String startDate, @Param("endDate") String endDate);
}
