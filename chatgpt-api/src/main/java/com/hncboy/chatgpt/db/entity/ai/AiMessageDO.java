package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一消息实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_message")
@Schema(description = "AI统一消息")
public class AiMessageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 房间ID
     */
    @TableField("room_id")
    @Schema(description = "房间ID")
    private Long roomId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 消息类型 1:用户消息 2:AI回复 3:系统消息 4:错误消息
     */
    @TableField("message_type")
    @Schema(description = "消息类型")
    private Integer messageType;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    @Schema(description = "能力类型")
    private String abilityType;

    /**
     * 消息内容
     */
    @TableField("content")
    @Schema(description = "消息内容")
    private String content;

    /**
     * 原始提示词
     */
    @TableField("original_prompt")
    @Schema(description = "原始提示词")
    private String originalPrompt;

    /**
     * 处理后的提示词
     */
    @TableField("processed_prompt")
    @Schema(description = "处理后的提示词")
    private String processedPrompt;

    /**
     * 模型标识
     */
    @TableField("model_gid")
    @Schema(description = "模型标识")
    private String modelGid;

    /**
     * 模型名称
     */
    @TableField("model_name")
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 父消息ID
     */
    @TableField("parent_msg_id")
    @Schema(description = "父消息ID")
    private Long parentMsgId;

    /**
     * 消息状态 PENDING:待处理 PROCESSING:处理中 COMPLETED:已完成 FAILED:失败
     */
    @TableField("status")
    @Schema(description = "消息状态")
    private String status;

    /**
     * 输入Token数
     */
    @TableField("input_tokens")
    @Schema(description = "输入Token数")
    private Integer inputTokens;

    /**
     * 输出Token数
     */
    @TableField("output_tokens")
    @Schema(description = "输出Token数")
    private Integer outputTokens;

    /**
     * 总Token数
     */
    @TableField("total_tokens")
    @Schema(description = "总Token数")
    private Integer totalTokens;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time")
    @Schema(description = "响应时间")
    private Long responseTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @TableField("error_code")
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    @Schema(description = "客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * 扩展信息(JSON)
     */
    @TableField("extra_info")
    @Schema(description = "扩展信息")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否为用户消息
     *
     * @return true:用户消息 false:其他消息
     */
    public boolean isUserMessage() {
        return messageType != null && messageType == 1;
    }

    /**
     * 检查是否为AI回复
     *
     * @return true:AI回复 false:其他消息
     */
    public boolean isAiMessage() {
        return messageType != null && messageType == 2;
    }

    /**
     * 检查是否为系统消息
     *
     * @return true:系统消息 false:其他消息
     */
    public boolean isSystemMessage() {
        return messageType != null && messageType == 3;
    }

    /**
     * 检查是否为错误消息
     *
     * @return true:错误消息 false:其他消息
     */
    public boolean isErrorMessage() {
        return messageType != null && messageType == 4;
    }

    /**
     * 检查消息是否处理完成
     *
     * @return true:已完成 false:未完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    /**
     * 检查消息是否处理失败
     *
     * @return true:失败 false:未失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查消息是否处理中
     *
     * @return true:处理中 false:非处理中
     */
    public boolean isProcessing() {
        return "PROCESSING".equals(status);
    }

    /**
     * 检查消息是否待处理
     *
     * @return true:待处理 false:非待处理
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 计算Token使用效率
     *
     * @return Token使用效率
     */
    public double getTokenEfficiency() {
        if (totalTokens == null || totalTokens == 0 || responseTime == null || responseTime == 0) {
            return 0.0;
        }
        return (double) totalTokens / responseTime * 1000; // tokens per second
    }
}
