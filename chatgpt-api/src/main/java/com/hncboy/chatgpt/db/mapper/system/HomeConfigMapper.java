package com.hncboy.chatgpt.db.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.system.HomeConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 首页配置Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:50
 */
@Mapper
public interface HomeConfigMapper extends BaseMapper<HomeConfigDO> {

    /**
     * 根据配置键查询配置
     *
     * @param configKey 配置键
     * @return 配置对象
     */
    HomeConfigDO selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 根据业务场景查询配置列表
     *
     * @param bizScene 业务场景
     * @param status   状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectByBizSceneAndStatus(@Param("bizScene") String bizScene, @Param("status") Integer status);

    /**
     * 根据配置分组查询配置列表
     *
     * @param configGroup 配置分组
     * @param status      状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectByConfigGroupAndStatus(@Param("configGroup") String configGroup, @Param("status") Integer status);

    /**
     * 根据配置类型查询配置列表
     *
     * @param configType 配置类型
     * @param status     状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectByConfigTypeAndStatus(@Param("configType") String configType, @Param("status") Integer status);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<HomeConfigDO> selectAllEnabled();

    /**
     * 查询必填配置
     *
     * @param status 状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectRequiredConfigs(@Param("status") Integer status);

    /**
     * 查询敏感配置
     *
     * @param status 状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectSensitiveConfigs(@Param("status") Integer status);

    /**
     * 查询可编辑配置
     *
     * @param status 状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectEditableConfigs(@Param("status") Integer status);

    /**
     * 根据业务场景和配置分组查询配置
     *
     * @param bizScene    业务场景
     * @param configGroup 配置分组
     * @param status      状态
     * @return 配置列表
     */
    List<HomeConfigDO> selectByBizSceneAndGroup(@Param("bizScene") String bizScene, 
                                                @Param("configGroup") String configGroup, 
                                                @Param("status") Integer status);

    /**
     * 批量查询配置
     *
     * @param configKeys 配置键列表
     * @return 配置列表
     */
    List<HomeConfigDO> selectByConfigKeys(@Param("configKeys") List<String> configKeys);

    /**
     * 统计配置数量
     *
     * @param bizScene    业务场景
     * @param configGroup 配置分组
     * @param status      状态
     * @return 配置数量
     */
    Long countByConditions(@Param("bizScene") String bizScene, 
                           @Param("configGroup") String configGroup, 
                           @Param("status") Integer status);

    /**
     * 批量更新配置值
     *
     * @param configKey   配置键
     * @param configValue 配置值
     * @return 更新数量
     */
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);

    /**
     * 批量更新状态
     *
     * @param ids    配置ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") Integer status);
}
