package com.hncboy.chatgpt.db.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 首页配置实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("home_config")
@Schema(description = "首页配置")
public class HomeConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 配置键
     */
    @TableField("config_key")
    @Schema(description = "配置键")
    private String configKey;

    /**
     * 配置名称
     */
    @TableField("config_name")
    @Schema(description = "配置名称")
    private String configName;

    /**
     * 配置值
     */
    @TableField("config_value")
    @Schema(description = "配置值")
    private String configValue;

    /**
     * 配置类型 STRING:字符串 NUMBER:数字 BOOLEAN:布尔 JSON:JSON对象 ARRAY:数组
     */
    @TableField("config_type")
    @Schema(description = "配置类型")
    private String configType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话 ALL:通用
     */
    @TableField("biz_scene")
    @Schema(description = "业务场景")
    private String bizScene;

    /**
     * 配置分组
     */
    @TableField("config_group")
    @Schema(description = "配置分组")
    private String configGroup;

    /**
     * 配置描述
     */
    @TableField("description")
    @Schema(description = "配置描述")
    private String description;

    /**
     * 默认值
     */
    @TableField("default_value")
    @Schema(description = "默认值")
    private String defaultValue;

    /**
     * 可选值(JSON数组)
     */
    @TableField("options")
    @Schema(description = "可选值")
    private String options;

    /**
     * 是否必填 1:必填 0:非必填
     */
    @TableField("is_required")
    @Schema(description = "是否必填")
    private Integer isRequired;

    /**
     * 是否敏感 1:敏感 0:非敏感
     */
    @TableField("is_sensitive")
    @Schema(description = "是否敏感")
    private Integer isSensitive;

    /**
     * 是否可编辑 1:可编辑 0:只读
     */
    @TableField("is_editable")
    @Schema(description = "是否可编辑")
    private Integer isEditable;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    @Schema(description = "排序权重")
    private Integer sortWeight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否必填
     *
     * @return true:必填 false:非必填
     */
    public boolean isRequiredConfig() {
        return isRequired != null && isRequired == 1;
    }

    /**
     * 检查是否敏感
     *
     * @return true:敏感 false:非敏感
     */
    public boolean isSensitiveConfig() {
        return isSensitive != null && isSensitive == 1;
    }

    /**
     * 检查是否可编辑
     *
     * @return true:可编辑 false:只读
     */
    public boolean isEditableConfig() {
        return isEditable != null && isEditable == 1;
    }

    /**
     * 检查是否为字符串类型
     *
     * @return true:字符串类型 false:其他类型
     */
    public boolean isStringType() {
        return "STRING".equals(configType);
    }

    /**
     * 检查是否为数字类型
     *
     * @return true:数字类型 false:其他类型
     */
    public boolean isNumberType() {
        return "NUMBER".equals(configType);
    }

    /**
     * 检查是否为布尔类型
     *
     * @return true:布尔类型 false:其他类型
     */
    public boolean isBooleanType() {
        return "BOOLEAN".equals(configType);
    }

    /**
     * 检查是否为JSON类型
     *
     * @return true:JSON类型 false:其他类型
     */
    public boolean isJsonType() {
        return "JSON".equals(configType);
    }

    /**
     * 检查是否为数组类型
     *
     * @return true:数组类型 false:其他类型
     */
    public boolean isArrayType() {
        return "ARRAY".equals(configType);
    }

    /**
     * 获取有效的配置值
     *
     * @return 配置值
     */
    public String getEffectiveValue() {
        if (configValue != null && !configValue.trim().isEmpty()) {
            return configValue;
        }
        return defaultValue;
    }

    /**
     * 检查是否适用于指定业务场景
     *
     * @param scene 业务场景
     * @return true:适用 false:不适用
     */
    public boolean isApplicableToScene(String scene) {
        if (bizScene == null || "ALL".equals(bizScene)) {
            return true;
        }
        return bizScene.equals(scene);
    }
}
