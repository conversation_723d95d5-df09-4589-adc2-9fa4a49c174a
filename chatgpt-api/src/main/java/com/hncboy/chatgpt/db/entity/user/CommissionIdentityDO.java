package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 分佣身份实体类
 * 
 * 重构说明:
 * 1. 完整保留commission_identity表结构和业务逻辑
 * 2. 保留分佣身份管理功能，支持多种身份类型
 * 3. 保留时间有效性验证，start_time和end_time字段
 * 4. 保留分佣比例配置，percentage字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("commission_identity")
@Schema(description = "分佣身份")
public class CommissionIdentityDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 类型
     */
    @TableField("type")
    @Schema(description = "类型")
    private String type;

    /**
     * 编号
     */
    @TableField("code")
    @Schema(description = "编号")
    private String code;

    /**
     * 状态 0:有效 1:无效
     */
    @TableField("status")
    @Schema(description = "状态")
    private String status;

    /**
     * 生效时间
     */
    @TableField("start_time")
    @Schema(description = "生效时间")
    private LocalDateTime startTime;

    /**
     * 过期时间
     */
    @TableField("end_time")
    @Schema(description = "过期时间")
    private LocalDateTime endTime;

    /**
     * 分佣比例(百分比)
     */
    @TableField("percentage")
    @Schema(description = "分佣比例")
    private Integer percentage;

    /**
     * 姓名
     */
    @TableField("name")
    @Schema(description = "姓名")
    private String name;

    /**
     * 手机号
     */
    @TableField("phone")
    @Schema(description = "手机号")
    private String phone;

    /**
     * 微信公众号链接
     */
    @TableField("wx_mp_url")
    @Schema(description = "微信公众号链接")
    private String wxMpUrl;

    /**
     * 用户信息ID(关联user_base_info.id)
     */
    @TableField("user_info_id")
    @Schema(description = "用户信息ID")
    private Integer userInfoId;

    /**
     * openId
     */
    @TableField("open_id")
    @Schema(description = "openId")
    private String openId;

    /**
     * 邀请码
     */
    @TableField("invite_code")
    @Schema(description = "邀请码")
    private String inviteCode;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 检查是否有效
     *
     * @return true:有效 false:无效
     */
    public boolean isValid() {
        return "0".equals(status);
    }

    /**
     * 检查是否无效
     *
     * @return true:无效 false:有效
     */
    public boolean isInvalid() {
        return "1".equals(status);
    }

    /**
     * 检查是否在有效期内
     *
     * @return true:在有效期内 false:不在有效期内
     */
    public boolean isInValidPeriod() {
        LocalDateTime now = LocalDateTime.now();
        return startTime != null && endTime != null 
               && now.isAfter(startTime) && now.isBefore(endTime);
    }

    /**
     * 检查是否已过期
     *
     * @return true:已过期 false:未过期
     */
    public boolean isExpired() {
        return endTime != null && LocalDateTime.now().isAfter(endTime);
    }

    /**
     * 检查是否未生效
     *
     * @return true:未生效 false:已生效
     */
    public boolean isNotEffective() {
        return startTime != null && LocalDateTime.now().isBefore(startTime);
    }

    /**
     * 检查是否可用(状态有效且在有效期内)
     *
     * @return true:可用 false:不可用
     */
    public boolean isAvailable() {
        return isValid() && isInValidPeriod();
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case "0":
                return "有效";
            case "1":
                return "无效";
            default:
                return "未知";
        }
    }

    /**
     * 获取分佣比例描述
     *
     * @return 分佣比例描述
     */
    public String getPercentageDescription() {
        if (percentage == null) {
            return "0%";
        }
        return percentage + "%";
    }

    /**
     * 设置为有效状态
     */
    public void setValid() {
        this.status = "0";
    }

    /**
     * 设置为无效状态
     */
    public void setInvalid() {
        this.status = "1";
    }

    /**
     * 检查是否有邀请码
     *
     * @return true:有邀请码 false:无邀请码
     */
    public boolean hasInviteCode() {
        return inviteCode != null && !inviteCode.trim().isEmpty();
    }

    /**
     * 检查是否有微信公众号链接
     *
     * @return true:有微信公众号链接 false:无微信公众号链接
     */
    public boolean hasWxMpUrl() {
        return wxMpUrl != null && !wxMpUrl.trim().isEmpty();
    }

    /**
     * 获取有效期描述
     *
     * @return 有效期描述
     */
    public String getValidPeriodDescription() {
        if (startTime == null || endTime == null) {
            return "无限期";
        }
        return startTime + " 至 " + endTime;
    }

    /**
     * 计算剩余有效天数
     *
     * @return 剩余有效天数，-1表示已过期或无效
     */
    public long getRemainingDays() {
        if (endTime == null) {
            return -1;
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(endTime)) {
            return -1;
        }
        return java.time.Duration.between(now, endTime).toDays();
    }

    /**
     * 检查是否即将过期(7天内)
     *
     * @return true:即将过期 false:不会即将过期
     */
    public boolean isExpiringSoon() {
        long remainingDays = getRemainingDays();
        return remainingDays >= 0 && remainingDays <= 7;
    }

    /**
     * 获取完整描述
     *
     * @return 完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append("身份类型: ").append(type != null ? type : "未知");
        sb.append(", 编号: ").append(code != null ? code : "无");
        sb.append(", 状态: ").append(getStatusDescription());
        sb.append(", 分佣比例: ").append(getPercentageDescription());
        sb.append(", 有效期: ").append(getValidPeriodDescription());
        return sb.toString();
    }
}
