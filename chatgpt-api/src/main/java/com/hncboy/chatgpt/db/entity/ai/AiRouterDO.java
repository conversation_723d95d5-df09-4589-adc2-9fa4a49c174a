package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI路由实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_router")
public class AiRouterDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 路由名称
     */
    @TableField("name")
    private String name;

    /**
     * 路由描述
     */
    @TableField("description")
    private String description;

    /**
     * 路由配置ID
     */
    @TableField("config_id")
    private Integer configId;

    /**
     * 模型ID
     */
    @TableField("model_id")
    private Integer modelId;

    /**
     * 路由权重
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 最大并发数
     */
    @TableField("max_concurrent")
    private Integer maxConcurrent;

    /**
     * 当前并发数
     */
    @TableField("current_concurrent")
    private Integer currentConcurrent;

    /**
     * 请求超时时间(秒)
     */
    @TableField("timeout")
    private Integer timeout;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 健康检查URL
     */
    @TableField("health_check_url")
    private String healthCheckUrl;

    /**
     * 健康检查间隔(秒)
     */
    @TableField("health_check_interval")
    private Integer healthCheckInterval;

    /**
     * 健康状态 1:健康 0:不健康
     */
    @TableField("health_status")
    private Integer healthStatus;

    /**
     * 最后健康检查时间
     */
    @TableField("last_health_check_time")
    private LocalDateTime lastHealthCheckTime;

    /**
     * 总请求数
     */
    @TableField("total_requests")
    private Long totalRequests;

    /**
     * 成功请求数
     */
    @TableField("success_requests")
    private Long successRequests;

    /**
     * 失败请求数
     */
    @TableField("failed_requests")
    private Long failedRequests;

    /**
     * 平均响应时间(毫秒)
     */
    @TableField("avg_response_time")
    private Long avgResponseTime;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否健康
     *
     * @return true:健康 false:不健康
     */
    public boolean isHealthy() {
        return healthStatus != null && healthStatus == 1;
    }

    /**
     * 检查是否可用
     *
     * @return true:可用 false:不可用
     */
    public boolean isAvailable() {
        return isEnabled() && isHealthy() && !isOverloaded();
    }

    /**
     * 检查是否过载
     *
     * @return true:过载 false:未过载
     */
    public boolean isOverloaded() {
        if (maxConcurrent == null || currentConcurrent == null) {
            return false;
        }
        return currentConcurrent >= maxConcurrent;
    }

    /**
     * 增加并发数
     */
    public void incrementConcurrent() {
        this.currentConcurrent = (this.currentConcurrent == null ? 0 : this.currentConcurrent) + 1;
    }

    /**
     * 减少并发数
     */
    public void decrementConcurrent() {
        this.currentConcurrent = (this.currentConcurrent == null || this.currentConcurrent <= 0) ? 0 : this.currentConcurrent - 1;
    }

    /**
     * 增加请求统计
     *
     * @param success      是否成功
     * @param responseTime 响应时间
     */
    public void addRequestStats(boolean success, long responseTime) {
        this.totalRequests = (this.totalRequests == null ? 0 : this.totalRequests) + 1;
        if (success) {
            this.successRequests = (this.successRequests == null ? 0 : this.successRequests) + 1;
        } else {
            this.failedRequests = (this.failedRequests == null ? 0 : this.failedRequests) + 1;
        }
        
        // 计算平均响应时间
        if (this.avgResponseTime == null) {
            this.avgResponseTime = responseTime;
        } else {
            this.avgResponseTime = (this.avgResponseTime + responseTime) / 2;
        }
    }

    /**
     * 获取成功率
     *
     * @return 成功率(0-100)
     */
    public double getSuccessRate() {
        if (totalRequests == null || totalRequests == 0) {
            return 0.0;
        }
        long success = successRequests == null ? 0 : successRequests;
        return (double) success / totalRequests * 100;
    }
}
