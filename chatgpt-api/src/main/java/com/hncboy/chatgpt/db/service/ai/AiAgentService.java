package com.hncboy.chatgpt.db.service.ai;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.ai.AiAgentDO;

import java.util.List;

/**
 * AI智能体服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:20
 */
public interface AiAgentService extends IService<AiAgentDO> {

    /**
     * 根据能力类型查询智能体列表
     *
     * @param abilityType 能力类型
     * @return 智能体列表
     */
    List<AiAgentDO> listByAbilityType(String abilityType);

    /**
     * 查询公开的智能体列表
     *
     * @return 智能体列表
     */
    List<AiAgentDO> listPublicAgents();

    /**
     * 根据创建者查询智能体列表
     *
     * @param creatorId 创建者ID
     * @return 智能体列表
     */
    List<AiAgentDO> listByCreatorId(Integer creatorId);

    /**
     * 增加使用次数
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean incrementUseCount(Integer id);

    /**
     * 点赞智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean likeAgent(Integer id);

    /**
     * 取消点赞智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean unlikeAgent(Integer id);

    /**
     * 收藏智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean favoriteAgent(Integer id);

    /**
     * 取消收藏智能体
     *
     * @param id 智能体ID
     * @return 是否成功
     */
    boolean unfavoriteAgent(Integer id);
}
