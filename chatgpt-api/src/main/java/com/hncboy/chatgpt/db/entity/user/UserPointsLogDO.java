package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户积分日志实体类
 * 
 * 重构说明:
 * 1. 修正POINTS概念，区分积分(points)和塔罗币(POINTS)
 * 2. currency_type字段：POINTS:塔罗币 COINS:积分
 * 3. 支持多种业务类型：SIGN:签到 PAY:支付 REFUND:退款 TAROT:塔罗解读
 * 4. 支持多种变动类型：EARN:获得 CONSUME:消费
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_points_log")
@Schema(description = "用户积分日志")
public class UserPointsLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 关联订单
     */
    @TableField("rel_order")
    @Schema(description = "关联订单")
    private String relOrder;

    /**
     * 变动数量
     */
    @TableField("points")
    @Schema(description = "变动数量")
    private Integer points;

    /**
     * 变动类型 EARN:获得 CONSUME:消费
     */
    @TableField("points_type")
    @Schema(description = "变动类型")
    private String pointsType;

    /**
     * 币种类型 POINTS:塔罗币 COINS:积分
     */
    @TableField("currency_type")
    @Schema(description = "币种类型")
    private String currencyType;

    /**
     * 业务类型 SIGN:签到 PAY:支付 REFUND:退款 TAROT:塔罗解读
     */
    @TableField("business_type")
    @Schema(description = "业务类型")
    private String businessType;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 检查是否为获得类型
     *
     * @return true:获得 false:消费
     */
    public boolean isEarnType() {
        return "EARN".equals(pointsType);
    }

    /**
     * 检查是否为消费类型
     *
     * @return true:消费 false:获得
     */
    public boolean isConsumeType() {
        return "CONSUME".equals(pointsType);
    }

    /**
     * 检查是否为塔罗币
     *
     * @return true:塔罗币 false:积分
     */
    public boolean isTarotCoins() {
        return "POINTS".equals(currencyType);
    }

    /**
     * 检查是否为积分
     *
     * @return true:积分 false:塔罗币
     */
    public boolean isCoins() {
        return "COINS".equals(currencyType);
    }

    /**
     * 检查是否为签到业务
     *
     * @return true:签到业务 false:其他业务
     */
    public boolean isSignBusiness() {
        return "SIGN".equals(businessType);
    }

    /**
     * 检查是否为支付业务
     *
     * @return true:支付业务 false:其他业务
     */
    public boolean isPayBusiness() {
        return "PAY".equals(businessType);
    }

    /**
     * 检查是否为退款业务
     *
     * @return true:退款业务 false:其他业务
     */
    public boolean isRefundBusiness() {
        return "REFUND".equals(businessType);
    }

    /**
     * 检查是否为塔罗解读业务
     *
     * @return true:塔罗解读业务 false:其他业务
     */
    public boolean isTarotBusiness() {
        return "TAROT".equals(businessType);
    }

    /**
     * 获取变动类型描述
     *
     * @return 变动类型描述
     */
    public String getPointsTypeDescription() {
        if (pointsType == null) {
            return "未知";
        }
        switch (pointsType) {
            case "EARN":
                return "获得";
            case "CONSUME":
                return "消费";
            default:
                return "未知";
        }
    }

    /**
     * 获取币种类型描述
     *
     * @return 币种类型描述
     */
    public String getCurrencyTypeDescription() {
        if (currencyType == null) {
            return "未知";
        }
        switch (currencyType) {
            case "POINTS":
                return "塔罗币";
            case "COINS":
                return "积分";
            default:
                return "未知";
        }
    }

    /**
     * 获取业务类型描述
     *
     * @return 业务类型描述
     */
    public String getBusinessTypeDescription() {
        if (businessType == null) {
            return "未知";
        }
        switch (businessType) {
            case "SIGN":
                return "签到";
            case "PAY":
                return "支付";
            case "REFUND":
                return "退款";
            case "TAROT":
                return "塔罗解读";
            default:
                return "未知";
        }
    }

    /**
     * 获取完整描述
     *
     * @return 完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getPointsTypeDescription());
        sb.append(" ");
        sb.append(points != null ? points : 0);
        sb.append(" ");
        sb.append(getCurrencyTypeDescription());
        sb.append("(");
        sb.append(getBusinessTypeDescription());
        sb.append(")");
        return sb.toString();
    }

    /**
     * 检查是否有关联订单
     *
     * @return true:有关联订单 false:无关联订单
     */
    public boolean hasRelatedOrder() {
        return relOrder != null && !relOrder.trim().isEmpty();
    }

    /**
     * 获取变动数量(带符号)
     *
     * @return 变动数量
     */
    public String getSignedPoints() {
        if (points == null) {
            return "0";
        }
        if (isEarnType()) {
            return "+" + points;
        } else if (isConsumeType()) {
            return "-" + points;
        } else {
            return String.valueOf(points);
        }
    }

    /**
     * 创建获得记录的静态方法
     *
     * @param userId 用户ID
     * @param points 变动数量
     * @param currencyType 币种类型
     * @param businessType 业务类型
     * @param relOrder 关联订单
     * @param remark 备注
     * @return UserPointsLogDO
     */
    public static UserPointsLogDO createEarnRecord(Integer userId, Integer points, String currencyType, 
                                                   String businessType, String relOrder, String remark) {
        UserPointsLogDO log = new UserPointsLogDO();
        log.setUserId(userId);
        log.setPoints(points);
        log.setPointsType("EARN");
        log.setCurrencyType(currencyType);
        log.setBusinessType(businessType);
        log.setRelOrder(relOrder);
        log.setRemark(remark);
        return log;
    }

    /**
     * 创建消费记录的静态方法
     *
     * @param userId 用户ID
     * @param points 变动数量
     * @param currencyType 币种类型
     * @param businessType 业务类型
     * @param relOrder 关联订单
     * @param remark 备注
     * @return UserPointsLogDO
     */
    public static UserPointsLogDO createConsumeRecord(Integer userId, Integer points, String currencyType, 
                                                      String businessType, String relOrder, String remark) {
        UserPointsLogDO log = new UserPointsLogDO();
        log.setUserId(userId);
        log.setPoints(points);
        log.setPointsType("CONSUME");
        log.setCurrencyType(currencyType);
        log.setBusinessType(businessType);
        log.setRelOrder(relOrder);
        log.setRemark(remark);
        return log;
    }
}
