package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户基础信息Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加用户相关的自定义查询方法
 * 3. 支持多种查询条件：账号、手机号、邮箱、用户类型等
 * 4. 支持分佣相关查询，parent_id和commission_id
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfoDO> {

    /**
     * 根据账号查询用户信息
     *
     * @param account 账号
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE account = #{account} AND deleted = 0")
    UserBaseInfoDO selectByAccount(@Param("account") String account);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE phone = #{phone} AND deleted = 0")
    UserBaseInfoDO selectByPhone(@Param("phone") String phone);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE email = #{email} AND deleted = 0")
    UserBaseInfoDO selectByEmail(@Param("email") String email);

    /**
     * 根据用户类型查询用户列表
     *
     * @param userType 用户类型
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE user_type = #{userType} AND deleted = 0")
    List<UserBaseInfoDO> selectByUserType(@Param("userType") String userType);

    /**
     * 根据分佣身份ID查询用户列表
     *
     * @param commissionId 分佣身份ID
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE commission_id = #{commissionId} AND deleted = 0")
    List<UserBaseInfoDO> selectByCommissionId(@Param("commissionId") Integer commissionId);

    /**
     * 根据上级用户ID查询下级用户列表
     *
     * @param parentId 上级用户ID
     * @return 下级用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE parent_id = #{parentId} AND deleted = 0")
    List<UserBaseInfoDO> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 查询VIP用户列表
     *
     * @return VIP用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE vip_end_time > NOW() AND deleted = 0")
    List<UserBaseInfoDO> selectVipUsers();

    /**
     * 查询即将过期的VIP用户列表(7天内过期)
     *
     * @return 即将过期的VIP用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE vip_end_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) AND deleted = 0")
    List<UserBaseInfoDO> selectExpiringSoonVipUsers();

    /**
     * 根据状态查询用户列表
     *
     * @param status 状态
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE status = #{status} AND deleted = 0")
    List<UserBaseInfoDO> selectByStatus(@Param("status") Integer status);

    /**
     * 查询正常状态的用户列表
     *
     * @return 正常状态的用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE status = 0 AND deleted = 0")
    List<UserBaseInfoDO> selectNormalUsers();

    /**
     * 查询被禁用的用户列表
     *
     * @return 被禁用的用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE status = 1 AND deleted = 0")
    List<UserBaseInfoDO> selectDisabledUsers();

    /**
     * 查询已注销的用户列表
     *
     * @return 已注销的用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE status = 2 AND deleted = 0")
    List<UserBaseInfoDO> selectDeactivatedUsers();

    /**
     * 更新用户积分
     *
     * @param userId 用户ID
     * @param points 积分变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET points = points + #{points} WHERE id = #{userId} AND deleted = 0")
    int updatePoints(@Param("userId") Integer userId, @Param("points") Integer points);

    /**
     * 更新用户塔罗币
     *
     * @param userId 用户ID
     * @param tarotCoins 塔罗币变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET tarot_coins = tarot_coins + #{tarotCoins} WHERE id = #{userId} AND deleted = 0")
    int updateTarotCoins(@Param("userId") Integer userId, @Param("tarotCoins") Integer tarotCoins);

    /**
     * 更新用户使用次数
     *
     * @param userId 用户ID
     * @param useNum 使用次数变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET use_num = use_num + #{useNum} WHERE id = #{userId} AND deleted = 0")
    int updateUseNum(@Param("userId") Integer userId, @Param("useNum") Integer useNum);

    /**
     * 更新用户免费次数
     *
     * @param userId 用户ID
     * @param freeNum 免费次数变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET free_num = free_num + #{freeNum} WHERE id = #{userId} AND deleted = 0")
    int updateFreeNum(@Param("userId") Integer userId, @Param("freeNum") Integer freeNum);

    /**
     * 根据语言偏好查询用户列表
     *
     * @param language 语言偏好
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE language = #{language} AND deleted = 0")
    List<UserBaseInfoDO> selectByLanguage(@Param("language") String language);

    /**
     * 根据币种偏好查询用户列表
     *
     * @param currency 币种偏好
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE currency = #{currency} AND deleted = 0")
    List<UserBaseInfoDO> selectByCurrency(@Param("currency") String currency);

    /**
     * 根据时区查询用户列表
     *
     * @param timezone 时区
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE timezone = #{timezone} AND deleted = 0")
    List<UserBaseInfoDO> selectByTimezone(@Param("timezone") String timezone);

    /**
     * 统计用户总数
     *
     * @return 用户总数
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE deleted = 0")
    Long countTotalUsers();

    /**
     * 根据用户类型统计用户数量
     *
     * @param userType 用户类型
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE user_type = #{userType} AND deleted = 0")
    Long countByUserType(@Param("userType") String userType);

    /**
     * 统计VIP用户数量
     *
     * @return VIP用户数量
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE vip_end_time > NOW() AND deleted = 0")
    Long countVipUsers();

    /**
     * 统计有分佣身份的用户数量
     *
     * @return 有分佣身份的用户数量
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE commission_id IS NOT NULL AND commission_id > 0 AND deleted = 0")
    Long countUsersWithCommission();

    /**
     * 统计有上级用户的用户数量
     *
     * @return 有上级用户的用户数量
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE parent_id IS NOT NULL AND parent_id > 0 AND deleted = 0")
    Long countUsersWithParent();
}
