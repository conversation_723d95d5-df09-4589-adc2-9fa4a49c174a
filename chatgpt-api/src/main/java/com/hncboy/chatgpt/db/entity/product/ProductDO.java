package com.hncboy.chatgpt.db.entity.product;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product")
public class ProductDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品编码
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品描述
     */
    @TableField("description")
    private String description;

    /**
     * 产品类型 VIP:会员 POINTS:积分 TAROT_COINS:塔罗币 TIMES:次数
     */
    @TableField("product_type")
    private String productType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话 ALL:通用
     */
    @TableField("biz_scene")
    private String bizScene;

    /**
     * 产品规格(JSON)
     */
    @TableField("specifications")
    private String specifications;

    /**
     * 产品价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 原价
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 有效期(天)
     */
    @TableField("validity_days")
    private Integer validityDays;

    /**
     * 产品数量/次数
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 赠送数量
     */
    @TableField("bonus_quantity")
    private Integer bonusQuantity;

    /**
     * 产品图片
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 产品标签(JSON数组)
     */
    @TableField("tags")
    private String tags;

    /**
     * 是否推荐 1:推荐 0:不推荐
     */
    @TableField("is_recommended")
    private Integer isRecommended;

    /**
     * 是否热门 1:热门 0:普通
     */
    @TableField("is_hot")
    private Integer isHot;

    /**
     * 是否限时 1:限时 0:长期
     */
    @TableField("is_limited_time")
    private Integer isLimitedTime;

    /**
     * 限时开始时间
     */
    @TableField("limited_start_time")
    private LocalDateTime limitedStartTime;

    /**
     * 限时结束时间
     */
    @TableField("limited_end_time")
    private LocalDateTime limitedEndTime;

    /**
     * 库存数量
     */
    @TableField("stock_quantity")
    private Integer stockQuantity;

    /**
     * 已售数量
     */
    @TableField("sold_quantity")
    private Integer soldQuantity;

    /**
     * 购买限制(每用户)
     */
    @TableField("purchase_limit")
    private Integer purchaseLimit;

    /**
     * 最小购买数量
     */
    @TableField("min_purchase")
    private Integer minPurchase;

    /**
     * 最大购买数量
     */
    @TableField("max_purchase")
    private Integer maxPurchase;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    private Integer sortWeight;

    /**
     * 上架时间
     */
    @TableField("shelf_time")
    private LocalDateTime shelfTime;

    /**
     * 下架时间
     */
    @TableField("off_shelf_time")
    private LocalDateTime offShelfTime;

    /**
     * 状态 1:上架 0:下架
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否上架
     *
     * @return true:上架 false:下架
     */
    public boolean isOnShelf() {
        return status != null && status == 1;
    }

    /**
     * 检查是否推荐
     *
     * @return true:推荐 false:不推荐
     */
    public boolean isRecommendedProduct() {
        return isRecommended != null && isRecommended == 1;
    }

    /**
     * 检查是否热门
     *
     * @return true:热门 false:普通
     */
    public boolean isHotProduct() {
        return isHot != null && isHot == 1;
    }

    /**
     * 检查是否限时产品
     *
     * @return true:限时 false:长期
     */
    public boolean isLimitedTimeProduct() {
        return isLimitedTime != null && isLimitedTime == 1;
    }

    /**
     * 检查是否在限时期内
     *
     * @return true:限时期内 false:已过期
     */
    public boolean isInLimitedTime() {
        if (!isLimitedTimeProduct()) {
            return true;
        }
        LocalDateTime now = LocalDateTime.now();
        return (limitedStartTime == null || now.isAfter(limitedStartTime)) &&
               (limitedEndTime == null || now.isBefore(limitedEndTime));
    }

    /**
     * 检查是否有库存
     *
     * @return true:有库存 false:无库存
     */
    public boolean hasStock() {
        return stockQuantity == null || stockQuantity > 0;
    }

    /**
     * 检查是否可以购买
     *
     * @return true:可以购买 false:不可以购买
     */
    public boolean canPurchase() {
        return isOnShelf() && hasStock() && isInLimitedTime();
    }

    /**
     * 检查购买数量是否合法
     *
     * @param quantity 购买数量
     * @return true:合法 false:不合法
     */
    public boolean isValidPurchaseQuantity(int quantity) {
        if (minPurchase != null && quantity < minPurchase) {
            return false;
        }
        if (maxPurchase != null && quantity > maxPurchase) {
            return false;
        }
        if (stockQuantity != null && quantity > stockQuantity) {
            return false;
        }
        return true;
    }

    /**
     * 减少库存
     *
     * @param quantity 减少数量
     */
    public void decreaseStock(int quantity) {
        if (stockQuantity != null) {
            this.stockQuantity = Math.max(0, this.stockQuantity - quantity);
        }
        this.soldQuantity = (this.soldQuantity == null ? 0 : this.soldQuantity) + quantity;
    }

    /**
     * 增加库存
     *
     * @param quantity 增加数量
     */
    public void increaseStock(int quantity) {
        this.stockQuantity = (this.stockQuantity == null ? 0 : this.stockQuantity) + quantity;
        this.soldQuantity = (this.soldQuantity == null || this.soldQuantity <= quantity) ? 0 : this.soldQuantity - quantity;
    }

    /**
     * 计算折扣率
     *
     * @return 折扣率(0-100)
     */
    public double getDiscountRate() {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) == 0 || price == null) {
            return 0.0;
        }
        BigDecimal discount = originalPrice.subtract(price);
        return discount.divide(originalPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue();
    }

    /**
     * 获取产品类型描述
     *
     * @return 产品类型描述
     */
    public String getProductTypeDesc() {
        if (productType == null) {
            return "未知";
        }
        switch (productType) {
            case "VIP":
                return "会员";
            case "POINTS":
                return "积分";
            case "TAROT_COINS":
                return "塔罗币";
            case "TIMES":
                return "次数";
            default:
                return productType;
        }
    }
}
