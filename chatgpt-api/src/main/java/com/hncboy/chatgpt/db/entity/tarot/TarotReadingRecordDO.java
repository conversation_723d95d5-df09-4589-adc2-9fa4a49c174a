package com.hncboy.chatgpt.db.entity.tarot;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 塔罗解读记录实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tarot_reading_record")
public class TarotReadingRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 牌阵ID
     */
    @TableField("spread_id")
    private Integer spreadId;

    /**
     * 牌阵名称
     */
    @TableField("spread_name")
    private String spreadName;

    /**
     * 问题
     */
    @TableField("question")
    private String question;

    /**
     * 问题类型
     */
    @TableField("question_type")
    private String questionType;

    /**
     * 抽取的牌(JSON数组)
     */
    @TableField("drawn_cards")
    private String drawnCards;

    /**
     * 牌位解读(JSON)
     */
    @TableField("card_positions")
    private String cardPositions;

    /**
     * AI解读内容
     */
    @TableField("ai_interpretation")
    private String aiInterpretation;

    /**
     * 解读摘要
     */
    @TableField("interpretation_summary")
    private String interpretationSummary;

    /**
     * 建议
     */
    @TableField("advice")
    private String advice;

    /**
     * 消耗的塔罗币
     */
    @TableField("consumed_coins")
    private Integer consumedCoins;

    /**
     * 解读状态 PENDING:待解读 COMPLETED:已完成 FAILED:失败
     */
    @TableField("status")
    private String status;

    /**
     * 解读开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 解读完成时间
     */
    @TableField("complete_time")
    private LocalDateTime completeTime;

    /**
     * 用户评分(1-5星)
     */
    @TableField("user_rating")
    private Integer userRating;

    /**
     * 用户反馈
     */
    @TableField("user_feedback")
    private String userFeedback;

    /**
     * 是否公开 1:公开 0:私有
     */
    @TableField("is_public")
    private Integer isPublic;

    /**
     * 分享次数
     */
    @TableField("share_count")
    private Integer shareCount;

    /**
     * 查看次数
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查解读是否完成
     *
     * @return true:已完成 false:未完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    /**
     * 检查解读是否失败
     *
     * @return true:失败 false:未失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查解读是否待处理
     *
     * @return true:待处理 false:非待处理
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否公开
     *
     * @return true:公开 false:私有
     */
    public boolean isPublicReading() {
        return isPublic != null && isPublic == 1;
    }

    /**
     * 增加分享次数
     */
    public void incrementShareCount() {
        this.shareCount = (this.shareCount == null ? 0 : this.shareCount) + 1;
    }

    /**
     * 增加查看次数
     */
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }

    /**
     * 计算解读耗时(秒)
     *
     * @return 解读耗时
     */
    public long getReadingDuration() {
        if (startTime == null || completeTime == null) {
            return 0;
        }
        return java.time.Duration.between(startTime, completeTime).getSeconds();
    }
}
