package com.hncboy.chatgpt.db.service.ai.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.ai.AiAgentDO;
import com.hncboy.chatgpt.db.mapper.ai.AiAgentMapper;
import com.hncboy.chatgpt.db.service.ai.AiAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * AI智能体服务实现类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:25
 */
@Slf4j
@Service
public class AiAgentServiceImpl extends ServiceImpl<AiAgentMapper, AiAgentDO> implements AiAgentService {

    @Resource
    private AiAgentMapper aiAgentMapper;

    @Override
    public List<AiAgentDO> listByAbilityType(String abilityType) {
        log.info("根据能力类型查询智能体列表: abilityType={}", abilityType);
        return aiAgentMapper.selectByAbilityTypeAndStatus(abilityType, 1);
    }

    @Override
    public List<AiAgentDO> listPublicAgents() {
        log.info("查询公开的智能体列表");
        return aiAgentMapper.selectPublicAgents(1, 1);
    }

    @Override
    public List<AiAgentDO> listByCreatorId(Integer creatorId) {
        log.info("根据创建者查询智能体列表: creatorId={}", creatorId);
        return aiAgentMapper.selectByCreatorId(creatorId);
    }

    @Override
    public boolean incrementUseCount(Integer id) {
        log.info("增加智能体使用次数: id={}", id);
        try {
            int result = aiAgentMapper.incrementUseCount(id);
            return result > 0;
        } catch (Exception e) {
            log.error("增加智能体使用次数失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean likeAgent(Integer id) {
        log.info("点赞智能体: id={}", id);
        try {
            int result = aiAgentMapper.updateLikeCount(id, 1);
            return result > 0;
        } catch (Exception e) {
            log.error("点赞智能体失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean unlikeAgent(Integer id) {
        log.info("取消点赞智能体: id={}", id);
        try {
            int result = aiAgentMapper.updateLikeCount(id, -1);
            return result > 0;
        } catch (Exception e) {
            log.error("取消点赞智能体失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean favoriteAgent(Integer id) {
        log.info("收藏智能体: id={}", id);
        try {
            int result = aiAgentMapper.updateFavoriteCount(id, 1);
            return result > 0;
        } catch (Exception e) {
            log.error("收藏智能体失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean unfavoriteAgent(Integer id) {
        log.info("取消收藏智能体: id={}", id);
        try {
            int result = aiAgentMapper.updateFavoriteCount(id, -1);
            return result > 0;
        } catch (Exception e) {
            log.error("取消收藏智能体失败: id={}", id, e);
            return false;
        }
    }
}
