package com.hncboy.chatgpt.db.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户登录历史实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_login_history")
public class UserLoginHistoryDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 登录类型 WECHAT:微信 GOOGLE:谷歌 FACEBOOK:脸书 PHONE:手机 EMAIL:邮箱
     */
    @TableField("login_type")
    private String loginType;

    /**
     * 登录账号
     */
    @TableField("login_account")
    private String loginAccount;

    /**
     * 登录状态 SUCCESS:成功 FAILED:失败
     */
    @TableField("login_status")
    private String loginStatus;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 登录IP
     */
    @TableField("login_ip")
    private String loginIp;

    /**
     * 登录地址
     */
    @TableField("login_address")
    private String loginAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 设备类型 PC:电脑 MOBILE:手机 TABLET:平板
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 操作系统
     */
    @TableField("operating_system")
    private String operatingSystem;

    /**
     * 浏览器
     */
    @TableField("browser")
    private String browser;

    /**
     * 登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 登出时间
     */
    @TableField("logout_time")
    private LocalDateTime logoutTime;

    /**
     * 在线时长(秒)
     */
    @TableField("online_duration")
    private Long onlineDuration;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 令牌ID
     */
    @TableField("token_id")
    private String tokenId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 检查登录是否成功
     *
     * @return true:成功 false:失败
     */
    public boolean isLoginSuccess() {
        return "SUCCESS".equals(loginStatus);
    }

    /**
     * 检查登录是否失败
     *
     * @return true:失败 false:成功
     */
    public boolean isLoginFailed() {
        return "FAILED".equals(loginStatus);
    }

    /**
     * 检查是否为移动端登录
     *
     * @return true:移动端 false:非移动端
     */
    public boolean isMobileLogin() {
        return "MOBILE".equals(deviceType);
    }

    /**
     * 检查是否为PC端登录
     *
     * @return true:PC端 false:非PC端
     */
    public boolean isPcLogin() {
        return "PC".equals(deviceType);
    }

    /**
     * 检查是否为第三方登录
     *
     * @return true:第三方登录 false:本地登录
     */
    public boolean isThirdPartyLogin() {
        return "WECHAT".equals(loginType) || "GOOGLE".equals(loginType) || "FACEBOOK".equals(loginType);
    }

    /**
     * 计算在线时长
     */
    public void calculateOnlineDuration() {
        if (loginTime != null && logoutTime != null) {
            this.onlineDuration = java.time.Duration.between(loginTime, logoutTime).getSeconds();
        }
    }

    /**
     * 设置登出时间并计算在线时长
     *
     * @param logoutTime 登出时间
     */
    public void setLogoutTimeAndCalculateDuration(LocalDateTime logoutTime) {
        this.logoutTime = logoutTime;
        calculateOnlineDuration();
    }
}
