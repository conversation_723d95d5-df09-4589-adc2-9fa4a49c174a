package com.hncboy.chatgpt.db.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.user.UserJointLoginDO;

import java.util.List;

/**
 * 用户联合登录服务接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的IService，提供标准CRUD操作
 * 2. 采用MyBatis-Plus标准CRUD，无业务逻辑
 * 3. 仅在db层保留Service，业务逻辑移至Worker层
 * 4. 提供基础的数据访问方法
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public interface UserJointLoginService extends IService<UserJointLoginDO> {

    /**
     * 根据用户ID查询联合登录信息列表
     *
     * @param userId 用户ID
     * @return 联合登录信息列表
     */
    List<UserJointLoginDO> listByUserId(Integer userId);

    /**
     * 根据登录类型查询联合登录信息列表
     *
     * @param loginType 登录类型
     * @return 联合登录信息列表
     */
    List<UserJointLoginDO> listByLoginType(String loginType);

    /**
     * 根据第三方唯一标识查询联合登录信息
     *
     * @param thirdPartyId 第三方唯一标识
     * @return 联合登录信息
     */
    UserJointLoginDO getByThirdPartyId(String thirdPartyId);

    /**
     * 根据第三方唯一标识和登录类型查询联合登录信息
     *
     * @param thirdPartyId 第三方唯一标识
     * @param loginType 登录类型
     * @return 联合登录信息
     */
    UserJointLoginDO getByThirdPartyIdAndLoginType(String thirdPartyId, String loginType);

    /**
     * 根据联合ID查询联合登录信息
     *
     * @param unionId 联合ID
     * @return 联合登录信息
     */
    UserJointLoginDO getByUnionId(String unionId);

    /**
     * 根据应用ID查询联合登录信息列表
     *
     * @param appId 应用ID
     * @return 联合登录信息列表
     */
    List<UserJointLoginDO> listByAppId(String appId);

    /**
     * 根据推荐人ID查询被推荐用户列表
     *
     * @param referrerId 推荐人ID
     * @return 被推荐用户列表
     */
    List<UserJointLoginDO> listByReferrerId(Long referrerId);

    /**
     * 根据状态查询联合登录信息列表
     *
     * @param status 状态
     * @return 联合登录信息列表
     */
    List<UserJointLoginDO> listByStatus(Integer status);

    /**
     * 查询启用状态的联合登录信息列表
     *
     * @return 启用状态的联合登录信息列表
     */
    List<UserJointLoginDO> listEnabledLogins();

    /**
     * 查询禁用状态的联合登录信息列表
     *
     * @return 禁用状态的联合登录信息列表
     */
    List<UserJointLoginDO> listDisabledLogins();

    /**
     * 查询微信登录信息列表
     *
     * @return 微信登录信息列表
     */
    List<UserJointLoginDO> listWechatLogins();

    /**
     * 查询Google登录信息列表
     *
     * @return Google登录信息列表
     */
    List<UserJointLoginDO> listGoogleLogins();

    /**
     * 查询Facebook登录信息列表
     *
     * @return Facebook登录信息列表
     */
    List<UserJointLoginDO> listFacebookLogins();

    /**
     * 查询手机登录信息列表
     *
     * @return 手机登录信息列表
     */
    List<UserJointLoginDO> listPhoneLogins();

    /**
     * 查询邮箱登录信息列表
     *
     * @return 邮箱登录信息列表
     */
    List<UserJointLoginDO> listEmailLogins();

    /**
     * 查询指纹登录信息列表
     *
     * @return 指纹登录信息列表
     */
    List<UserJointLoginDO> listFingerprintLogins();

    /**
     * 更新登录次数
     *
     * @param id 主键ID
     * @return 是否成功
     */
    boolean updateLoginCount(Long id);

    /**
     * 更新幸运币
     *
     * @param id 主键ID
     * @param luckyCoins 幸运币变化量
     * @return 是否成功
     */
    boolean updateLuckyCoins(Long id, Long luckyCoins);

    /**
     * 根据用户ID和登录类型查询联合登录信息
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 联合登录信息
     */
    UserJointLoginDO getByUserIdAndLoginType(Integer userId, String loginType);

    /**
     * 统计联合登录总数
     *
     * @return 联合登录总数
     */
    Long countTotalLogins();

    /**
     * 根据登录类型统计联合登录数量
     *
     * @param loginType 登录类型
     * @return 联合登录数量
     */
    Long countByLoginType(String loginType);

    /**
     * 统计启用状态的联合登录数量
     *
     * @return 启用状态的联合登录数量
     */
    Long countEnabledLogins();

    /**
     * 统计有推荐人的联合登录数量
     *
     * @return 有推荐人的联合登录数量
     */
    Long countLoginsWithReferrer();

    /**
     * 统计有幸运币的联合登录数量
     *
     * @return 有幸运币的联合登录数量
     */
    Long countLoginsWithLuckyCoins();

    /**
     * 查询最近登录的联合登录信息列表
     *
     * @param limit 限制数量
     * @return 最近登录的联合登录信息列表
     */
    List<UserJointLoginDO> listRecentLogins(Integer limit);

    /**
     * 查询登录次数最多的联合登录信息列表
     *
     * @param limit 限制数量
     * @return 登录次数最多的联合登录信息列表
     */
    List<UserJointLoginDO> listMostActiveLogins(Integer limit);
}
