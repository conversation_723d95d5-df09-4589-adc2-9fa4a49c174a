package com.hncboy.chatgpt.db.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.db.entity.system.ExceptionLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常日志Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:45
 */
@Mapper
public interface ExceptionLogMapper extends BaseMapper<ExceptionLogDO> {

    /**
     * 分页查询异常日志
     *
     * @param page        分页参数
     * @param level       异常级别
     * @param status      处理状态
     * @param bizScene    业务场景
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 异常日志列表
     */
    IPage<ExceptionLogDO> selectByConditions(Page<ExceptionLogDO> page, 
                                             @Param("level") String level, 
                                             @Param("status") String status, 
                                             @Param("bizScene") String bizScene, 
                                             @Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 根据异常类型查询异常日志
     *
     * @param exceptionType 异常类型
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param limit         限制数量
     * @return 异常日志列表
     */
    List<ExceptionLogDO> selectByExceptionType(@Param("exceptionType") String exceptionType, 
                                               @Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime, 
                                               @Param("limit") Integer limit);

    /**
     * 根据用户ID查询异常日志
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 异常日志列表
     */
    List<ExceptionLogDO> selectByUserId(@Param("userId") Integer userId, 
                                        @Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 根据处理状态查询异常日志
     *
     * @param status 处理状态
     * @param limit  限制数量
     * @return 异常日志列表
     */
    List<ExceptionLogDO> selectByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 统计异常日志数量
     *
     * @param level     异常级别
     * @param status    处理状态
     * @param bizScene  业务场景
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 异常日志数量
     */
    Long countByConditions(@Param("level") String level, 
                           @Param("status") String status, 
                           @Param("bizScene") String bizScene, 
                           @Param("startTime") LocalDateTime startTime, 
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 统计异常类型分布
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 异常类型统计列表
     */
    List<ExceptionLogDO> selectExceptionTypeStats(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  @Param("limit") Integer limit);

    /**
     * 统计业务场景异常分布
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 业务场景异常统计列表
     */
    List<ExceptionLogDO> selectBizSceneStats(@Param("startTime") LocalDateTime startTime, 
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 批量更新处理状态
     *
     * @param ids           异常日志ID列表
     * @param status        处理状态
     * @param handler       处理人
     * @param handleRemark  处理备注
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, 
                          @Param("status") String status, 
                          @Param("handler") String handler, 
                          @Param("handleRemark") String handleRemark);

    /**
     * 删除过期的异常日志
     *
     * @param beforeTime 时间点
     * @return 删除数量
     */
    int deleteExpiredLogs(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询高频异常
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 高频异常列表
     */
    List<ExceptionLogDO> selectFrequentExceptions(@Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime, 
                                                  @Param("limit") Integer limit);
}
