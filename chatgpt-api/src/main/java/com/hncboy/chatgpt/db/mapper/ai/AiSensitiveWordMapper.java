package com.hncboy.chatgpt.db.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.ai.AiSensitiveWordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI敏感词Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:35
 */
@Mapper
public interface AiSensitiveWordMapper extends BaseMapper<AiSensitiveWordDO> {

    /**
     * 根据语言查询敏感词列表
     *
     * @param language 语言
     * @param status   状态
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectByLanguageAndStatus(@Param("language") String language, @Param("status") Integer status);

    /**
     * 根据敏感词类型查询敏感词列表
     *
     * @param wordType 敏感词类型
     * @param status   状态
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectByWordTypeAndStatus(@Param("wordType") String wordType, @Param("status") Integer status);

    /**
     * 根据敏感等级查询敏感词列表
     *
     * @param severityLevel 敏感等级
     * @param status        状态
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectBySeverityLevelAndStatus(@Param("severityLevel") Integer severityLevel, @Param("status") Integer status);

    /**
     * 根据适用场景查询敏感词列表
     *
     * @param scene  适用场景
     * @param status 状态
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectBySceneAndStatus(@Param("scene") String scene, @Param("status") Integer status);

    /**
     * 查询所有启用的敏感词
     *
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectAllEnabled();

    /**
     * 根据敏感词查询
     *
     * @param word 敏感词
     * @return 敏感词对象
     */
    AiSensitiveWordDO selectByWord(@Param("word") String word);

    /**
     * 批量查询敏感词
     *
     * @param words 敏感词列表
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectByWords(@Param("words") List<String> words);

    /**
     * 增加匹配次数
     *
     * @param id 敏感词ID
     * @return 更新数量
     */
    int incrementMatchCount(@Param("id") Integer id);

    /**
     * 批量增加匹配次数
     *
     * @param ids 敏感词ID列表
     * @return 更新数量
     */
    int batchIncrementMatchCount(@Param("ids") List<Integer> ids);

    /**
     * 统计敏感词数量
     *
     * @param wordType      敏感词类型
     * @param severityLevel 敏感等级
     * @param language      语言
     * @return 敏感词数量
     */
    Long countByConditions(@Param("wordType") String wordType, 
                           @Param("severityLevel") Integer severityLevel, 
                           @Param("language") String language);

    /**
     * 查询高频匹配的敏感词
     *
     * @param limit 限制数量
     * @return 敏感词列表
     */
    List<AiSensitiveWordDO> selectTopMatchedWords(@Param("limit") Integer limit);
}
