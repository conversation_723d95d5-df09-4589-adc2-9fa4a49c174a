package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户基础信息实体类
 *
 * 重构说明:
 * 1. 增强多语言支持，新增language、currency、timezone字段
 * 2. 保留分佣功能，commission_id和parent_id字段
 * 3. 修正POINTS概念，区分积分(points)和塔罗币(tarot_coins)
 * 4. 支持多业务场景，user_type字段区分不同业务
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_base_info")
@Schema(description = "用户基础信息")
public class UserBaseInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 分佣身份ID(关联commission_identity.id)
     */
    @TableField("commission_id")
    @Schema(description = "分佣身份ID")
    private Integer commissionId;

    /**
     * 账号(手机号或其他账号)
     */
    @TableField("account")
    @Schema(description = "账号")
    private String account;

    /**
     * 用户类型 zns:智能社 tarot:塔罗 chatoi:对话
     */
    @TableField("user_type")
    @Schema(description = "用户类型")
    private String userType;

    /**
     * 用户名
     */
    @TableField("name")
    @Schema(description = "用户名")
    private String name;

    /**
     * 昵称
     */
    @TableField("nick_name")
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 上级用户id（邀请人id，用于分佣）
     */
    @TableField("parent_id")
    @Schema(description = "上级用户ID")
    private Integer parentId;

    /**
     * 密码
     */
    @TableField("password")
    @Schema(description = "密码")
    private String password;

    /**
     * 邮箱
     */
    @TableField("email")
    @Schema(description = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    @Schema(description = "手机号")
    private String phone;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    @Schema(description = "头像URL")
    private String avatarUrl;

    /**
     * VIP到期时间
     */
    @TableField("vip_end_time")
    @Schema(description = "VIP到期时间")
    private LocalDateTime vipEndTime;

    /**
     * 积分(区别于POINTS塔罗币)
     */
    @TableField("points")
    @Schema(description = "积分")
    private Integer points;

    /**
     * POINTS塔罗币(用于塔罗解读)
     */
    @TableField("tarot_coins")
    @Schema(description = "塔罗币")
    private Integer tarotCoins;

    /**
     * 绘画次数
     */
    @TableField("draw_num")
    @Schema(description = "绘画次数")
    private Integer drawNum;

    /**
     * 音乐次数
     */
    @TableField("music_num")
    @Schema(description = "音乐次数")
    private Integer musicNum;

    /**
     * 写作次数
     */
    @TableField("write_num")
    @Schema(description = "写作次数")
    private Integer writeNum;

    /**
     * 剩余使用次数(充值)
     */
    @TableField("use_num")
    @Schema(description = "剩余使用次数")
    private Integer useNum;

    /**
     * 免费使用次数(赠送)
     */
    @TableField("free_num")
    @Schema(description = "免费使用次数")
    private Integer freeNum;

    /**
     * 每日免费次数
     */
    @TableField("daily_free_time")
    @Schema(description = "每日免费次数")
    private Integer dailyFreeTime;

    /**
     * 最后登录时间
     */
    @TableField("login_time")
    @Schema(description = "最后登录时间")
    private LocalDateTime loginTime;

    /**
     * 是否首次登录 1:是 0:否
     */
    @TableField("first_status")
    @Schema(description = "是否首次登录")
    private String firstStatus;

    /**
     * IP地址
     */
    @TableField("ip")
    @Schema(description = "IP地址")
    private String ip;

    /**
     * 地址
     */
    @TableField("address")
    @Schema(description = "地址")
    private String address;

    /**
     * 状态 0:正常 1:禁用 2:注销
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 用户语言偏好 zh_CN:中文 en_US:英文 vi_VN:越南语
     */
    @TableField("language")
    @Schema(description = "用户语言偏好")
    private String language;

    /**
     * 用户币种偏好 CNY:人民币 USD:美元 VND:越南盾
     */
    @TableField("currency")
    @Schema(description = "用户币种偏好")
    private String currency;

    /**
     * 用户时区 Asia/Shanghai:上海 America/New_York:纽约 Asia/Ho_Chi_Minh:胡志明市
     */
    @TableField("timezone")
    @Schema(description = "用户时区")
    private String timezone;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查用户是否为VIP
     *
     * @return true:是VIP false:不是VIP
     */
    public boolean isVip() {
        return vipEndTime != null && vipEndTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查用户状态是否正常
     *
     * @return true:正常 false:异常
     */
    public boolean isNormal() {
        return status != null && status == 0;
    }

    /**
     * 检查是否首次登录
     *
     * @return true:首次登录 false:非首次登录
     */
    public boolean isFirstLogin() {
        return "1".equals(firstStatus);
    }

    /**
     * 获取用户显示名称(优先昵称，其次用户名，最后账号)
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (nickName != null && !nickName.trim().isEmpty()) {
            return nickName;
        }
        if (name != null && !name.trim().isEmpty()) {
            return name;
        }
        return account;
    }

    /**
     * 获取总可用次数(充值次数+免费次数)
     *
     * @return 总可用次数
     */
    public int getTotalAvailableTimes() {
        int use = useNum != null ? useNum : 0;
        int free = freeNum != null ? freeNum : 0;
        return use + free;
    }

    /**
     * 扣减使用次数(优先扣减免费次数)
     *
     * @param count 扣减数量
     * @return 是否成功
     */
    public boolean deductUseTimes(int count) {
        if (count <= 0) {
            return false;
        }

        int totalAvailable = getTotalAvailableTimes();
        if (totalAvailable < count) {
            return false;
        }

        int freeCount = freeNum != null ? freeNum : 0;
        if (freeCount >= count) {
            // 免费次数足够，直接扣减免费次数
            this.freeNum = freeCount - count;
        } else {
            // 免费次数不够，先扣完免费次数，再扣减付费次数
            int remainCount = count - freeCount;
            this.freeNum = 0;
            this.useNum = (this.useNum != null ? this.useNum : 0) - remainCount;
        }

        return true;
    }

    /**
     * 增加使用次数
     *
     * @param count 增加数量
     */
    public void addUseTimes(int count) {
        this.useNum = (this.useNum != null ? this.useNum : 0) + count;
    }

    /**
     * 增加免费次数
     *
     * @param count 增加数量
     */
    public void addFreeTimes(int count) {
        this.freeNum = (this.freeNum != null ? this.freeNum : 0) + count;
    }

    /**
     * 扣减绘画次数
     *
     * @param count 扣减数量
     * @return 是否成功
     */
    public boolean deductDrawTimes(int count) {
        if (count <= 0) {
            return false;
        }

        int currentDraw = drawNum != null ? drawNum : 0;
        if (currentDraw < count) {
            return false;
        }

        this.drawNum = currentDraw - count;
        return true;
    }

    /**
     * 增加绘画次数
     *
     * @param count 增加数量
     */
    public void addDrawTimes(int count) {
        this.drawNum = (this.drawNum != null ? this.drawNum : 0) + count;
    }

    /**
     * 扣减塔罗币
     *
     * @param count 扣减数量
     * @return 是否成功
     */
    public boolean deductTarotCoins(int count) {
        if (count <= 0) {
            return false;
        }

        int currentCoins = tarotCoins != null ? tarotCoins : 0;
        if (currentCoins < count) {
            return false;
        }

        this.tarotCoins = currentCoins - count;
        return true;
    }

    /**
     * 增加塔罗币
     *
     * @param count 增加数量
     */
    public void addTarotCoins(int count) {
        this.tarotCoins = (this.tarotCoins != null ? this.tarotCoins : 0) + count;
    }

    /**
     * 扣减积分
     *
     * @param count 扣减数量
     * @return 是否成功
     */
    public boolean deductPoints(int count) {
        if (count <= 0) {
            return false;
        }

        int currentPoints = points != null ? points : 0;
        if (currentPoints < count) {
            return false;
        }

        this.points = currentPoints - count;
        return true;
    }

    /**
     * 增加积分
     *
     * @param count 增加数量
     */
    public void addPoints(int count) {
        this.points = (this.points != null ? this.points : 0) + count;
    }

    /**
     * 检查是否为智能社用户
     *
     * @return true:智能社用户 false:其他用户
     */
    public boolean isZnsUser() {
        return "zns".equals(userType);
    }

    /**
     * 检查是否为塔罗用户
     *
     * @return true:塔罗用户 false:其他用户
     */
    public boolean isTarotUser() {
        return "tarot".equals(userType);
    }

    /**
     * 检查是否为对话用户
     *
     * @return true:对话用户 false:其他用户
     */
    public boolean isChatoiUser() {
        return "chatoi".equals(userType);
    }

    /**
     * 检查用户是否被禁用
     *
     * @return true:被禁用 false:正常
     */
    public boolean isDisabled() {
        return status != null && status == 1;
    }

    /**
     * 检查用户是否已注销
     *
     * @return true:已注销 false:正常
     */
    public boolean isCancelled() {
        return status != null && status == 2;
    }

    /**
     * 设置为首次登录
     */
    public void setFirstLogin() {
        this.firstStatus = "1";
    }

    /**
     * 设置为非首次登录
     */
    public void setNotFirstLogin() {
        this.firstStatus = "0";
    }

    /**
     * 更新登录信息
     *
     * @param ip 登录IP
     */
    public void updateLoginInfo(String ip) {
        this.loginTime = LocalDateTime.now();
        this.ip = ip;
        if (isFirstLogin()) {
            setNotFirstLogin();
        }
    }

    /**
     * 更新登录信息(完整版)
     *
     * @param ip IP地址
     * @param address 地址
     */
    public void updateLoginInfo(String ip, String address) {
        this.loginTime = LocalDateTime.now();
        this.ip = ip;
        this.address = address;
        if (isFirstLogin()) {
            setNotFirstLogin();
        }
    }

    /**
     * 设置用户语言偏好
     *
     * @param language 语言代码 zh_CN:中文 en_US:英文 vi_VN:越南语
     */
    public void setLanguagePreference(String language) {
        this.language = language;
    }

    /**
     * 设置用户币种偏好
     *
     * @param currency 币种代码 CNY:人民币 USD:美元 VND:越南盾
     */
    public void setCurrencyPreference(String currency) {
        this.currency = currency;
    }

    /**
     * 设置用户时区
     *
     * @param timezone 时区 Asia/Shanghai:上海 America/New_York:纽约 Asia/Ho_Chi_Minh:胡志明市
     */
    public void setTimezonePreference(String timezone) {
        this.timezone = timezone;
    }

    /**
     * 获取用户状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "正常";
            case 1:
                return "禁用";
            case 2:
                return "注销";
            default:
                return "未知";
        }
    }

    /**
     * 检查用户是否已注销
     *
     * @return true:已注销 false:正常
     */
    public boolean isDeactivated() {
        return status != null && status == 2;
    }

    /**
     * 获取用户类型描述
     *
     * @return 用户类型描述
     */
    public String getUserTypeDescription() {
        if (userType == null) {
            return "未知";
        }
        switch (userType) {
            case "zns":
                return "智能社";
            case "tarot":
                return "塔罗";
            case "chatoi":
                return "对话";
            default:
                return "未知";
        }
    }

    /**
     * 检查是否有分佣身份
     *
     * @return true:有分佣身份 false:无分佣身份
     */
    public boolean hasCommission() {
        return commissionId != null && commissionId > 0;
    }

    /**
     * 检查是否有上级用户
     *
     * @return true:有上级用户 false:无上级用户
     */
    public boolean hasParent() {
        return parentId != null && parentId > 0;
    }
}
