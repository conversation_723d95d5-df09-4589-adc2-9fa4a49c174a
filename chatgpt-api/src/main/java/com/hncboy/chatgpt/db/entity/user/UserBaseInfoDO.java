package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户基础信息实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_base_info")
@Schema(description = "用户基础信息")
public class UserBaseInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 分佣身份ID(关联commission_identity.id)
     */
    @TableField("commission_id")
    @Schema(description = "分佣身份ID")
    private Integer commissionId;

    /**
     * 账号(手机号或其他账号)
     */
    @TableField("account")
    @Schema(description = "账号")
    private String account;

    /**
     * 用户类型 zns:智能社 tarot:塔罗 chatoi:对话
     */
    @TableField("user_type")
    @Schema(description = "用户类型")
    private String userType;

    /**
     * 用户名
     */
    @TableField("name")
    @Schema(description = "用户名")
    private String name;

    /**
     * 昵称
     */
    @TableField("nick_name")
    @Schema(description = "昵称")
    private String nickName;

    /**
     * 上级用户id（邀请人id，用于分佣）
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * VIP到期时间
     */
    @TableField("vip_end_time")
    private LocalDateTime vipEndTime;

    /**
     * 积分(区别于POINTS塔罗币)
     */
    @TableField("points")
    private Integer points;

    /**
     * POINTS塔罗币(用于塔罗解读)
     */
    @TableField("tarot_coins")
    private Integer tarotCoins;

    /**
     * 绘画次数
     */
    @TableField("draw_num")
    private Integer drawNum;

    /**
     * 音乐次数
     */
    @TableField("music_num")
    private Integer musicNum;

    /**
     * 写作次数
     */
    @TableField("write_num")
    private Integer writeNum;

    /**
     * 剩余使用次数(充值)
     */
    @TableField("use_num")
    private Integer useNum;

    /**
     * 免费使用次数(赠送)
     */
    @TableField("free_num")
    private Integer freeNum;

    /**
     * 每日免费次数
     */
    @TableField("daily_free_time")
    private Integer dailyFreeTime;

    /**
     * 最后登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 是否首次登录 1:是 0:否
     */
    @TableField("first_status")
    private String firstStatus;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 状态 0:正常 1:禁用 2:注销
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户语言偏好
     */
    @TableField("language")
    private String language;

    /**
     * 用户币种偏好
     */
    @TableField("currency")
    private String currency;

    /**
     * 用户时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查用户是否为VIP
     *
     * @return true:是VIP false:不是VIP
     */
    public boolean isVip() {
        return vipEndTime != null && vipEndTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查用户状态是否正常
     *
     * @return true:正常 false:异常
     */
    public boolean isNormal() {
        return status != null && status == 0;
    }

    /**
     * 检查是否首次登录
     *
     * @return true:首次登录 false:非首次登录
     */
    public boolean isFirstLogin() {
        return "1".equals(firstStatus);
    }

    /**
     * 获取用户显示名称(优先昵称，其次用户名，最后账号)
     *
     * @return 显示名称
     */
    public String getDisplayName() {
        if (nickName != null && !nickName.trim().isEmpty()) {
            return nickName;
        }
        if (name != null && !name.trim().isEmpty()) {
            return name;
        }
        return account;
    }

    /**
     * 获取总可用次数(充值次数+免费次数)
     *
     * @return 总可用次数
     */
    public int getTotalAvailableTimes() {
        int use = useNum != null ? useNum : 0;
        int free = freeNum != null ? freeNum : 0;
        return use + free;
    }
}
