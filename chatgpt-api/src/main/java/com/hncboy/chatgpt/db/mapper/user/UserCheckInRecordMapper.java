package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserCheckInRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户签到记录Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加签到记录相关的自定义查询方法
 * 3. 支持多种签到类型查询：塔罗、智能社、对话
 * 4. 支持补签功能查询，is_make_up字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserCheckInRecordMapper extends BaseMapper<UserCheckInRecordDO> {

    /**
     * 根据用户ID查询签到记录列表
     *
     * @param userId 用户ID
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和签到日期查询签到记录
     *
     * @param userId 用户ID
     * @param checkInDate 签到日期
     * @return 签到记录
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND check_in_date = #{checkInDate}")
    UserCheckInRecordDO selectByUserIdAndDate(@Param("userId") Integer userId, @Param("checkInDate") String checkInDate);

    /**
     * 根据用户ID和签到类型查询签到记录列表
     *
     * @param userId 用户ID
     * @param type 签到类型
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND type = #{type} ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type);

    /**
     * 根据用户ID、签到日期和签到类型查询签到记录
     *
     * @param userId 用户ID
     * @param checkInDate 签到日期
     * @param type 签到类型
     * @return 签到记录
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND check_in_date = #{checkInDate} AND type = #{type}")
    UserCheckInRecordDO selectByUserIdAndDateAndType(@Param("userId") Integer userId, @Param("checkInDate") String checkInDate, @Param("type") String type);

    /**
     * 根据签到类型查询签到记录列表
     *
     * @param type 签到类型
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE type = #{type} ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectByType(@Param("type") String type);

    /**
     * 根据签到日期查询签到记录列表
     *
     * @param checkInDate 签到日期
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE check_in_date = #{checkInDate} ORDER BY create_time DESC")
    List<UserCheckInRecordDO> selectByDate(@Param("checkInDate") String checkInDate);

    /**
     * 查询补签记录列表
     *
     * @return 补签记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE is_make_up = '1' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectMakeUpRecords();

    /**
     * 根据用户ID查询补签记录列表
     *
     * @param userId 用户ID
     * @return 补签记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND is_make_up = '1' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectMakeUpRecordsByUserId(@Param("userId") Integer userId);

    /**
     * 查询正常签到记录列表
     *
     * @return 正常签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE is_make_up = '0' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectNormalRecords();

    /**
     * 根据用户ID查询正常签到记录列表
     *
     * @param userId 用户ID
     * @return 正常签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND is_make_up = '0' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectNormalRecordsByUserId(@Param("userId") Integer userId);

    /**
     * 查询塔罗签到记录列表
     *
     * @return 塔罗签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE type = 'TAROT' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectTarotRecords();

    /**
     * 查询智能社签到记录列表
     *
     * @return 智能社签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE type = 'ZNS' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectZnsRecords();

    /**
     * 查询对话签到记录列表
     *
     * @return 对话签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE type = 'CHATOI' ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectChatoiRecords();

    /**
     * 根据用户ID查询最近的签到记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近的签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} ORDER BY check_in_date DESC LIMIT #{limit}")
    List<UserCheckInRecordDO> selectRecentRecordsByUserId(@Param("userId") Integer userId, @Param("limit") Integer limit);

    /**
     * 根据用户ID和签到类型查询最近的签到记录
     *
     * @param userId 用户ID
     * @param type 签到类型
     * @param limit 限制数量
     * @return 最近的签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND type = #{type} ORDER BY check_in_date DESC LIMIT #{limit}")
    List<UserCheckInRecordDO> selectRecentRecordsByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type, @Param("limit") Integer limit);

    /**
     * 根据用户ID查询连续签到天数
     *
     * @param userId 用户ID
     * @param type 签到类型
     * @return 连续签到天数
     */
    @Select("SELECT IFNULL(MAX(continuous_days), 0) FROM user_check_in_record WHERE user_id = #{userId} AND type = #{type}")
    Integer selectMaxContinuousDaysByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type);

    /**
     * 根据用户ID和日期范围查询签到记录列表
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND check_in_date BETWEEN #{startDate} AND #{endDate} ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectByUserIdAndDateRange(@Param("userId") Integer userId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 根据用户ID和年月查询签到记录列表
     *
     * @param userId 用户ID
     * @param yearMonth 年月(格式：202501)
     * @return 签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND check_in_date LIKE CONCAT(#{yearMonth}, '%') ORDER BY check_in_date DESC")
    List<UserCheckInRecordDO> selectByUserIdAndYearMonth(@Param("userId") Integer userId, @Param("yearMonth") String yearMonth);

    /**
     * 统计签到记录总数
     *
     * @return 签到记录总数
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record")
    Long countTotalRecords();

    /**
     * 根据用户ID统计签到记录数量
     *
     * @param userId 用户ID
     * @return 签到记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Integer userId);

    /**
     * 根据签到类型统计签到记录数量
     *
     * @param type 签到类型
     * @return 签到记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE type = #{type}")
    Long countByType(@Param("type") String type);

    /**
     * 统计补签记录数量
     *
     * @return 补签记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE is_make_up = '1'")
    Long countMakeUpRecords();

    /**
     * 统计正常签到记录数量
     *
     * @return 正常签到记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE is_make_up = '0'")
    Long countNormalRecords();

    /**
     * 根据签到日期统计签到记录数量
     *
     * @param checkInDate 签到日期
     * @return 签到记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE check_in_date = #{checkInDate}")
    Long countByDate(@Param("checkInDate") String checkInDate);

    /**
     * 根据用户ID和签到类型统计签到记录数量
     *
     * @param userId 用户ID
     * @param type 签到类型
     * @return 签到记录数量
     */
    @Select("SELECT COUNT(*) FROM user_check_in_record WHERE user_id = #{userId} AND type = #{type}")
    Long countByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type);

    /**
     * 查询今日签到记录列表
     *
     * @return 今日签到记录列表
     */
    @Select("SELECT * FROM user_check_in_record WHERE check_in_date = DATE_FORMAT(NOW(), '%Y%m%d') ORDER BY create_time DESC")
    List<UserCheckInRecordDO> selectTodayRecords();

    /**
     * 根据用户ID查询今日签到记录
     *
     * @param userId 用户ID
     * @return 今日签到记录
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND check_in_date = DATE_FORMAT(NOW(), '%Y%m%d')")
    UserCheckInRecordDO selectTodayRecordByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和签到类型查询今日签到记录
     *
     * @param userId 用户ID
     * @param type 签到类型
     * @return 今日签到记录
     */
    @Select("SELECT * FROM user_check_in_record WHERE user_id = #{userId} AND type = #{type} AND check_in_date = DATE_FORMAT(NOW(), '%Y%m%d')")
    UserCheckInRecordDO selectTodayRecordByUserIdAndType(@Param("userId") Integer userId, @Param("type") String type);
}
