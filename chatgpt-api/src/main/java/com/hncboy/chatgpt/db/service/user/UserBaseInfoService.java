package com.hncboy.chatgpt.db.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;

import java.util.List;

/**
 * 用户基础信息服务接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的IService，提供标准CRUD操作
 * 2. 采用MyBatis-Plus标准CRUD，无业务逻辑
 * 3. 仅在db层保留Service，业务逻辑移至Worker层
 * 4. 提供基础的数据访问方法
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
public interface UserBaseInfoService extends IService<UserBaseInfoDO> {

    /**
     * 根据账号查询用户信息
     *
     * @param account 账号
     * @return 用户信息
     */
    UserBaseInfoDO getByAccount(String account);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    UserBaseInfoDO getByPhone(String phone);

    /**
     * 根据邮箱查询用户信息
     *
     * @param email 邮箱
     * @return 用户信息
     */
    UserBaseInfoDO getByEmail(String email);

    /**
     * 根据用户类型查询用户列表
     *
     * @param userType 用户类型
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByUserType(String userType);

    /**
     * 根据分佣身份ID查询用户列表
     *
     * @param commissionId 分佣身份ID
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByCommissionId(Integer commissionId);

    /**
     * 根据上级用户ID查询下级用户列表
     *
     * @param parentId 上级用户ID
     * @return 下级用户列表
     */
    List<UserBaseInfoDO> listByParentId(Integer parentId);

    /**
     * 查询VIP用户列表
     *
     * @return VIP用户列表
     */
    List<UserBaseInfoDO> listVipUsers();

    /**
     * 查询即将过期的VIP用户列表(7天内过期)
     *
     * @return 即将过期的VIP用户列表
     */
    List<UserBaseInfoDO> listExpiringSoonVipUsers();

    /**
     * 根据状态查询用户列表
     *
     * @param status 状态
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByStatus(Integer status);

    /**
     * 查询正常状态的用户列表
     *
     * @return 正常状态的用户列表
     */
    List<UserBaseInfoDO> listNormalUsers();

    /**
     * 查询被禁用的用户列表
     *
     * @return 被禁用的用户列表
     */
    List<UserBaseInfoDO> listDisabledUsers();

    /**
     * 查询已注销的用户列表
     *
     * @return 已注销的用户列表
     */
    List<UserBaseInfoDO> listDeactivatedUsers();

    /**
     * 更新用户积分
     *
     * @param userId 用户ID
     * @param points 积分变化量
     * @return 是否成功
     */
    boolean updatePoints(Integer userId, Integer points);

    /**
     * 更新用户塔罗币
     *
     * @param userId 用户ID
     * @param tarotCoins 塔罗币变化量
     * @return 是否成功
     */
    boolean updateTarotCoins(Integer userId, Integer tarotCoins);

    /**
     * 更新用户使用次数
     *
     * @param userId 用户ID
     * @param useNum 使用次数变化量
     * @return 是否成功
     */
    boolean updateUseNum(Integer userId, Integer useNum);

    /**
     * 更新用户免费次数
     *
     * @param userId 用户ID
     * @param freeNum 免费次数变化量
     * @return 是否成功
     */
    boolean updateFreeNum(Integer userId, Integer freeNum);

    /**
     * 根据语言偏好查询用户列表
     *
     * @param language 语言偏好
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByLanguage(String language);

    /**
     * 根据币种偏好查询用户列表
     *
     * @param currency 币种偏好
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByCurrency(String currency);

    /**
     * 根据时区查询用户列表
     *
     * @param timezone 时区
     * @return 用户列表
     */
    List<UserBaseInfoDO> listByTimezone(String timezone);

    /**
     * 统计用户总数
     *
     * @return 用户总数
     */
    Long countTotalUsers();

    /**
     * 根据用户类型统计用户数量
     *
     * @param userType 用户类型
     * @return 用户数量
     */
    Long countByUserType(String userType);

    /**
     * 统计VIP用户数量
     *
     * @return VIP用户数量
     */
    Long countVipUsers();

    /**
     * 统计有分佣身份的用户数量
     *
     * @return 有分佣身份的用户数量
     */
    Long countUsersWithCommission();

    /**
     * 统计有上级用户的用户数量
     *
     * @return 有上级用户的用户数量
     */
    Long countUsersWithParent();
}
