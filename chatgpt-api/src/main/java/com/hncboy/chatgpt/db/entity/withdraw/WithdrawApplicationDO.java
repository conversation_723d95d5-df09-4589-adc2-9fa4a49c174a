package com.hncboy.chatgpt.db.entity.withdraw;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_application")
public class WithdrawApplicationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请单号
     */
    @TableField("application_no")
    private String applicationNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 提现方式 ALIPAY:支付宝 WECHAT:微信 BANK:银行卡
     */
    @TableField("withdraw_method")
    private String withdrawMethod;

    /**
     * 提现账户信息(JSON)
     */
    @TableField("account_info")
    private String accountInfo;

    /**
     * 申请金额
     */
    @TableField("apply_amount")
    private BigDecimal applyAmount;

    /**
     * 手续费
     */
    @TableField("fee_amount")
    private BigDecimal feeAmount;

    /**
     * 实际到账金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 申请状态 PENDING:待审核 APPROVED:审核通过 REJECTED:审核拒绝 PROCESSING:处理中 COMPLETED:已完成 CANCELLED:已取消 FAILED:失败
     */
    @TableField("status")
    private String status;

    /**
     * 审核人ID
     */
    @TableField("reviewer_id")
    private Integer reviewerId;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private LocalDateTime reviewTime;

    /**
     * 审核备注
     */
    @TableField("review_remark")
    private String reviewRemark;

    /**
     * 处理人ID
     */
    @TableField("processor_id")
    private Integer processorId;

    /**
     * 处理时间
     */
    @TableField("process_time")
    private LocalDateTime processTime;

    /**
     * 处理备注
     */
    @TableField("process_remark")
    private String processRemark;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private LocalDateTime completeTime;

    /**
     * 第三方交易号
     */
    @TableField("third_party_trade_no")
    private String thirdPartyTradeNo;

    /**
     * 第三方响应(JSON)
     */
    @TableField("third_party_response")
    private String thirdPartyResponse;

    /**
     * 失败原因
     */
    @TableField("failure_reason")
    private String failureReason;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    @TableField("next_retry_time")
    private LocalDateTime nextRetryTime;

    /**
     * 申请IP
     */
    @TableField("apply_ip")
    private String applyIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否待审核
     *
     * @return true:待审核 false:非待审核
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否审核通过
     *
     * @return true:审核通过 false:未通过
     */
    public boolean isApproved() {
        return "APPROVED".equals(status);
    }

    /**
     * 检查是否审核拒绝
     *
     * @return true:审核拒绝 false:未拒绝
     */
    public boolean isRejected() {
        return "REJECTED".equals(status);
    }

    /**
     * 检查是否处理中
     *
     * @return true:处理中 false:非处理中
     */
    public boolean isProcessing() {
        return "PROCESSING".equals(status);
    }

    /**
     * 检查是否已完成
     *
     * @return true:已完成 false:未完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(status);
    }

    /**
     * 检查是否已取消
     *
     * @return true:已取消 false:未取消
     */
    public boolean isCancelled() {
        return "CANCELLED".equals(status);
    }

    /**
     * 检查是否失败
     *
     * @return true:失败 false:未失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 检查是否为终态
     *
     * @return true:终态 false:非终态
     */
    public boolean isFinalStatus() {
        return isCompleted() || isCancelled() || isFailed();
    }

    /**
     * 检查是否可以取消
     *
     * @return true:可以取消 false:不可以取消
     */
    public boolean canCancel() {
        return isPending() || isApproved();
    }

    /**
     * 检查是否可以重试
     *
     * @return true:可以重试 false:不可以重试
     */
    public boolean canRetry() {
        if (!isFailed()) {
            return false;
        }
        if (maxRetryCount == null || retryCount == null) {
            return true;
        }
        return retryCount < maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }

    /**
     * 计算手续费率
     *
     * @return 手续费率(0-100)
     */
    public double getFeeRate() {
        if (applyAmount == null || applyAmount.compareTo(BigDecimal.ZERO) == 0 || feeAmount == null) {
            return 0.0;
        }
        return feeAmount.divide(applyAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue();
    }

    /**
     * 计算处理耗时(秒)
     *
     * @return 处理耗时
     */
    public long getProcessDuration() {
        if (createTime == null || completeTime == null) {
            return 0;
        }
        return java.time.Duration.between(createTime, completeTime).getSeconds();
    }
}
