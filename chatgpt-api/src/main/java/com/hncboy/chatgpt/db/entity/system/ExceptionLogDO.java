package com.hncboy.chatgpt.db.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 异常日志实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exception_log")
@Schema(description = "异常日志")
public class ExceptionLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 异常类型
     */
    @TableField("exception_type")
    @Schema(description = "异常类型")
    private String exceptionType;

    /**
     * 异常消息
     */
    @TableField("exception_message")
    @Schema(description = "异常消息")
    private String exceptionMessage;

    /**
     * 异常堆栈
     */
    @TableField("stack_trace")
    @Schema(description = "异常堆栈")
    private String stackTrace;

    /**
     * 请求URI
     */
    @TableField("request_uri")
    @Schema(description = "请求URI")
    private String requestUri;

    /**
     * 请求方法
     */
    @TableField("request_method")
    @Schema(description = "请求方法")
    private String requestMethod;

    /**
     * 请求参数
     */
    @TableField("request_params")
    @Schema(description = "请求参数")
    private String requestParams;

    /**
     * 请求头
     */
    @TableField("request_headers")
    @Schema(description = "请求头")
    private String requestHeaders;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    @Schema(description = "客户端IP")
    private String clientIp;

    /**
     * 业务场景
     */
    @TableField("biz_scene")
    @Schema(description = "业务场景")
    private String bizScene;

    /**
     * 异常级别 ERROR:错误 WARN:警告 FATAL:致命
     */
    @TableField("level")
    @Schema(description = "异常级别")
    private String level;

    /**
     * 处理状态 PENDING:待处理 PROCESSING:处理中 RESOLVED:已解决 IGNORED:已忽略
     */
    @TableField("status")
    @Schema(description = "处理状态")
    private String status;

    /**
     * 处理人
     */
    @TableField("handler")
    @Schema(description = "处理人")
    private String handler;

    /**
     * 处理时间
     */
    @TableField("handle_time")
    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    /**
     * 处理备注
     */
    @TableField("handle_remark")
    @Schema(description = "处理备注")
    private String handleRemark;

    /**
     * 服务器信息
     */
    @TableField("server_info")
    @Schema(description = "服务器信息")
    private String serverInfo;

    /**
     * 应用版本
     */
    @TableField("app_version")
    @Schema(description = "应用版本")
    private String appVersion;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 检查是否为错误级别
     *
     * @return true:错误级别 false:其他级别
     */
    public boolean isErrorLevel() {
        return "ERROR".equals(level);
    }

    /**
     * 检查是否为警告级别
     *
     * @return true:警告级别 false:其他级别
     */
    public boolean isWarnLevel() {
        return "WARN".equals(level);
    }

    /**
     * 检查是否为致命级别
     *
     * @return true:致命级别 false:其他级别
     */
    public boolean isFatalLevel() {
        return "FATAL".equals(level);
    }

    /**
     * 检查是否待处理
     *
     * @return true:待处理 false:其他状态
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }

    /**
     * 检查是否已解决
     *
     * @return true:已解决 false:其他状态
     */
    public boolean isResolved() {
        return "RESOLVED".equals(status);
    }

    /**
     * 检查是否已忽略
     *
     * @return true:已忽略 false:其他状态
     */
    public boolean isIgnored() {
        return "IGNORED".equals(status);
    }

    /**
     * 标记为已解决
     *
     * @param handler 处理人
     * @param remark  处理备注
     */
    public void markAsResolved(String handler, String remark) {
        this.status = "RESOLVED";
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemark = remark;
    }

    /**
     * 标记为已忽略
     *
     * @param handler 处理人
     * @param remark  处理备注
     */
    public void markAsIgnored(String handler, String remark) {
        this.status = "IGNORED";
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleRemark = remark;
    }
}
