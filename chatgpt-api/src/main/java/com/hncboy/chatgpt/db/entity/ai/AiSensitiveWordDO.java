package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI敏感词实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 13:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_sensitive_word")
@Schema(description = "AI敏感词")
public class AiSensitiveWordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 敏感词
     */
    @TableField("word")
    @Schema(description = "敏感词")
    private String word;

    /**
     * 敏感词类型 POLITICAL:政治 VIOLENCE:暴力 PORNOGRAPHY:色情 GAMBLING:赌博 DRUG:毒品 CUSTOM:自定义
     */
    @TableField("word_type")
    @Schema(description = "敏感词类型")
    private String wordType;

    /**
     * 敏感等级 1:低 2:中 3:高 4:严重
     */
    @TableField("severity_level")
    @Schema(description = "敏感等级")
    private Integer severityLevel;

    /**
     * 替换词
     */
    @TableField("replacement")
    @Schema(description = "替换词")
    private String replacement;

    /**
     * 语言 zh_CN:中文简体 en_US:英语 vi_VN:越南语 ja_JP:日语 ko_KR:韩语
     */
    @TableField("language")
    @Schema(description = "语言")
    private String language;

    /**
     * 适用场景 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐 ALL:全部
     */
    @TableField("applicable_scenes")
    @Schema(description = "适用场景")
    private String applicableScenes;

    /**
     * 处理动作 BLOCK:阻止 REPLACE:替换 WARN:警告
     */
    @TableField("action")
    @Schema(description = "处理动作")
    private String action;

    /**
     * 匹配次数
     */
    @TableField("match_count")
    @Schema(description = "匹配次数")
    private Long matchCount;

    /**
     * 最后匹配时间
     */
    @TableField("last_match_time")
    @Schema(description = "最后匹配时间")
    private LocalDateTime lastMatchTime;

    /**
     * 是否启用正则匹配 1:启用 0:不启用
     */
    @TableField("is_regex")
    @Schema(description = "是否启用正则匹配")
    private Integer isRegex;

    /**
     * 是否区分大小写 1:区分 0:不区分
     */
    @TableField("case_sensitive")
    @Schema(description = "是否区分大小写")
    private Integer caseSensitive;

    /**
     * 权重(用于排序)
     */
    @TableField("weight")
    @Schema(description = "权重")
    private Integer weight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否启用正则匹配
     *
     * @return true:启用 false:不启用
     */
    public boolean isRegexEnabled() {
        return isRegex != null && isRegex == 1;
    }

    /**
     * 检查是否区分大小写
     *
     * @return true:区分 false:不区分
     */
    public boolean isCaseSensitiveEnabled() {
        return caseSensitive != null && caseSensitive == 1;
    }

    /**
     * 检查是否为政治敏感词
     *
     * @return true:政治敏感词 false:其他类型
     */
    public boolean isPoliticalWord() {
        return "POLITICAL".equals(wordType);
    }

    /**
     * 检查是否为暴力敏感词
     *
     * @return true:暴力敏感词 false:其他类型
     */
    public boolean isViolenceWord() {
        return "VIOLENCE".equals(wordType);
    }

    /**
     * 检查是否为色情敏感词
     *
     * @return true:色情敏感词 false:其他类型
     */
    public boolean isPornographyWord() {
        return "PORNOGRAPHY".equals(wordType);
    }

    /**
     * 检查是否为高危敏感词
     *
     * @return true:高危敏感词 false:普通敏感词
     */
    public boolean isHighRiskWord() {
        return severityLevel != null && severityLevel >= 3;
    }

    /**
     * 检查处理动作是否为阻止
     *
     * @return true:阻止 false:其他动作
     */
    public boolean isBlockAction() {
        return "BLOCK".equals(action);
    }

    /**
     * 检查处理动作是否为替换
     *
     * @return true:替换 false:其他动作
     */
    public boolean isReplaceAction() {
        return "REPLACE".equals(action);
    }

    /**
     * 检查处理动作是否为警告
     *
     * @return true:警告 false:其他动作
     */
    public boolean isWarnAction() {
        return "WARN".equals(action);
    }

    /**
     * 增加匹配次数
     */
    public void incrementMatchCount() {
        this.matchCount = (this.matchCount == null ? 0 : this.matchCount) + 1;
        this.lastMatchTime = LocalDateTime.now();
    }

    /**
     * 检查是否适用于指定场景
     *
     * @param scene 场景
     * @return true:适用 false:不适用
     */
    public boolean isApplicableToScene(String scene) {
        if (applicableScenes == null || "ALL".equals(applicableScenes)) {
            return true;
        }
        return applicableScenes.contains(scene);
    }

    /**
     * 获取敏感等级描述
     *
     * @return 敏感等级描述
     */
    public String getSeverityLevelDesc() {
        if (severityLevel == null) {
            return "未知";
        }
        switch (severityLevel) {
            case 1:
                return "低";
            case 2:
                return "中";
            case 3:
                return "高";
            case 4:
                return "严重";
            default:
                return "未知";
        }
    }
}
