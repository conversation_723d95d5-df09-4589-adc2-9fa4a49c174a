package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一房间实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 11:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_room")
@Schema(description = "AI统一房间")
public class AiRoomDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 房间标题
     */
    @TableField("title")
    @Schema(description = "房间标题")
    private String title;

    /**
     * 房间描述
     */
    @TableField("description")
    @Schema(description = "房间描述")
    private String description;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    @Schema(description = "能力类型")
    private String abilityType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话 ALL:通用
     */
    @TableField("biz_scene")
    @Schema(description = "业务场景")
    private String bizScene;

    /**
     * 智能体ID
     */
    @TableField("agent_id")
    @Schema(description = "智能体ID")
    private Integer agentId;

    /**
     * 智能体名称
     */
    @TableField("agent_name")
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 模型标识
     */
    @TableField("model_gid")
    @Schema(description = "模型标识")
    private String modelGid;

    /**
     * 模型名称
     */
    @TableField("model_name")
    @Schema(description = "模型名称")
    private String modelName;

    /**
     * 系统提示词
     */
    @TableField("system_prompt")
    @Schema(description = "系统提示词")
    private String systemPrompt;

    /**
     * 温度参数
     */
    @TableField("temperature")
    @Schema(description = "温度参数")
    private Double temperature;

    /**
     * 最大Token数
     */
    @TableField("max_tokens")
    @Schema(description = "最大Token数")
    private Integer maxTokens;

    /**
     * 上下文长度
     */
    @TableField("context_length")
    @Schema(description = "上下文长度")
    private Integer contextLength;

    /**
     * 消息总数
     */
    @TableField("message_count")
    @Schema(description = "消息总数")
    private Integer messageCount;

    /**
     * 最后消息时间
     */
    @TableField("last_message_time")
    @Schema(description = "最后消息时间")
    private LocalDateTime lastMessageTime;

    /**
     * 最后消息内容
     */
    @TableField("last_message_content")
    @Schema(description = "最后消息内容")
    private String lastMessageContent;

    /**
     * 总输入Token数
     */
    @TableField("total_input_tokens")
    @Schema(description = "总输入Token数")
    private Long totalInputTokens;

    /**
     * 总输出Token数
     */
    @TableField("total_output_tokens")
    @Schema(description = "总输出Token数")
    private Long totalOutputTokens;

    /**
     * 总Token数
     */
    @TableField("total_tokens")
    @Schema(description = "总Token数")
    private Long totalTokens;

    /**
     * 平均响应时间(毫秒)
     */
    @TableField("avg_response_time")
    @Schema(description = "平均响应时间")
    private Long avgResponseTime;

    /**
     * 房间状态 ACTIVE:活跃 INACTIVE:非活跃 ARCHIVED:已归档
     */
    @TableField("status")
    @Schema(description = "房间状态")
    private String status;

    /**
     * 是否置顶 1:置顶 0:不置顶
     */
    @TableField("is_pinned")
    @Schema(description = "是否置顶")
    private Integer isPinned;

    /**
     * 是否收藏 1:收藏 0:不收藏
     */
    @TableField("is_favorite")
    @Schema(description = "是否收藏")
    private Integer isFavorite;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    @Schema(description = "客户端IP")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    @Schema(description = "用户代理")
    private String userAgent;

    /**
     * 扩展信息(JSON)
     */
    @TableField("extra_info")
    @Schema(description = "扩展信息")
    private String extraInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查房间是否活跃
     *
     * @return true:活跃 false:非活跃
     */
    public boolean isActive() {
        return "ACTIVE".equals(status);
    }

    /**
     * 检查房间是否已归档
     *
     * @return true:已归档 false:未归档
     */
    public boolean isArchived() {
        return "ARCHIVED".equals(status);
    }

    /**
     * 检查是否置顶
     *
     * @return true:置顶 false:不置顶
     */
    public boolean isPinnedRoom() {
        return isPinned != null && isPinned == 1;
    }

    /**
     * 检查是否收藏
     *
     * @return true:收藏 false:不收藏
     */
    public boolean isFavoriteRoom() {
        return isFavorite != null && isFavorite == 1;
    }

    /**
     * 增加消息数量
     */
    public void incrementMessageCount() {
        this.messageCount = (this.messageCount == null ? 0 : this.messageCount) + 1;
    }

    /**
     * 更新最后消息信息
     *
     * @param content 消息内容
     */
    public void updateLastMessage(String content) {
        this.lastMessageTime = LocalDateTime.now();
        this.lastMessageContent = content;
        incrementMessageCount();
    }

    /**
     * 更新Token统计
     *
     * @param inputTokens  输入Token数
     * @param outputTokens 输出Token数
     */
    public void updateTokenStats(int inputTokens, int outputTokens) {
        this.totalInputTokens = (this.totalInputTokens == null ? 0 : this.totalInputTokens) + inputTokens;
        this.totalOutputTokens = (this.totalOutputTokens == null ? 0 : this.totalOutputTokens) + outputTokens;
        this.totalTokens = (this.totalTokens == null ? 0 : this.totalTokens) + inputTokens + outputTokens;
    }

    /**
     * 更新平均响应时间
     *
     * @param responseTime 响应时间
     */
    public void updateAvgResponseTime(long responseTime) {
        if (this.avgResponseTime == null) {
            this.avgResponseTime = responseTime;
        } else {
            this.avgResponseTime = (this.avgResponseTime + responseTime) / 2;
        }
    }

    /**
     * 检查是否为对话类型
     *
     * @return true:对话类型 false:其他类型
     */
    public boolean isChatType() {
        return "CHAT".equals(abilityType);
    }

    /**
     * 检查是否为绘画类型
     *
     * @return true:绘画类型 false:其他类型
     */
    public boolean isDrawType() {
        return "DRAW".equals(abilityType);
    }

    /**
     * 检查是否为写作类型
     *
     * @return true:写作类型 false:其他类型
     */
    public boolean isWriteType() {
        return "WRITE".equals(abilityType);
    }

    /**
     * 检查是否为音乐类型
     *
     * @return true:音乐类型 false:其他类型
     */
    public boolean isMusicType() {
        return "MUSIC".equals(abilityType);
    }
}
