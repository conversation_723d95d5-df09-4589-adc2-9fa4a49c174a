package com.hncboy.chatgpt.db.service.ai.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.ai.AiMessageDO;
import com.hncboy.chatgpt.db.mapper.ai.AiMessageMapper;
import com.hncboy.chatgpt.db.service.ai.AiMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * AI消息服务实现类
 *
 * <AUTHOR>
 * @date 2023/3/22 14:00
 */
@Slf4j
@Service
public class AiMessageServiceImpl extends ServiceImpl<AiMessageMapper, AiMessageDO> implements AiMessageService {

    @Resource
    private AiMessageMapper aiMessageMapper;

    @Override
    public IPage<AiMessageDO> pageByRoomId(Page<AiMessageDO> page, Long roomId) {
        log.info("分页查询房间消息: roomId={}, page={}, size={}", roomId, page.getCurrent(), page.getSize());
        return aiMessageMapper.selectByRoomId(page, roomId);
    }

    @Override
    public List<AiMessageDO> listByRoomIdAndType(Long roomId, Integer messageType, Integer limit) {
        log.info("根据房间ID和消息类型查询消息: roomId={}, messageType={}, limit={}", roomId, messageType, limit);
        return aiMessageMapper.selectByRoomIdAndType(roomId, messageType, limit);
    }

    @Override
    public List<AiMessageDO> listByUserId(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("根据用户ID查询消息: userId={}, startTime={}, endTime={}", userId, startTime, endTime);
        return aiMessageMapper.selectByUserId(userId, startTime, endTime);
    }

    @Override
    public List<AiMessageDO> listByParentMsgId(Long parentMsgId) {
        log.info("根据父消息ID查询子消息: parentMsgId={}", parentMsgId);
        return aiMessageMapper.selectByParentMsgId(parentMsgId);
    }

    @Override
    public Long countByRoomId(Long roomId) {
        log.info("统计房间消息数量: roomId={}", roomId);
        return aiMessageMapper.countByRoomId(roomId);
    }

    @Override
    public Long countByUserIdAndAbilityType(Integer userId, String abilityType, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("统计用户消息数量: userId={}, abilityType={}, startTime={}, endTime={}", userId, abilityType, startTime, endTime);
        return aiMessageMapper.countByUserIdAndAbilityType(userId, abilityType, startTime, endTime);
    }

    @Override
    public Long sumTokensByUserIdAndAbilityType(Integer userId, String abilityType, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("统计Token使用量: userId={}, abilityType={}, startTime={}, endTime={}", userId, abilityType, startTime, endTime);
        return aiMessageMapper.sumTokensByUserIdAndAbilityType(userId, abilityType, startTime, endTime);
    }

    @Override
    public boolean deleteByRoomId(Long roomId) {
        log.info("删除房间所有消息: roomId={}", roomId);
        try {
            int result = aiMessageMapper.deleteByRoomId(roomId);
            log.info("删除房间消息成功: roomId={}, count={}", roomId, result);
            return result > 0;
        } catch (Exception e) {
            log.error("删除房间消息失败: roomId={}", roomId, e);
            return false;
        }
    }

    @Override
    public List<AiMessageDO> listRecentMessages(Long roomId, Integer limit) {
        log.info("查询最近消息: roomId={}, limit={}", roomId, limit);
        return aiMessageMapper.selectRecentMessages(roomId, limit);
    }

    @Override
    public List<AiMessageDO> listByStatus(String status, Integer limit) {
        log.info("根据状态查询消息: status={}, limit={}", status, limit);
        return aiMessageMapper.selectByStatus(status, limit);
    }

    @Override
    public boolean updateStatus(Long id, String status) {
        log.info("更新消息状态: id={}, status={}", id, status);
        try {
            int result = aiMessageMapper.updateStatus(id, status);
            return result > 0;
        } catch (Exception e) {
            log.error("更新消息状态失败: id={}, status={}", id, status, e);
            return false;
        }
    }

    @Override
    public boolean batchUpdateStatus(List<Long> ids, String status) {
        log.info("批量更新消息状态: ids={}, status={}", ids, status);
        try {
            int result = aiMessageMapper.batchUpdateStatus(ids, status);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新消息状态失败: ids={}, status={}", ids, status, e);
            return false;
        }
    }

    @Override
    public AiMessageDO createUserMessage(Long roomId, Integer userId, String content) {
        log.info("创建用户消息: roomId={}, userId={}, content={}", roomId, userId, StrUtil.brief(content, 100));
        
        AiMessageDO message = new AiMessageDO();
        message.setRoomId(roomId);
        message.setUserId(userId);
        message.setMessageType(1); // 用户消息
        message.setContent(content);
        message.setStatus("COMPLETED");
        
        try {
            save(message);
            log.info("创建用户消息成功: messageId={}", message.getId());
            return message;
        } catch (Exception e) {
            log.error("创建用户消息失败: roomId={}, userId={}", roomId, userId, e);
            return null;
        }
    }

    @Override
    public AiMessageDO createAiMessage(Long roomId, Integer userId, String content, Long parentMsgId) {
        log.info("创建AI回复消息: roomId={}, userId={}, parentMsgId={}, content={}", roomId, userId, parentMsgId, StrUtil.brief(content, 100));
        
        AiMessageDO message = new AiMessageDO();
        message.setRoomId(roomId);
        message.setUserId(userId);
        message.setMessageType(2); // AI回复
        message.setContent(content);
        message.setParentMsgId(parentMsgId);
        message.setStatus("COMPLETED");
        
        try {
            save(message);
            log.info("创建AI回复消息成功: messageId={}", message.getId());
            return message;
        } catch (Exception e) {
            log.error("创建AI回复消息失败: roomId={}, userId={}, parentMsgId={}", roomId, userId, parentMsgId, e);
            return null;
        }
    }

    @Override
    public AiMessageDO createSystemMessage(Long roomId, Integer userId, String content) {
        log.info("创建系统消息: roomId={}, userId={}, content={}", roomId, userId, StrUtil.brief(content, 100));
        
        AiMessageDO message = new AiMessageDO();
        message.setRoomId(roomId);
        message.setUserId(userId);
        message.setMessageType(3); // 系统消息
        message.setContent(content);
        message.setStatus("COMPLETED");
        
        try {
            save(message);
            log.info("创建系统消息成功: messageId={}", message.getId());
            return message;
        } catch (Exception e) {
            log.error("创建系统消息失败: roomId={}, userId={}", roomId, userId, e);
            return null;
        }
    }

    @Override
    public AiMessageDO createErrorMessage(Long roomId, Integer userId, String errorMessage, String errorCode) {
        log.info("创建错误消息: roomId={}, userId={}, errorCode={}, errorMessage={}", roomId, userId, errorCode, StrUtil.brief(errorMessage, 100));
        
        AiMessageDO message = new AiMessageDO();
        message.setRoomId(roomId);
        message.setUserId(userId);
        message.setMessageType(4); // 错误消息
        message.setContent(errorMessage);
        message.setErrorCode(errorCode);
        message.setErrorMessage(errorMessage);
        message.setStatus("FAILED");
        
        try {
            save(message);
            log.info("创建错误消息成功: messageId={}", message.getId());
            return message;
        } catch (Exception e) {
            log.error("创建错误消息失败: roomId={}, userId={}, errorCode={}", roomId, userId, errorCode, e);
            return null;
        }
    }

    @Override
    public boolean updateTokenStats(Long messageId, Integer inputTokens, Integer outputTokens, Long responseTime) {
        log.info("更新消息Token统计: messageId={}, inputTokens={}, outputTokens={}, responseTime={}", 
                messageId, inputTokens, outputTokens, responseTime);
        
        try {
            AiMessageDO message = getById(messageId);
            if (message == null) {
                log.warn("消息不存在: messageId={}", messageId);
                return false;
            }
            
            message.setInputTokens(inputTokens);
            message.setOutputTokens(outputTokens);
            message.setTotalTokens((inputTokens != null ? inputTokens : 0) + (outputTokens != null ? outputTokens : 0));
            message.setResponseTime(responseTime);
            
            updateById(message);
            log.info("更新消息Token统计成功: messageId={}", messageId);
            return true;
        } catch (Exception e) {
            log.error("更新消息Token统计失败: messageId={}", messageId, e);
            return false;
        }
    }
}
