package com.hncboy.chatgpt.db.entity.tarot;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 塔罗牌阵实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tarot_spread")
public class TarotSpreadDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 牌阵名称
     */
    @TableField("name")
    private String name;

    /**
     * 牌阵英文名称
     */
    @TableField("english_name")
    private String englishName;

    /**
     * 牌阵描述
     */
    @TableField("description")
    private String description;

    /**
     * 牌阵图片
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 牌阵类型
     */
    @TableField("spread_type")
    private String spreadType;

    /**
     * 牌的数量
     */
    @TableField("card_count")
    private Integer cardCount;

    /**
     * 牌位配置(JSON)
     */
    @TableField("positions")
    private String positions;

    /**
     * 适用场景
     */
    @TableField("suitable_scenes")
    private String suitableScenes;

    /**
     * 难度等级 1:简单 2:中等 3:困难
     */
    @TableField("difficulty_level")
    private Integer difficultyLevel;

    /**
     * 消耗塔罗币
     */
    @TableField("cost_coins")
    private Integer costCoins;

    /**
     * 使用次数
     */
    @TableField("use_count")
    private Long useCount;

    /**
     * 平均评分
     */
    @TableField("avg_rating")
    private Double avgRating;

    /**
     * 评分次数
     */
    @TableField("rating_count")
    private Long ratingCount;

    /**
     * 是否推荐 1:推荐 0:不推荐
     */
    @TableField("is_recommended")
    private Integer isRecommended;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    private Integer sortWeight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否推荐
     *
     * @return true:推荐 false:不推荐
     */
    public boolean isRecommendedSpread() {
        return isRecommended != null && isRecommended == 1;
    }

    /**
     * 检查是否为简单牌阵
     *
     * @return true:简单 false:不简单
     */
    public boolean isEasySpread() {
        return difficultyLevel != null && difficultyLevel == 1;
    }

    /**
     * 检查是否为中等牌阵
     *
     * @return true:中等 false:不是中等
     */
    public boolean isMediumSpread() {
        return difficultyLevel != null && difficultyLevel == 2;
    }

    /**
     * 检查是否为困难牌阵
     *
     * @return true:困难 false:不困难
     */
    public boolean isHardSpread() {
        return difficultyLevel != null && difficultyLevel == 3;
    }

    /**
     * 增加使用次数
     */
    public void incrementUseCount() {
        this.useCount = (this.useCount == null ? 0 : this.useCount) + 1;
    }

    /**
     * 更新评分
     *
     * @param rating 新评分
     */
    public void updateRating(int rating) {
        if (rating < 1 || rating > 5) {
            return;
        }
        
        if (this.avgRating == null || this.ratingCount == null) {
            this.avgRating = (double) rating;
            this.ratingCount = 1L;
        } else {
            double totalScore = this.avgRating * this.ratingCount + rating;
            this.ratingCount += 1;
            this.avgRating = totalScore / this.ratingCount;
        }
    }

    /**
     * 获取难度等级描述
     *
     * @return 难度等级描述
     */
    public String getDifficultyLevelDesc() {
        if (difficultyLevel == null) {
            return "未知";
        }
        switch (difficultyLevel) {
            case 1:
                return "简单";
            case 2:
                return "中等";
            case 3:
                return "困难";
            default:
                return "未知";
        }
    }
}
