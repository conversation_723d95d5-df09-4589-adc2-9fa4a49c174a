package com.hncboy.chatgpt.db.service.user.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import com.hncboy.chatgpt.db.mapper.user.UserBaseInfoMapper;
import com.hncboy.chatgpt.db.service.user.UserBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户基础信息服务实现类
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的ServiceImpl，提供标准CRUD操作
 * 2. 采用MyBatis-Plus标准CRUD，无业务逻辑
 * 3. 仅在db层保留Service，业务逻辑移至Worker层
 * 4. 提供基础的数据访问方法实现
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserBaseInfoServiceImpl extends ServiceImpl<UserBaseInfoMapper, UserBaseInfoDO> implements UserBaseInfoService {

    private final UserBaseInfoMapper userBaseInfoMapper;

    @Override
    public UserBaseInfoDO getByAccount(String account) {
        log.info("根据账号查询用户信息: {}", account);
        return userBaseInfoMapper.selectByAccount(account);
    }

    @Override
    public UserBaseInfoDO getByPhone(String phone) {
        log.info("根据手机号查询用户信息: {}", phone);
        return userBaseInfoMapper.selectByPhone(phone);
    }

    @Override
    public UserBaseInfoDO getByEmail(String email) {
        log.info("根据邮箱查询用户信息: {}", email);
        return userBaseInfoMapper.selectByEmail(email);
    }

    @Override
    public List<UserBaseInfoDO> listByUserType(String userType) {
        log.info("根据用户类型查询用户列表: {}", userType);
        return userBaseInfoMapper.selectByUserType(userType);
    }

    @Override
    public List<UserBaseInfoDO> listByCommissionId(Integer commissionId) {
        log.info("根据分佣身份ID查询用户列表: {}", commissionId);
        return userBaseInfoMapper.selectByCommissionId(commissionId);
    }

    @Override
    public List<UserBaseInfoDO> listByParentId(Integer parentId) {
        log.info("根据上级用户ID查询下级用户列表: {}", parentId);
        return userBaseInfoMapper.selectByParentId(parentId);
    }

    @Override
    public List<UserBaseInfoDO> listVipUsers() {
        log.info("查询VIP用户列表");
        return userBaseInfoMapper.selectVipUsers();
    }

    @Override
    public List<UserBaseInfoDO> listExpiringSoonVipUsers() {
        log.info("查询即将过期的VIP用户列表");
        return userBaseInfoMapper.selectExpiringSoonVipUsers();
    }

    @Override
    public List<UserBaseInfoDO> listByStatus(Integer status) {
        log.info("根据状态查询用户列表: {}", status);
        return userBaseInfoMapper.selectByStatus(status);
    }

    @Override
    public List<UserBaseInfoDO> listNormalUsers() {
        log.info("查询正常状态的用户列表");
        return userBaseInfoMapper.selectNormalUsers();
    }

    @Override
    public List<UserBaseInfoDO> listDisabledUsers() {
        log.info("查询被禁用的用户列表");
        return userBaseInfoMapper.selectDisabledUsers();
    }

    @Override
    public List<UserBaseInfoDO> listDeactivatedUsers() {
        log.info("查询已注销的用户列表");
        return userBaseInfoMapper.selectDeactivatedUsers();
    }

    @Override
    public boolean updatePoints(Integer userId, Integer points) {
        log.info("更新用户积分: userId={}, points={}", userId, points);
        int result = userBaseInfoMapper.updatePoints(userId, points);
        return result > 0;
    }

    @Override
    public boolean updateTarotCoins(Integer userId, Integer tarotCoins) {
        log.info("更新用户塔罗币: userId={}, tarotCoins={}", userId, tarotCoins);
        int result = userBaseInfoMapper.updateTarotCoins(userId, tarotCoins);
        return result > 0;
    }

    @Override
    public boolean updateUseNum(Integer userId, Integer useNum) {
        log.info("更新用户使用次数: userId={}, useNum={}", userId, useNum);
        int result = userBaseInfoMapper.updateUseNum(userId, useNum);
        return result > 0;
    }

    @Override
    public boolean updateFreeNum(Integer userId, Integer freeNum) {
        log.info("更新用户免费次数: userId={}, freeNum={}", userId, freeNum);
        int result = userBaseInfoMapper.updateFreeNum(userId, freeNum);
        return result > 0;
    }

    @Override
    public List<UserBaseInfoDO> listByLanguage(String language) {
        log.info("根据语言偏好查询用户列表: {}", language);
        return userBaseInfoMapper.selectByLanguage(language);
    }

    @Override
    public List<UserBaseInfoDO> listByCurrency(String currency) {
        log.info("根据币种偏好查询用户列表: {}", currency);
        return userBaseInfoMapper.selectByCurrency(currency);
    }

    @Override
    public List<UserBaseInfoDO> listByTimezone(String timezone) {
        log.info("根据时区查询用户列表: {}", timezone);
        return userBaseInfoMapper.selectByTimezone(timezone);
    }

    @Override
    public Long countTotalUsers() {
        log.info("统计用户总数");
        return userBaseInfoMapper.countTotalUsers();
    }

    @Override
    public Long countByUserType(String userType) {
        log.info("根据用户类型统计用户数量: {}", userType);
        return userBaseInfoMapper.countByUserType(userType);
    }

    @Override
    public Long countVipUsers() {
        log.info("统计VIP用户数量");
        return userBaseInfoMapper.countVipUsers();
    }

    @Override
    public Long countUsersWithCommission() {
        log.info("统计有分佣身份的用户数量");
        return userBaseInfoMapper.countUsersWithCommission();
    }

    @Override
    public Long countUsersWithParent() {
        log.info("统计有上级用户的用户数量");
        return userBaseInfoMapper.countUsersWithParent();
    }
}
