package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserJointLoginDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户联合登录Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加第三方登录相关的自定义查询方法
 * 3. 支持多种登录类型查询：微信、Google、Facebook等
 * 4. 支持推荐关系查询，referrer_id字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserJointLoginMapper extends BaseMapper<UserJointLoginDO> {

    /**
     * 根据用户ID查询联合登录信息列表
     *
     * @param userId 用户ID
     * @return 联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE user_id = #{userId} AND deleted = 0")
    List<UserJointLoginDO> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据登录类型查询联合登录信息列表
     *
     * @param loginType 登录类型
     * @return 联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = #{loginType} AND deleted = 0")
    List<UserJointLoginDO> selectByLoginType(@Param("loginType") String loginType);

    /**
     * 根据第三方唯一标识查询联合登录信息
     *
     * @param thirdPartyId 第三方唯一标识
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE third_party_id = #{thirdPartyId} AND deleted = 0")
    UserJointLoginDO selectByThirdPartyId(@Param("thirdPartyId") String thirdPartyId);

    /**
     * 根据第三方唯一标识和登录类型查询联合登录信息
     *
     * @param thirdPartyId 第三方唯一标识
     * @param loginType 登录类型
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE third_party_id = #{thirdPartyId} AND login_type = #{loginType} AND deleted = 0")
    UserJointLoginDO selectByThirdPartyIdAndLoginType(@Param("thirdPartyId") String thirdPartyId, @Param("loginType") String loginType);

    /**
     * 根据联合ID查询联合登录信息
     *
     * @param unionId 联合ID
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE union_id = #{unionId} AND deleted = 0")
    UserJointLoginDO selectByUnionId(@Param("unionId") String unionId);

    /**
     * 根据应用ID查询联合登录信息列表
     *
     * @param appId 应用ID
     * @return 联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE app_id = #{appId} AND deleted = 0")
    List<UserJointLoginDO> selectByAppId(@Param("appId") String appId);

    /**
     * 根据推荐人ID查询被推荐用户列表
     *
     * @param referrerId 推荐人ID
     * @return 被推荐用户列表
     */
    @Select("SELECT * FROM user_joint_login WHERE referrer_id = #{referrerId} AND deleted = 0")
    List<UserJointLoginDO> selectByReferrerId(@Param("referrerId") Long referrerId);

    /**
     * 根据状态查询联合登录信息列表
     *
     * @param status 状态
     * @return 联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE status = #{status} AND deleted = 0")
    List<UserJointLoginDO> selectByStatus(@Param("status") Integer status);

    /**
     * 查询启用状态的联合登录信息列表
     *
     * @return 启用状态的联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE status = 1 AND deleted = 0")
    List<UserJointLoginDO> selectEnabledLogins();

    /**
     * 查询禁用状态的联合登录信息列表
     *
     * @return 禁用状态的联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE status = 0 AND deleted = 0")
    List<UserJointLoginDO> selectDisabledLogins();

    /**
     * 查询微信登录信息列表
     *
     * @return 微信登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'WECHAT' AND deleted = 0")
    List<UserJointLoginDO> selectWechatLogins();

    /**
     * 查询Google登录信息列表
     *
     * @return Google登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'GOOGLE' AND deleted = 0")
    List<UserJointLoginDO> selectGoogleLogins();

    /**
     * 查询Facebook登录信息列表
     *
     * @return Facebook登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'FACEBOOK' AND deleted = 0")
    List<UserJointLoginDO> selectFacebookLogins();

    /**
     * 查询手机登录信息列表
     *
     * @return 手机登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'PHONE' AND deleted = 0")
    List<UserJointLoginDO> selectPhoneLogins();

    /**
     * 查询邮箱登录信息列表
     *
     * @return 邮箱登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'EMAIL' AND deleted = 0")
    List<UserJointLoginDO> selectEmailLogins();

    /**
     * 查询指纹登录信息列表
     *
     * @return 指纹登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = 'FINGERPRINT' AND deleted = 0")
    List<UserJointLoginDO> selectFingerprintLogins();

    /**
     * 更新登录次数
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET login_count = login_count + 1, last_login_time = NOW() WHERE id = #{id} AND deleted = 0")
    int updateLoginCount(@Param("id") Long id);

    /**
     * 更新幸运币
     *
     * @param id 主键ID
     * @param luckyCoins 幸运币变化量
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET lucky_coins = lucky_coins + #{luckyCoins} WHERE id = #{id} AND deleted = 0")
    int updateLuckyCoins(@Param("id") Long id, @Param("luckyCoins") Long luckyCoins);

    /**
     * 根据用户ID和登录类型查询联合登录信息
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE user_id = #{userId} AND login_type = #{loginType} AND deleted = 0")
    UserJointLoginDO selectByUserIdAndLoginType(@Param("userId") Integer userId, @Param("loginType") String loginType);

    /**
     * 统计联合登录总数
     *
     * @return 联合登录总数
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE deleted = 0")
    Long countTotalLogins();

    /**
     * 根据登录类型统计联合登录数量
     *
     * @param loginType 登录类型
     * @return 联合登录数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE login_type = #{loginType} AND deleted = 0")
    Long countByLoginType(@Param("loginType") String loginType);

    /**
     * 统计启用状态的联合登录数量
     *
     * @return 启用状态的联合登录数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE status = 1 AND deleted = 0")
    Long countEnabledLogins();

    /**
     * 统计有推荐人的联合登录数量
     *
     * @return 有推荐人的联合登录数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE referrer_id IS NOT NULL AND referrer_id > 0 AND deleted = 0")
    Long countLoginsWithReferrer();

    /**
     * 统计有幸运币的联合登录数量
     *
     * @return 有幸运币的联合登录数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE lucky_coins IS NOT NULL AND lucky_coins > 0 AND deleted = 0")
    Long countLoginsWithLuckyCoins();

    /**
     * 查询最近登录的联合登录信息列表
     *
     * @param limit 限制数量
     * @return 最近登录的联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE deleted = 0 ORDER BY last_login_time DESC LIMIT #{limit}")
    List<UserJointLoginDO> selectRecentLogins(@Param("limit") Integer limit);

    /**
     * 查询登录次数最多的联合登录信息列表
     *
     * @param limit 限制数量
     * @return 登录次数最多的联合登录信息列表
     */
    @Select("SELECT * FROM user_joint_login WHERE deleted = 0 ORDER BY login_count DESC LIMIT #{limit}")
    List<UserJointLoginDO> selectMostActiveLogins(@Param("limit") Integer limit);
}
