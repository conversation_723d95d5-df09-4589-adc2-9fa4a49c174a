package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户签到记录实体类
 * 
 * 重构说明:
 * 1. 合并user_check_in_record和app_sign表，统一签到记录管理
 * 2. 支持多业务场景签到：TAROT:塔罗牌 ZNS:智能社 CHATOI:对话
 * 3. 支持补签功能，is_make_up字段标识是否补签
 * 4. 支持多种奖励类型：POINTS:积分 COINS:代币
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_check_in_record")
@Schema(description = "用户签到记录")
public class UserCheckInRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 签到日期 yyyyMMDD
     */
    @TableField("check_in_date")
    @Schema(description = "签到日期")
    private String checkInDate;

    /**
     * 签到星期
     */
    @TableField("week")
    @Schema(description = "签到星期")
    private String week;

    /**
     * 签到类型 TAROT:塔罗牌 ZNS:智能社 CHATOI:对话
     */
    @TableField("type")
    @Schema(description = "签到类型")
    private String type;

    /**
     * 是否补签 0:否 1:是
     */
    @TableField("is_make_up")
    @Schema(description = "是否补签")
    private String isMakeUp;

    /**
     * 奖励数量
     */
    @TableField("awarded")
    @Schema(description = "奖励数量")
    private Integer awarded;

    /**
     * 奖励类型 POINTS:积分 COINS:代币
     */
    @TableField("award_type")
    @Schema(description = "奖励类型")
    private String awardType;

    /**
     * 连续签到天数
     */
    @TableField("continuous_days")
    @Schema(description = "连续签到天数")
    private Integer continuousDays;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 检查是否为补签
     *
     * @return true:补签 false:正常签到
     */
    public boolean isMakeUpSign() {
        return "1".equals(isMakeUp);
    }

    /**
     * 检查是否为塔罗签到
     *
     * @return true:塔罗签到 false:其他签到
     */
    public boolean isTarotCheckIn() {
        return "TAROT".equals(type);
    }

    /**
     * 检查是否为智能社签到
     *
     * @return true:智能社签到 false:其他签到
     */
    public boolean isZnsCheckIn() {
        return "ZNS".equals(type);
    }

    /**
     * 检查是否为对话签到
     *
     * @return true:对话签到 false:其他签到
     */
    public boolean isChatoiCheckIn() {
        return "CHATOI".equals(type);
    }

    /**
     * 检查奖励类型是否为积分
     *
     * @return true:积分奖励 false:其他奖励
     */
    public boolean isPointsAward() {
        return "POINTS".equals(awardType);
    }

    /**
     * 检查奖励类型是否为代币
     *
     * @return true:代币奖励 false:其他奖励
     */
    public boolean isCoinsAward() {
        return "COINS".equals(awardType);
    }

    /**
     * 设置为补签
     */
    public void setMakeUp() {
        this.isMakeUp = "1";
    }

    /**
     * 设置为正常签到
     */
    public void setNormalCheckIn() {
        this.isMakeUp = "0";
    }

    /**
     * 获取签到类型描述
     *
     * @return 签到类型描述
     */
    public String getTypeDescription() {
        if (type == null) {
            return "未知";
        }
        switch (type) {
            case "TAROT":
                return "塔罗牌";
            case "ZNS":
                return "智能社";
            case "CHATOI":
                return "对话";
            default:
                return "未知";
        }
    }

    /**
     * 获取奖励类型描述
     *
     * @return 奖励类型描述
     */
    public String getAwardTypeDescription() {
        if (awardType == null) {
            return "未知";
        }
        switch (awardType) {
            case "POINTS":
                return "积分";
            case "COINS":
                return "代币";
            default:
                return "未知";
        }
    }

    /**
     * 获取星期描述
     *
     * @return 星期描述
     */
    public String getWeekDescription() {
        if (week == null) {
            return "未知";
        }
        switch (week) {
            case "1":
                return "星期一";
            case "2":
                return "星期二";
            case "3":
                return "星期三";
            case "4":
                return "星期四";
            case "5":
                return "星期五";
            case "6":
                return "星期六";
            case "7":
                return "星期日";
            default:
                return "未知";
        }
    }

    /**
     * 增加连续签到天数
     */
    public void increaseContinuousDays() {
        this.continuousDays = (this.continuousDays != null ? this.continuousDays : 0) + 1;
    }

    /**
     * 重置连续签到天数
     */
    public void resetContinuousDays() {
        this.continuousDays = 1;
    }

    /**
     * 检查是否有奖励
     *
     * @return true:有奖励 false:无奖励
     */
    public boolean hasAward() {
        return awarded != null && awarded > 0;
    }

    /**
     * 获取签到状态描述
     *
     * @return 签到状态描述
     */
    public String getCheckInStatusDescription() {
        if (isMakeUpSign()) {
            return "补签";
        } else {
            return "正常签到";
        }
    }
}
