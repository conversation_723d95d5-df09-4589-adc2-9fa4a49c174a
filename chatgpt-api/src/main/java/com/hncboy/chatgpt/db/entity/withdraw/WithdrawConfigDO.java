package com.hncboy.chatgpt.db.entity.withdraw;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现配置实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_config")
public class WithdrawConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;

    /**
     * 提现方式 ALIPAY:支付宝 WECHAT:微信 BANK:银行卡
     */
    @TableField("withdraw_method")
    private String withdrawMethod;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 最小提现金额
     */
    @TableField("min_amount")
    private BigDecimal minAmount;

    /**
     * 最大提现金额
     */
    @TableField("max_amount")
    private BigDecimal maxAmount;

    /**
     * 每日提现限额
     */
    @TableField("daily_limit")
    private BigDecimal dailyLimit;

    /**
     * 每月提现限额
     */
    @TableField("monthly_limit")
    private BigDecimal monthlyLimit;

    /**
     * 手续费类型 FIXED:固定金额 RATE:费率
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 手续费值
     */
    @TableField("fee_value")
    private BigDecimal feeValue;

    /**
     * 最小手续费
     */
    @TableField("min_fee")
    private BigDecimal minFee;

    /**
     * 最大手续费
     */
    @TableField("max_fee")
    private BigDecimal maxFee;

    /**
     * 处理时间(小时)
     */
    @TableField("process_hours")
    private Integer processHours;

    /**
     * 工作日处理 1:仅工作日 0:全天候
     */
    @TableField("workday_only")
    private Integer workdayOnly;

    /**
     * 处理开始时间
     */
    @TableField("process_start_time")
    private String processStartTime;

    /**
     * 处理结束时间
     */
    @TableField("process_end_time")
    private String processEndTime;

    /**
     * 是否需要审核 1:需要 0:不需要
     */
    @TableField("need_review")
    private Integer needReview;

    /**
     * 自动审核金额阈值
     */
    @TableField("auto_review_threshold")
    private BigDecimal autoReviewThreshold;

    /**
     * 最大重试次数
     */
    @TableField("max_retry_count")
    private Integer maxRetryCount;

    /**
     * 重试间隔(分钟)
     */
    @TableField("retry_interval")
    private Integer retryInterval;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否需要审核
     *
     * @return true:需要审核 false:不需要审核
     */
    public boolean needsReview() {
        return needReview != null && needReview == 1;
    }

    /**
     * 检查是否仅工作日处理
     *
     * @return true:仅工作日 false:全天候
     */
    public boolean isWorkdayOnly() {
        return workdayOnly != null && workdayOnly == 1;
    }

    /**
     * 检查金额是否在允许范围内
     *
     * @param amount 金额
     * @return true:在范围内 false:超出范围
     */
    public boolean isAmountInRange(BigDecimal amount) {
        if (amount == null) {
            return false;
        }
        if (minAmount != null && amount.compareTo(minAmount) < 0) {
            return false;
        }
        if (maxAmount != null && amount.compareTo(maxAmount) > 0) {
            return false;
        }
        return true;
    }

    /**
     * 检查是否需要人工审核
     *
     * @param amount 金额
     * @return true:需要人工审核 false:可自动审核
     */
    public boolean needsManualReview(BigDecimal amount) {
        if (!needsReview()) {
            return false;
        }
        if (autoReviewThreshold == null) {
            return true;
        }
        return amount.compareTo(autoReviewThreshold) > 0;
    }

    /**
     * 计算手续费
     *
     * @param amount 提现金额
     * @return 手续费
     */
    public BigDecimal calculateFee(BigDecimal amount) {
        if (amount == null || feeValue == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal fee;
        if ("FIXED".equals(feeType)) {
            fee = feeValue;
        } else {
            // 费率计算
            fee = amount.multiply(feeValue).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
        }
        
        // 应用最小和最大手续费限制
        if (minFee != null && fee.compareTo(minFee) < 0) {
            fee = minFee;
        }
        if (maxFee != null && fee.compareTo(maxFee) > 0) {
            fee = maxFee;
        }
        
        return fee;
    }

    /**
     * 计算实际到账金额
     *
     * @param amount 申请金额
     * @return 实际到账金额
     */
    public BigDecimal calculateActualAmount(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal fee = calculateFee(amount);
        return amount.subtract(fee);
    }
}
