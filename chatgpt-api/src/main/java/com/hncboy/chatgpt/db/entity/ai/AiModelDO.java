package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI模型实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_model")
public class AiModelDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模型名称
     */
    @TableField("name")
    private String name;

    /**
     * 模型标识
     */
    @TableField("model_key")
    private String modelKey;

    /**
     * 模型描述
     */
    @TableField("description")
    private String description;

    /**
     * 模型提供商
     */
    @TableField("provider")
    private String provider;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    private String abilityType;

    /**
     * API端点
     */
    @TableField("api_endpoint")
    private String apiEndpoint;

    /**
     * API密钥
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 模型版本
     */
    @TableField("version")
    private String version;

    /**
     * 最大Token数
     */
    @TableField("max_tokens")
    private Integer maxTokens;

    /**
     * 输入价格(每1K Token)
     */
    @TableField("input_price")
    private BigDecimal inputPrice;

    /**
     * 输出价格(每1K Token)
     */
    @TableField("output_price")
    private BigDecimal outputPrice;

    /**
     * 价格币种
     */
    @TableField("price_currency")
    private String priceCurrency;

    /**
     * 支持的语言(JSON数组)
     */
    @TableField("supported_languages")
    private String supportedLanguages;

    /**
     * 模型配置(JSON)
     */
    @TableField("model_config")
    private String modelConfig;

    /**
     * 请求限制配置(JSON)
     */
    @TableField("rate_limit_config")
    private String rateLimitConfig;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 权重
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为对话模型
     *
     * @return true:对话模型 false:其他模型
     */
    public boolean isChatModel() {
        return "CHAT".equals(abilityType);
    }

    /**
     * 检查是否为绘画模型
     *
     * @return true:绘画模型 false:其他模型
     */
    public boolean isDrawModel() {
        return "DRAW".equals(abilityType);
    }

    /**
     * 检查是否为写作模型
     *
     * @return true:写作模型 false:其他模型
     */
    public boolean isWriteModel() {
        return "WRITE".equals(abilityType);
    }

    /**
     * 检查是否为音乐模型
     *
     * @return true:音乐模型 false:其他模型
     */
    public boolean isMusicModel() {
        return "MUSIC".equals(abilityType);
    }

    /**
     * 计算Token成本
     *
     * @param inputTokens  输入Token数
     * @param outputTokens 输出Token数
     * @return 成本
     */
    public BigDecimal calculateCost(int inputTokens, int outputTokens) {
        BigDecimal inputCost = BigDecimal.ZERO;
        BigDecimal outputCost = BigDecimal.ZERO;
        
        if (inputPrice != null && inputTokens > 0) {
            inputCost = inputPrice.multiply(BigDecimal.valueOf(inputTokens)).divide(BigDecimal.valueOf(1000), 6, BigDecimal.ROUND_HALF_UP);
        }
        
        if (outputPrice != null && outputTokens > 0) {
            outputCost = outputPrice.multiply(BigDecimal.valueOf(outputTokens)).divide(BigDecimal.valueOf(1000), 6, BigDecimal.ROUND_HALF_UP);
        }
        
        return inputCost.add(outputCost);
    }
}
