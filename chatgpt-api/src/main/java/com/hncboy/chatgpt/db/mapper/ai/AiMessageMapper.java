package com.hncboy.chatgpt.db.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.db.entity.ai.AiMessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI消息Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:30
 */
@Mapper
public interface AiMessageMapper extends BaseMapper<AiMessageDO> {

    /**
     * 根据房间ID分页查询消息列表
     *
     * @param page   分页参数
     * @param roomId 房间ID
     * @return 消息列表
     */
    IPage<AiMessageDO> selectByRoomId(Page<AiMessageDO> page, @Param("roomId") Long roomId);

    /**
     * 根据房间ID和消息类型查询消息列表
     *
     * @param roomId      房间ID
     * @param messageType 消息类型
     * @param limit       限制数量
     * @return 消息列表
     */
    List<AiMessageDO> selectByRoomIdAndType(@Param("roomId") Long roomId, 
                                            @Param("messageType") Integer messageType, 
                                            @Param("limit") Integer limit);

    /**
     * 根据用户ID查询消息列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 消息列表
     */
    List<AiMessageDO> selectByUserId(@Param("userId") Integer userId, 
                                     @Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 根据父消息ID查询子消息列表
     *
     * @param parentMsgId 父消息ID
     * @return 子消息列表
     */
    List<AiMessageDO> selectByParentMsgId(@Param("parentMsgId") Long parentMsgId);

    /**
     * 统计房间消息数量
     *
     * @param roomId 房间ID
     * @return 消息数量
     */
    Long countByRoomId(@Param("roomId") Long roomId);

    /**
     * 统计用户消息数量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 消息数量
     */
    Long countByUserIdAndAbilityType(@Param("userId") Integer userId, 
                                     @Param("abilityType") String abilityType,
                                     @Param("startTime") LocalDateTime startTime, 
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 统计Token使用量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return Token使用量
     */
    Long sumTokensByUserIdAndAbilityType(@Param("userId") Integer userId, 
                                         @Param("abilityType") String abilityType,
                                         @Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 删除房间的所有消息
     *
     * @param roomId 房间ID
     * @return 删除数量
     */
    int deleteByRoomId(@Param("roomId") Long roomId);

    /**
     * 查询最近的消息列表(用于上下文)
     *
     * @param roomId 房间ID
     * @param limit  限制数量
     * @return 消息列表
     */
    List<AiMessageDO> selectRecentMessages(@Param("roomId") Long roomId, @Param("limit") Integer limit);

    /**
     * 根据状态查询消息列表
     *
     * @param status 状态
     * @param limit  限制数量
     * @return 消息列表
     */
    List<AiMessageDO> selectByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 更新消息状态
     *
     * @param id     消息ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 批量更新消息状态
     *
     * @param ids    消息ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);
}
