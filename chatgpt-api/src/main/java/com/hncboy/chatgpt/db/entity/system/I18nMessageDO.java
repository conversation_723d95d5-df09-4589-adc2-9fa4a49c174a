package com.hncboy.chatgpt.db.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 国际化消息实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("i18n_message")
public class I18nMessageDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息键
     */
    @TableField("message_key")
    private String messageKey;

    /**
     * 语言代码
     */
    @TableField("language_code")
    private String languageCode;

    /**
     * 消息内容
     */
    @TableField("message_content")
    private String messageContent;

    /**
     * 消息分组
     */
    @TableField("message_group")
    private String messageGroup;

    /**
     * 消息模块
     */
    @TableField("message_module")
    private String messageModule;

    /**
     * 消息描述
     */
    @TableField("description")
    private String description;

    /**
     * 参数说明(JSON)
     */
    @TableField("parameters")
    private String parameters;

    /**
     * 是否支持参数化 1:支持 0:不支持
     */
    @TableField("is_parameterized")
    private Integer isParameterized;

    /**
     * 默认值
     */
    @TableField("default_value")
    private String defaultValue;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否支持参数化
     *
     * @return true:支持 false:不支持
     */
    public boolean isParameterizedMessage() {
        return isParameterized != null && isParameterized == 1;
    }

    /**
     * 获取有效的消息内容
     *
     * @return 消息内容
     */
    public String getEffectiveContent() {
        if (messageContent != null && !messageContent.trim().isEmpty()) {
            return messageContent;
        }
        return defaultValue;
    }
}
