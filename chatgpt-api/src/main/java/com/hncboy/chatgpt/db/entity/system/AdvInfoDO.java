package com.hncboy.chatgpt.db.entity.system;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 广告信息实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("adv_info")
public class AdvInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 广告标题
     */
    @TableField("title")
    private String title;

    /**
     * 广告内容
     */
    @TableField("content")
    private String content;

    /**
     * 广告图片
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 广告链接
     */
    @TableField("link_url")
    private String linkUrl;

    /**
     * 广告类型 BANNER:横幅 POPUP:弹窗 SIDEBAR:侧边栏 INLINE:内嵌
     */
    @TableField("adv_type")
    private String advType;

    /**
     * 展示位置
     */
    @TableField("position")
    private String position;

    /**
     * 目标用户类型 ALL:所有用户 VIP:VIP用户 NORMAL:普通用户
     */
    @TableField("target_user_type")
    private String targetUserType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话 ALL:全部
     */
    @TableField("biz_scene")
    private String bizScene;

    /**
     * 语言
     */
    @TableField("language")
    private String language;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 展示次数
     */
    @TableField("show_count")
    private Long showCount;

    /**
     * 点击次数
     */
    @TableField("click_count")
    private Long clickCount;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    private Integer sortWeight;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否在有效期内
     *
     * @return true:有效期内 false:已过期
     */
    public boolean isInValidPeriod() {
        LocalDateTime now = LocalDateTime.now();
        return (startTime == null || now.isAfter(startTime)) && 
               (endTime == null || now.isBefore(endTime));
    }

    /**
     * 检查是否可以展示
     *
     * @return true:可以展示 false:不可以展示
     */
    public boolean canShow() {
        return isEnabled() && isInValidPeriod();
    }

    /**
     * 增加展示次数
     */
    public void incrementShowCount() {
        this.showCount = (this.showCount == null ? 0 : this.showCount) + 1;
    }

    /**
     * 增加点击次数
     */
    public void incrementClickCount() {
        this.clickCount = (this.clickCount == null ? 0 : this.clickCount) + 1;
    }

    /**
     * 计算点击率
     *
     * @return 点击率(0-100)
     */
    public double getClickRate() {
        if (showCount == null || showCount == 0) {
            return 0.0;
        }
        long clicks = clickCount == null ? 0 : clickCount;
        return (double) clicks / showCount * 100;
    }

    /**
     * 检查是否为横幅广告
     *
     * @return true:横幅广告 false:其他类型
     */
    public boolean isBannerAd() {
        return "BANNER".equals(advType);
    }

    /**
     * 检查是否为弹窗广告
     *
     * @return true:弹窗广告 false:其他类型
     */
    public boolean isPopupAd() {
        return "POPUP".equals(advType);
    }

    /**
     * 检查是否针对所有用户
     *
     * @return true:所有用户 false:特定用户
     */
    public boolean isForAllUsers() {
        return "ALL".equals(targetUserType);
    }

    /**
     * 检查是否针对VIP用户
     *
     * @return true:VIP用户 false:其他用户
     */
    public boolean isForVipUsers() {
        return "VIP".equals(targetUserType);
    }
}
