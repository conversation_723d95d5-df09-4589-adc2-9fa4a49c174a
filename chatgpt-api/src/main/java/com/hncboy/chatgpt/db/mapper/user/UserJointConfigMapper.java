package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserJointConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 第三方登录配置Mapper接口
 * 
 * 重构说明:
 * 1. 继承MyBatis-Plus的BaseMapper，提供标准CRUD操作
 * 2. 添加第三方登录配置相关的自定义查询方法
 * 3. 支持多种平台类型查询：微信、Google、Facebook等
 * 4. 支持多环境配置查询，env_type字段
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Mapper
public interface UserJointConfigMapper extends BaseMapper<UserJointConfigDO> {

    /**
     * 根据平台类型查询第三方登录配置
     *
     * @param platformType 平台类型
     * @return 第三方登录配置
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = #{platformType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC LIMIT 1")
    UserJointConfigDO selectByPlatformType(@Param("platformType") String platformType);

    /**
     * 根据平台类型和环境类型查询第三方登录配置
     *
     * @param platformType 平台类型
     * @param envType 环境类型
     * @return 第三方登录配置
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = #{platformType} AND env_type = #{envType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC LIMIT 1")
    UserJointConfigDO selectByPlatformTypeAndEnvType(@Param("platformType") String platformType, @Param("envType") String envType);

    /**
     * 根据平台类型查询第三方登录配置列表
     *
     * @param platformType 平台类型
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = #{platformType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectListByPlatformType(@Param("platformType") String platformType);

    /**
     * 根据环境类型查询第三方登录配置列表
     *
     * @param envType 环境类型
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE env_type = #{envType} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectByEnvType(@Param("envType") String envType);

    /**
     * 根据状态查询第三方登录配置列表
     *
     * @param status 状态
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE status = #{status} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectByStatus(@Param("status") Integer status);

    /**
     * 查询启用状态的第三方登录配置列表
     *
     * @return 启用状态的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE status = 1 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectEnabledConfigs();

    /**
     * 查询禁用状态的第三方登录配置列表
     *
     * @return 禁用状态的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE status = 0 AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectDisabledConfigs();

    /**
     * 查询微信平台配置列表
     *
     * @return 微信平台配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = 'WECHAT' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectWechatConfigs();

    /**
     * 查询Google平台配置列表
     *
     * @return Google平台配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = 'GOOGLE' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectGoogleConfigs();

    /**
     * 查询Facebook平台配置列表
     *
     * @return Facebook平台配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = 'FACEBOOK' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectFacebookConfigs();

    /**
     * 查询GitHub平台配置列表
     *
     * @return GitHub平台配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = 'GITHUB' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectGithubConfigs();

    /**
     * 查询支付宝平台配置列表
     *
     * @return 支付宝平台配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = 'ALIPAY' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectAlipayConfigs();

    /**
     * 查询生产环境配置列表
     *
     * @return 生产环境配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE env_type = 'prod' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectProdConfigs();

    /**
     * 查询测试环境配置列表
     *
     * @return 测试环境配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE env_type = 'test' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectTestConfigs();

    /**
     * 查询开发环境配置列表
     *
     * @return 开发环境配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE env_type = 'dev' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectDevConfigs();

    /**
     * 根据配置版本查询第三方登录配置列表
     *
     * @param configVersion 配置版本
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE config_version = #{configVersion} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectByConfigVersion(@Param("configVersion") String configVersion);

    /**
     * 根据客户端ID查询第三方登录配置
     *
     * @param clientId 客户端ID
     * @return 第三方登录配置
     */
    @Select("SELECT * FROM user_joint_config WHERE client_id = #{clientId} AND deleted = 0")
    UserJointConfigDO selectByClientId(@Param("clientId") String clientId);

    /**
     * 查询配置完整的第三方登录配置列表
     *
     * @return 配置完整的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE client_id IS NOT NULL AND client_id != '' " +
            "AND client_secret IS NOT NULL AND client_secret != '' " +
            "AND redirect_uri IS NOT NULL AND redirect_uri != '' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectCompleteConfigs();

    /**
     * 查询有扩展配置的第三方登录配置列表
     *
     * @return 有扩展配置的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE extra_config IS NOT NULL AND extra_config != '' AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectConfigsWithExtra();

    /**
     * 根据平台类型和状态查询第三方登录配置列表
     *
     * @param platformType 平台类型
     * @param status 状态
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE platform_type = #{platformType} AND status = #{status} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectByPlatformTypeAndStatus(@Param("platformType") String platformType, @Param("status") Integer status);

    /**
     * 根据环境类型和状态查询第三方登录配置列表
     *
     * @param envType 环境类型
     * @param status 状态
     * @return 第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE env_type = #{envType} AND status = #{status} AND deleted = 0 ORDER BY sort_weight ASC, create_time DESC")
    List<UserJointConfigDO> selectByEnvTypeAndStatus(@Param("envType") String envType, @Param("status") Integer status);

    /**
     * 查询最近创建的第三方登录配置列表
     *
     * @param limit 限制数量
     * @return 最近创建的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<UserJointConfigDO> selectRecentConfigs(@Param("limit") Integer limit);

    /**
     * 查询最近更新的第三方登录配置列表
     *
     * @param limit 限制数量
     * @return 最近更新的第三方登录配置列表
     */
    @Select("SELECT * FROM user_joint_config WHERE deleted = 0 ORDER BY update_time DESC LIMIT #{limit}")
    List<UserJointConfigDO> selectRecentlyUpdatedConfigs(@Param("limit") Integer limit);

    /**
     * 统计第三方登录配置总数
     *
     * @return 第三方登录配置总数
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE deleted = 0")
    Long countTotalConfigs();

    /**
     * 根据平台类型统计第三方登录配置数量
     *
     * @param platformType 平台类型
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE platform_type = #{platformType} AND deleted = 0")
    Long countByPlatformType(@Param("platformType") String platformType);

    /**
     * 根据环境类型统计第三方登录配置数量
     *
     * @param envType 环境类型
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE env_type = #{envType} AND deleted = 0")
    Long countByEnvType(@Param("envType") String envType);

    /**
     * 根据状态统计第三方登录配置数量
     *
     * @param status 状态
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE status = #{status} AND deleted = 0")
    Long countByStatus(@Param("status") Integer status);

    /**
     * 统计启用状态的第三方登录配置数量
     *
     * @return 启用状态的第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE status = 1 AND deleted = 0")
    Long countEnabledConfigs();

    /**
     * 统计配置完整的第三方登录配置数量
     *
     * @return 配置完整的第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE client_id IS NOT NULL AND client_id != '' " +
            "AND client_secret IS NOT NULL AND client_secret != '' " +
            "AND redirect_uri IS NOT NULL AND redirect_uri != '' AND deleted = 0")
    Long countCompleteConfigs();

    /**
     * 统计有扩展配置的第三方登录配置数量
     *
     * @return 有扩展配置的第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE extra_config IS NOT NULL AND extra_config != '' AND deleted = 0")
    Long countConfigsWithExtra();

    /**
     * 根据平台类型和环境类型统计第三方登录配置数量
     *
     * @param platformType 平台类型
     * @param envType 环境类型
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE platform_type = #{platformType} AND env_type = #{envType} AND deleted = 0")
    Long countByPlatformTypeAndEnvType(@Param("platformType") String platformType, @Param("envType") String envType);

    /**
     * 根据平台类型和状态统计第三方登录配置数量
     *
     * @param platformType 平台类型
     * @param status 状态
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE platform_type = #{platformType} AND status = #{status} AND deleted = 0")
    Long countByPlatformTypeAndStatus(@Param("platformType") String platformType, @Param("status") Integer status);

    /**
     * 根据环境类型和状态统计第三方登录配置数量
     *
     * @param envType 环境类型
     * @param status 状态
     * @return 第三方登录配置数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_config WHERE env_type = #{envType} AND status = #{status} AND deleted = 0")
    Long countByEnvTypeAndStatus(@Param("envType") String envType, @Param("status") Integer status);
}
