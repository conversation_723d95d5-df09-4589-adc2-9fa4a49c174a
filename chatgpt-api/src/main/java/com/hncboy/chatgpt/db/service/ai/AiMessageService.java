package com.hncboy.chatgpt.db.service.ai;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.ai.AiMessageDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI消息服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:55
 */
public interface AiMessageService extends IService<AiMessageDO> {

    /**
     * 根据房间ID分页查询消息列表
     *
     * @param page   分页参数
     * @param roomId 房间ID
     * @return 消息列表
     */
    IPage<AiMessageDO> pageByRoomId(Page<AiMessageDO> page, Long roomId);

    /**
     * 根据房间ID和消息类型查询消息列表
     *
     * @param roomId      房间ID
     * @param messageType 消息类型
     * @param limit       限制数量
     * @return 消息列表
     */
    List<AiMessageDO> listByRoomIdAndType(Long roomId, Integer messageType, Integer limit);

    /**
     * 根据用户ID查询消息列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 消息列表
     */
    List<AiMessageDO> listByUserId(Integer userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据父消息ID查询子消息列表
     *
     * @param parentMsgId 父消息ID
     * @return 子消息列表
     */
    List<AiMessageDO> listByParentMsgId(Long parentMsgId);

    /**
     * 统计房间消息数量
     *
     * @param roomId 房间ID
     * @return 消息数量
     */
    Long countByRoomId(Long roomId);

    /**
     * 统计用户消息数量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 消息数量
     */
    Long countByUserIdAndAbilityType(Integer userId, String abilityType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计Token使用量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return Token使用量
     */
    Long sumTokensByUserIdAndAbilityType(Integer userId, String abilityType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除房间的所有消息
     *
     * @param roomId 房间ID
     * @return 是否成功
     */
    boolean deleteByRoomId(Long roomId);

    /**
     * 查询最近的消息列表(用于上下文)
     *
     * @param roomId 房间ID
     * @param limit  限制数量
     * @return 消息列表
     */
    List<AiMessageDO> listRecentMessages(Long roomId, Integer limit);

    /**
     * 根据状态查询消息列表
     *
     * @param status 状态
     * @param limit  限制数量
     * @return 消息列表
     */
    List<AiMessageDO> listByStatus(String status, Integer limit);

    /**
     * 更新消息状态
     *
     * @param id     消息ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long id, String status);

    /**
     * 批量更新消息状态
     *
     * @param ids    消息ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, String status);

    /**
     * 创建用户消息
     *
     * @param roomId  房间ID
     * @param userId  用户ID
     * @param content 消息内容
     * @return 消息对象
     */
    AiMessageDO createUserMessage(Long roomId, Integer userId, String content);

    /**
     * 创建AI回复消息
     *
     * @param roomId      房间ID
     * @param userId      用户ID
     * @param content     消息内容
     * @param parentMsgId 父消息ID
     * @return 消息对象
     */
    AiMessageDO createAiMessage(Long roomId, Integer userId, String content, Long parentMsgId);

    /**
     * 创建系统消息
     *
     * @param roomId  房间ID
     * @param userId  用户ID
     * @param content 消息内容
     * @return 消息对象
     */
    AiMessageDO createSystemMessage(Long roomId, Integer userId, String content);

    /**
     * 创建错误消息
     *
     * @param roomId       房间ID
     * @param userId       用户ID
     * @param errorMessage 错误信息
     * @param errorCode    错误代码
     * @return 消息对象
     */
    AiMessageDO createErrorMessage(Long roomId, Integer userId, String errorMessage, String errorCode);

    /**
     * 更新消息Token统计
     *
     * @param messageId    消息ID
     * @param inputTokens  输入Token数
     * @param outputTokens 输出Token数
     * @param responseTime 响应时间
     * @return 是否成功
     */
    boolean updateTokenStats(Long messageId, Integer inputTokens, Integer outputTokens, Long responseTime);
}
