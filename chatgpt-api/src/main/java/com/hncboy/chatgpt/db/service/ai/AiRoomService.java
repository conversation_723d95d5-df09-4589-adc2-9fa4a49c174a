package com.hncboy.chatgpt.db.service.ai;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.ai.AiRoomDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI房间服务接口
 *
 * <AUTHOR>
 * @date 2023/3/22 14:05
 */
public interface AiRoomService extends IService<AiRoomDO> {

    /**
     * 根据用户ID分页查询房间列表
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @return 房间列表
     */
    IPage<AiRoomDO> pageByUserId(Page<AiRoomDO> page, Integer userId);

    /**
     * 根据用户ID和能力类型查询房间列表
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @return 房间列表
     */
    List<AiRoomDO> listByUserIdAndAbilityType(Integer userId, String abilityType);

    /**
     * 根据用户ID和业务场景查询房间列表
     *
     * @param userId   用户ID
     * @param bizScene 业务场景
     * @return 房间列表
     */
    List<AiRoomDO> listByUserIdAndBizScene(Integer userId, String bizScene);

    /**
     * 根据智能体ID查询房间列表
     *
     * @param agentId 智能体ID
     * @return 房间列表
     */
    List<AiRoomDO> listByAgentId(Integer agentId);

    /**
     * 统计用户房间数量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @return 房间数量
     */
    Long countByUserIdAndAbilityType(Integer userId, String abilityType);

    /**
     * 查询用户置顶房间列表
     *
     * @param userId 用户ID
     * @return 置顶房间列表
     */
    List<AiRoomDO> listPinnedRoomsByUserId(Integer userId);

    /**
     * 查询用户收藏房间列表
     *
     * @param userId 用户ID
     * @return 收藏房间列表
     */
    List<AiRoomDO> listFavoriteRoomsByUserId(Integer userId);

    /**
     * 查询活跃房间列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param limit     限制数量
     * @return 活跃房间列表
     */
    List<AiRoomDO> listActiveRooms(Integer userId, LocalDateTime startTime, Integer limit);

    /**
     * 更新房间最后消息信息
     *
     * @param roomId             房间ID
     * @param lastMessageTime    最后消息时间
     * @param lastMessageContent 最后消息内容
     * @return 是否成功
     */
    boolean updateLastMessage(Long roomId, LocalDateTime lastMessageTime, String lastMessageContent);

    /**
     * 更新房间Token统计
     *
     * @param roomId            房间ID
     * @param totalInputTokens  总输入Token数
     * @param totalOutputTokens 总输出Token数
     * @param totalTokens       总Token数
     * @return 是否成功
     */
    boolean updateTokenStats(Long roomId, Long totalInputTokens, Long totalOutputTokens, Long totalTokens);

    /**
     * 增加房间消息数量
     *
     * @param roomId 房间ID
     * @return 是否成功
     */
    boolean incrementMessageCount(Long roomId);

    /**
     * 更新房间状态
     *
     * @param roomId 房间ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long roomId, String status);

    /**
     * 批量归档房间
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 是否成功
     */
    boolean batchArchiveRooms(Integer userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 创建对话房间
     *
     * @param userId      用户ID
     * @param title       房间标题
     * @param abilityType 能力类型
     * @param bizScene    业务场景
     * @param agentId     智能体ID
     * @return 房间对象
     */
    AiRoomDO createChatRoom(Integer userId, String title, String abilityType, String bizScene, Integer agentId);

    /**
     * 创建绘画房间
     *
     * @param userId   用户ID
     * @param title    房间标题
     * @param bizScene 业务场景
     * @return 房间对象
     */
    AiRoomDO createDrawRoom(Integer userId, String title, String bizScene);

    /**
     * 创建写作房间
     *
     * @param userId   用户ID
     * @param title    房间标题
     * @param bizScene 业务场景
     * @return 房间对象
     */
    AiRoomDO createWriteRoom(Integer userId, String title, String bizScene);

    /**
     * 创建音乐房间
     *
     * @param userId   用户ID
     * @param title    房间标题
     * @param bizScene 业务场景
     * @return 房间对象
     */
    AiRoomDO createMusicRoom(Integer userId, String title, String bizScene);

    /**
     * 置顶房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean pinRoom(Long roomId, Integer userId);

    /**
     * 取消置顶房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unpinRoom(Long roomId, Integer userId);

    /**
     * 收藏房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean favoriteRoom(Long roomId, Integer userId);

    /**
     * 取消收藏房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unfavoriteRoom(Long roomId, Integer userId);

    /**
     * 归档房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean archiveRoom(Long roomId, Integer userId);

    /**
     * 激活房间
     *
     * @param roomId 房间ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean activateRoom(Long roomId, Integer userId);
}
