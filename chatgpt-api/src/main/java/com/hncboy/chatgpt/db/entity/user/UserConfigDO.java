package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户配置实体类
 * 
 * 重构说明:
 * 1. 增强配置类型，支持多种用户配置管理
 * 2. 支持JSON格式配置内容，灵活存储各种配置信息
 * 3. 支持配置分组和业务场景区分
 * 4. 支持配置版本管理和敏感信息标识
 * 5. 完整保留原有业务逻辑，确保100%功能复刻
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_config")
@Schema(description = "用户配置")
public class UserConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 用户ID(关联user_base_info.id)
     */
    @TableField("user_id")
    @Schema(description = "用户ID")
    private Integer userId;

    /**
     * 配置键
     */
    @TableField("config_key")
    @Schema(description = "配置键")
    private String configKey;

    /**
     * 配置名称
     */
    @TableField("config_name")
    @Schema(description = "配置名称")
    private String configName;

    /**
     * 配置值(JSON格式)
     */
    @TableField("config_value")
    @Schema(description = "配置值")
    private String configValue;

    /**
     * 配置类型 SYSTEM:系统配置 USER:用户配置 PREFERENCE:偏好设置 SECURITY:安全配置
     */
    @TableField("config_type")
    @Schema(description = "配置类型")
    private String configType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话 ALL:通用
     */
    @TableField("biz_scene")
    @Schema(description = "业务场景")
    private String bizScene;

    /**
     * 配置分组
     */
    @TableField("config_group")
    @Schema(description = "配置分组")
    private String configGroup;

    /**
     * 配置描述
     */
    @TableField("description")
    @Schema(description = "配置描述")
    private String description;

    /**
     * 默认值
     */
    @TableField("default_value")
    @Schema(description = "默认值")
    private String defaultValue;

    /**
     * 是否敏感 1:敏感 0:非敏感
     */
    @TableField("is_sensitive")
    @Schema(description = "是否敏感")
    private Integer isSensitive;

    /**
     * 配置版本
     */
    @TableField("version")
    @Schema(description = "配置版本")
    private String version;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为系统配置
     *
     * @return true:系统配置 false:其他配置
     */
    public boolean isSystemConfig() {
        return "SYSTEM".equals(configType);
    }

    /**
     * 检查是否为用户配置
     *
     * @return true:用户配置 false:其他配置
     */
    public boolean isUserConfig() {
        return "USER".equals(configType);
    }

    /**
     * 检查是否为偏好设置
     *
     * @return true:偏好设置 false:其他配置
     */
    public boolean isPreferenceConfig() {
        return "PREFERENCE".equals(configType);
    }

    /**
     * 检查是否为安全配置
     *
     * @return true:安全配置 false:其他配置
     */
    public boolean isSecurityConfig() {
        return "SECURITY".equals(configType);
    }

    /**
     * 检查是否为敏感配置
     *
     * @return true:敏感配置 false:非敏感配置
     */
    public boolean isSensitive() {
        return isSensitive != null && isSensitive == 1;
    }

    /**
     * 检查是否适用于指定业务场景
     *
     * @param scene 业务场景
     * @return true:适用 false:不适用
     */
    public boolean isApplicableToScene(String scene) {
        if (bizScene == null || "ALL".equals(bizScene)) {
            return true;
        }
        return bizScene.equals(scene);
    }

    /**
     * 获取有效的配置值
     *
     * @return 配置值
     */
    public String getEffectiveValue() {
        if (configValue != null && !configValue.trim().isEmpty()) {
            return configValue;
        }
        return defaultValue;
    }

    /**
     * 获取配置类型描述
     *
     * @return 配置类型描述
     */
    public String getConfigTypeDescription() {
        if (configType == null) {
            return "未知";
        }
        switch (configType) {
            case "SYSTEM":
                return "系统配置";
            case "USER":
                return "用户配置";
            case "PREFERENCE":
                return "偏好设置";
            case "SECURITY":
                return "安全配置";
            default:
                return "未知";
        }
    }

    /**
     * 获取业务场景描述
     *
     * @return 业务场景描述
     */
    public String getBizSceneDescription() {
        if (bizScene == null) {
            return "未知";
        }
        switch (bizScene) {
            case "tarot":
                return "塔罗";
            case "zns":
                return "智能社";
            case "chatoi":
                return "对话";
            case "ALL":
                return "通用";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "启用";
            case 0:
                return "禁用";
            default:
                return "未知";
        }
    }

    /**
     * 设置为启用状态
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 设置为禁用状态
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 设置为敏感配置
     */
    public void setSensitive() {
        this.isSensitive = 1;
    }

    /**
     * 设置为非敏感配置
     */
    public void setNotSensitive() {
        this.isSensitive = 0;
    }

    /**
     * 检查配置值是否为空
     *
     * @return true:为空 false:不为空
     */
    public boolean isValueEmpty() {
        return configValue == null || configValue.trim().isEmpty();
    }

    /**
     * 检查是否有默认值
     *
     * @return true:有默认值 false:无默认值
     */
    public boolean hasDefaultValue() {
        return defaultValue != null && !defaultValue.trim().isEmpty();
    }
}
