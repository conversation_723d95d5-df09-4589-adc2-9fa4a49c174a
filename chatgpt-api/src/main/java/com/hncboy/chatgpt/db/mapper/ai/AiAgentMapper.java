package com.hncboy.chatgpt.db.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.ai.AiAgentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI智能体Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:10
 */
@Mapper
public interface AiAgentMapper extends BaseMapper<AiAgentDO> {

    /**
     * 根据能力类型查询智能体列表
     *
     * @param abilityType 能力类型
     * @param status      状态
     * @return 智能体列表
     */
    List<AiAgentDO> selectByAbilityTypeAndStatus(@Param("abilityType") String abilityType, @Param("status") Integer status);

    /**
     * 查询公开的智能体列表
     *
     * @param isPublic 是否公开
     * @param status   状态
     * @return 智能体列表
     */
    List<AiAgentDO> selectPublicAgents(@Param("isPublic") Integer isPublic, @Param("status") Integer status);

    /**
     * 根据创建者查询智能体列表
     *
     * @param creatorId 创建者ID
     * @return 智能体列表
     */
    List<AiAgentDO> selectByCreatorId(@Param("creatorId") Integer creatorId);

    /**
     * 更新使用次数
     *
     * @param id 智能体ID
     * @return 更新行数
     */
    int incrementUseCount(@Param("id") Integer id);

    /**
     * 更新点赞数
     *
     * @param id        智能体ID
     * @param increment 增量(正数增加，负数减少)
     * @return 更新行数
     */
    int updateLikeCount(@Param("id") Integer id, @Param("increment") Integer increment);

    /**
     * 更新收藏数
     *
     * @param id        智能体ID
     * @param increment 增量(正数增加，负数减少)
     * @return 更新行数
     */
    int updateFavoriteCount(@Param("id") Integer id, @Param("increment") Integer increment);
}
