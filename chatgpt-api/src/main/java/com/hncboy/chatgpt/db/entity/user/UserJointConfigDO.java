package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 第三方登录配置实体类
 * 
 * 重构说明:
 * 1. 新增UserJointConfig实体，用于JustAuth第三方登录配置管理
 * 2. 支持多种第三方登录平台：微信、Google、Facebook、GitHub等
 * 3. 支持OAuth2.0认证流程配置，包括client_id、client_secret等
 * 4. 支持配置启用/禁用状态，灵活控制第三方登录开关
 * 5. 支持多环境配置，dev、test、prod环境隔离
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_joint_config")
@Schema(description = "第三方登录配置")
public class UserJointConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 平台类型 WECHAT:微信 GOOGLE:谷歌 FACEBOOK:脸书 GITHUB:GitHub ALIPAY:支付宝
     */
    @TableField("platform_type")
    @Schema(description = "平台类型")
    private String platformType;

    /**
     * 平台名称
     */
    @TableField("platform_name")
    @Schema(description = "平台名称")
    private String platformName;

    /**
     * 客户端ID
     */
    @TableField("client_id")
    @Schema(description = "客户端ID")
    private String clientId;

    /**
     * 客户端密钥
     */
    @TableField("client_secret")
    @Schema(description = "客户端密钥")
    private String clientSecret;

    /**
     * 重定向URI
     */
    @TableField("redirect_uri")
    @Schema(description = "重定向URI")
    private String redirectUri;

    /**
     * 授权范围
     */
    @TableField("scope")
    @Schema(description = "授权范围")
    private String scope;

    /**
     * 授权地址
     */
    @TableField("auth_url")
    @Schema(description = "授权地址")
    private String authUrl;

    /**
     * 令牌地址
     */
    @TableField("token_url")
    @Schema(description = "令牌地址")
    private String tokenUrl;

    /**
     * 用户信息地址
     */
    @TableField("user_info_url")
    @Schema(description = "用户信息地址")
    private String userInfoUrl;

    /**
     * 环境类型 dev:开发 test:测试 prod:生产
     */
    @TableField("env_type")
    @Schema(description = "环境类型")
    private String envType;

    /**
     * 配置版本
     */
    @TableField("config_version")
    @Schema(description = "配置版本")
    private String configVersion;

    /**
     * 扩展配置(JSON格式)
     */
    @TableField("extra_config")
    @Schema(description = "扩展配置")
    private String extraConfig;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    @Schema(description = "状态")
    private Integer status;

    /**
     * 排序权重
     */
    @TableField("sort_weight")
    @Schema(description = "排序权重")
    private Integer sortWeight;

    /**
     * 备注
     */
    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @Schema(description = "创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为微信平台
     *
     * @return true:微信平台 false:其他平台
     */
    public boolean isWechatPlatform() {
        return "WECHAT".equals(platformType);
    }

    /**
     * 检查是否为Google平台
     *
     * @return true:Google平台 false:其他平台
     */
    public boolean isGooglePlatform() {
        return "GOOGLE".equals(platformType);
    }

    /**
     * 检查是否为Facebook平台
     *
     * @return true:Facebook平台 false:其他平台
     */
    public boolean isFacebookPlatform() {
        return "FACEBOOK".equals(platformType);
    }

    /**
     * 检查是否为GitHub平台
     *
     * @return true:GitHub平台 false:其他平台
     */
    public boolean isGithubPlatform() {
        return "GITHUB".equals(platformType);
    }

    /**
     * 检查是否为支付宝平台
     *
     * @return true:支付宝平台 false:其他平台
     */
    public boolean isAlipayPlatform() {
        return "ALIPAY".equals(platformType);
    }

    /**
     * 检查是否为生产环境
     *
     * @return true:生产环境 false:其他环境
     */
    public boolean isProdEnv() {
        return "prod".equals(envType);
    }

    /**
     * 检查是否为测试环境
     *
     * @return true:测试环境 false:其他环境
     */
    public boolean isTestEnv() {
        return "test".equals(envType);
    }

    /**
     * 检查是否为开发环境
     *
     * @return true:开发环境 false:其他环境
     */
    public boolean isDevEnv() {
        return "dev".equals(envType);
    }

    /**
     * 获取平台类型描述
     *
     * @return 平台类型描述
     */
    public String getPlatformTypeDescription() {
        if (platformType == null) {
            return "未知";
        }
        switch (platformType) {
            case "WECHAT":
                return "微信";
            case "GOOGLE":
                return "谷歌";
            case "FACEBOOK":
                return "脸书";
            case "GITHUB":
                return "GitHub";
            case "ALIPAY":
                return "支付宝";
            default:
                return "未知";
        }
    }

    /**
     * 获取环境类型描述
     *
     * @return 环境类型描述
     */
    public String getEnvTypeDescription() {
        if (envType == null) {
            return "未知";
        }
        switch (envType) {
            case "dev":
                return "开发环境";
            case "test":
                return "测试环境";
            case "prod":
                return "生产环境";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "启用";
            case 0:
                return "禁用";
            default:
                return "未知";
        }
    }

    /**
     * 设置为启用状态
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 设置为禁用状态
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 检查配置是否完整
     *
     * @return true:配置完整 false:配置不完整
     */
    public boolean isConfigComplete() {
        return clientId != null && !clientId.trim().isEmpty()
               && clientSecret != null && !clientSecret.trim().isEmpty()
               && redirectUri != null && !redirectUri.trim().isEmpty();
    }

    /**
     * 检查是否有扩展配置
     *
     * @return true:有扩展配置 false:无扩展配置
     */
    public boolean hasExtraConfig() {
        return extraConfig != null && !extraConfig.trim().isEmpty();
    }

    /**
     * 获取完整描述
     *
     * @return 完整描述
     */
    public String getFullDescription() {
        StringBuilder sb = new StringBuilder();
        sb.append(getPlatformTypeDescription());
        sb.append("(").append(getEnvTypeDescription()).append(")");
        sb.append(" - ").append(getStatusDescription());
        if (configVersion != null) {
            sb.append(" v").append(configVersion);
        }
        return sb.toString();
    }
}
