package com.hncboy.chatgpt.db.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI路由配置实体类
 *
 * <AUTHOR>
 * @date 2023/3/22 12:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_router_config")
public class AiRouterConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置名称
     */
    @TableField("name")
    private String name;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    private String abilityType;

    /**
     * 路由策略 ROUND_ROBIN:轮询 WEIGHTED:权重 RANDOM:随机 LEAST_CONNECTIONS:最少连接
     */
    @TableField("strategy")
    private String strategy;

    /**
     * 路由规则(JSON)
     */
    @TableField("rules")
    private String rules;

    /**
     * 负载均衡配置(JSON)
     */
    @TableField("load_balance_config")
    private String loadBalanceConfig;

    /**
     * 故障转移配置(JSON)
     */
    @TableField("failover_config")
    private String failoverConfig;

    /**
     * 限流配置(JSON)
     */
    @TableField("rate_limit_config")
    private String rateLimitConfig;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 状态 1:启用 0:禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否启用
     *
     * @return true:启用 false:禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为轮询策略
     *
     * @return true:轮询策略 false:其他策略
     */
    public boolean isRoundRobinStrategy() {
        return "ROUND_ROBIN".equals(strategy);
    }

    /**
     * 检查是否为权重策略
     *
     * @return true:权重策略 false:其他策略
     */
    public boolean isWeightedStrategy() {
        return "WEIGHTED".equals(strategy);
    }

    /**
     * 检查是否为随机策略
     *
     * @return true:随机策略 false:其他策略
     */
    public boolean isRandomStrategy() {
        return "RANDOM".equals(strategy);
    }

    /**
     * 检查是否为最少连接策略
     *
     * @return true:最少连接策略 false:其他策略
     */
    public boolean isLeastConnectionsStrategy() {
        return "LEAST_CONNECTIONS".equals(strategy);
    }
}
