package com.hncboy.chatgpt.db.mapper.pay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.pay.PayChannelConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付渠道配置Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:40
 */
@Mapper
public interface PayChannelConfigMapper extends BaseMapper<PayChannelConfigDO> {

    /**
     * 根据渠道类型查询配置列表
     *
     * @param channelType 渠道类型
     * @param status      状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByChannelTypeAndStatus(@Param("channelType") String channelType, @Param("status") Integer status);

    /**
     * 根据支付方式查询配置列表
     *
     * @param payMethod 支付方式
     * @param status    状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByPayMethodAndStatus(@Param("payMethod") String payMethod, @Param("status") Integer status);

    /**
     * 根据币种查询配置列表
     *
     * @param currency 币种
     * @param status   状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByCurrencyAndStatus(@Param("currency") String currency, @Param("status") Integer status);

    /**
     * 根据渠道编码查询配置
     *
     * @param channelCode 渠道编码
     * @return 配置对象
     */
    PayChannelConfigDO selectByChannelCode(@Param("channelCode") String channelCode);

    /**
     * 查询支持指定金额的渠道配置
     *
     * @param amount      金额
     * @param channelType 渠道类型
     * @param currency    币种
     * @param status      状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByAmountRange(@Param("amount") BigDecimal amount, 
                                                 @Param("channelType") String channelType, 
                                                 @Param("currency") String currency, 
                                                 @Param("status") Integer status);

    /**
     * 查询所有启用的配置
     *
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectAllEnabled();

    /**
     * 根据权重排序查询配置
     *
     * @param channelType 渠道类型
     * @param currency    币种
     * @param status      状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByWeightOrder(@Param("channelType") String channelType, 
                                                 @Param("currency") String currency, 
                                                 @Param("status") Integer status);

    /**
     * 根据优先级排序查询配置
     *
     * @param channelType 渠道类型
     * @param currency    币种
     * @param status      状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectByPriorityOrder(@Param("channelType") String channelType, 
                                                   @Param("currency") String currency, 
                                                   @Param("status") Integer status);

    /**
     * 查询沙箱环境配置
     *
     * @param isSandbox 是否沙箱环境
     * @param status    状态
     * @return 配置列表
     */
    List<PayChannelConfigDO> selectBySandboxAndStatus(@Param("isSandbox") Integer isSandbox, @Param("status") Integer status);

    /**
     * 统计渠道配置数量
     *
     * @param channelType 渠道类型
     * @param status      状态
     * @return 配置数量
     */
    Long countByChannelTypeAndStatus(@Param("channelType") String channelType, @Param("status") Integer status);

    /**
     * 批量更新状态
     *
     * @param ids    配置ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Integer> ids, @Param("status") Integer status);
}
