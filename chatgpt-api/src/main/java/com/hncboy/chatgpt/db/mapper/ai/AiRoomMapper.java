package com.hncboy.chatgpt.db.mapper.ai;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.db.entity.ai.AiRoomDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI房间Mapper接口
 *
 * <AUTHOR>
 * @date 2023/3/22 13:15
 */
@Mapper
public interface AiRoomMapper extends BaseMapper<AiRoomDO> {

    /**
     * 根据用户ID分页查询房间列表
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @return 房间列表
     */
    IPage<AiRoomDO> selectByUserId(Page<AiRoomDO> page, @Param("userId") Integer userId);

    /**
     * 根据用户ID和能力类型查询房间列表
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @return 房间列表
     */
    List<AiRoomDO> selectByUserIdAndAbilityType(@Param("userId") Integer userId, @Param("abilityType") String abilityType);

    /**
     * 根据用户ID和业务场景查询房间列表
     *
     * @param userId   用户ID
     * @param bizScene 业务场景
     * @return 房间列表
     */
    List<AiRoomDO> selectByUserIdAndBizScene(@Param("userId") Integer userId, @Param("bizScene") String bizScene);

    /**
     * 根据智能体ID查询房间列表
     *
     * @param agentId 智能体ID
     * @return 房间列表
     */
    List<AiRoomDO> selectByAgentId(@Param("agentId") Integer agentId);

    /**
     * 统计用户房间数量
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @return 房间数量
     */
    Long countByUserIdAndAbilityType(@Param("userId") Integer userId, @Param("abilityType") String abilityType);

    /**
     * 查询用户置顶房间列表
     *
     * @param userId 用户ID
     * @return 置顶房间列表
     */
    List<AiRoomDO> selectPinnedRoomsByUserId(@Param("userId") Integer userId);

    /**
     * 查询用户收藏房间列表
     *
     * @param userId 用户ID
     * @return 收藏房间列表
     */
    List<AiRoomDO> selectFavoriteRoomsByUserId(@Param("userId") Integer userId);

    /**
     * 查询活跃房间列表
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param limit     限制数量
     * @return 活跃房间列表
     */
    List<AiRoomDO> selectActiveRooms(@Param("userId") Integer userId,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("limit") Integer limit);

    /**
     * 更新房间最后消息信息
     *
     * @param roomId             房间ID
     * @param lastMessageTime    最后消息时间
     * @param lastMessageContent 最后消息内容
     * @return 更新数量
     */
    int updateLastMessage(@Param("roomId") Long roomId,
                          @Param("lastMessageTime") LocalDateTime lastMessageTime,
                          @Param("lastMessageContent") String lastMessageContent);

    /**
     * 更新房间Token统计
     *
     * @param roomId            房间ID
     * @param totalInputTokens  总输入Token数
     * @param totalOutputTokens 总输出Token数
     * @param totalTokens       总Token数
     * @return 更新数量
     */
    int updateTokenStats(@Param("roomId") Long roomId,
                         @Param("totalInputTokens") Long totalInputTokens,
                         @Param("totalOutputTokens") Long totalOutputTokens,
                         @Param("totalTokens") Long totalTokens);

    /**
     * 增加房间消息数量
     *
     * @param roomId 房间ID
     * @return 更新数量
     */
    int incrementMessageCount(@Param("roomId") Long roomId);

    /**
     * 更新房间状态
     *
     * @param roomId 房间ID
     * @param status 状态
     * @return 更新数量
     */
    int updateStatus(@Param("roomId") Long roomId, @Param("status") String status);

    /**
     * 批量归档房间
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 更新数量
     */
    int batchArchiveRooms(@Param("userId") Integer userId,
                          @Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);
}
