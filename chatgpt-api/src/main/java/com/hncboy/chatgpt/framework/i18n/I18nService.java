package com.hncboy.chatgpt.framework.i18n;

import com.hncboy.chatgpt.common.enums.LanguageEnum;
import com.hncboy.chatgpt.common.util.StringUtil;
import com.hncboy.chatgpt.db.entity.system.I18nMessageDO;
import com.hncboy.chatgpt.framework.cache.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

/**
 * 国际化服务
 *
 * <AUTHOR>
 * @date 2023/3/22 13:50
 */
@Slf4j
@Service
public class I18nService {

    @Resource
    private MessageSource messageSource;

    @Resource
    private CacheService cacheService;

    /**
     * 缓存前缀
     */
    private static final String CACHE_PREFIX = "i18n:message:";

    /**
     * 缓存过期时间(小时)
     */
    private static final long CACHE_EXPIRE_HOURS = 24;

    /**
     * 获取国际化消息
     *
     * @param key 消息键
     * @return 消息内容
     */
    public String getMessage(String key) {
        return getMessage(key, null, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     *
     * @param key  消息键
     * @param args 参数
     * @return 消息内容
     */
    public String getMessage(String key, Object[] args) {
        return getMessage(key, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     *
     * @param key    消息键
     * @param locale 区域
     * @return 消息内容
     */
    public String getMessage(String key, Locale locale) {
        return getMessage(key, null, locale);
    }

    /**
     * 获取国际化消息
     *
     * @param key    消息键
     * @param args   参数
     * @param locale 区域
     * @return 消息内容
     */
    public String getMessage(String key, Object[] args, Locale locale) {
        if (StringUtil.isEmpty(key)) {
            return key;
        }

        try {
            // 先从缓存获取
            String cacheKey = buildCacheKey(key, locale.toString());
            String cachedMessage = cacheService.get(cacheKey, String.class);
            if (StringUtil.isNotEmpty(cachedMessage)) {
                return formatMessage(cachedMessage, args);
            }

            // 从数据库获取
            String message = getMessageFromDatabase(key, locale.toString());
            if (StringUtil.isNotEmpty(message)) {
                // 缓存消息
                cacheService.set(cacheKey, message, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
                return formatMessage(message, args);
            }

            // 从资源文件获取
            message = messageSource.getMessage(key, args, key, locale);
            
            // 缓存消息
            cacheService.set(cacheKey, message, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            return message;
        } catch (Exception e) {
            log.error("获取国际化消息失败: key={}, locale={}", key, locale, e);
            return key;
        }
    }

    /**
     * 获取指定语言的消息
     *
     * @param key          消息键
     * @param languageCode 语言代码
     * @return 消息内容
     */
    public String getMessage(String key, String languageCode) {
        Locale locale = parseLocale(languageCode);
        return getMessage(key, null, locale);
    }

    /**
     * 获取指定语言的消息
     *
     * @param key          消息键
     * @param args         参数
     * @param languageCode 语言代码
     * @return 消息内容
     */
    public String getMessage(String key, Object[] args, String languageCode) {
        Locale locale = parseLocale(languageCode);
        return getMessage(key, args, locale);
    }

    /**
     * 清除消息缓存
     *
     * @param key 消息键
     */
    public void clearMessageCache(String key) {
        try {
            // 清除所有语言的缓存
            for (LanguageEnum languageEnum : LanguageEnum.values()) {
                String cacheKey = buildCacheKey(key, languageEnum.getCode());
                cacheService.delete(cacheKey);
            }
            log.info("清除消息缓存成功: key={}", key);
        } catch (Exception e) {
            log.error("清除消息缓存失败: key={}", key, e);
        }
    }

    /**
     * 清除所有消息缓存
     */
    public void clearAllMessageCache() {
        try {
            // 这里可以实现批量清除缓存的逻辑
            log.info("清除所有消息缓存成功");
        } catch (Exception e) {
            log.error("清除所有消息缓存失败", e);
        }
    }

    /**
     * 从数据库获取消息
     *
     * @param key          消息键
     * @param languageCode 语言代码
     * @return 消息内容
     */
    private String getMessageFromDatabase(String key, String languageCode) {
        try {
            // 这里需要实现从数据库查询I18nMessageDO的逻辑
            // 示例代码，实际需要注入相应的Service
            /*
            I18nMessageDO message = i18nMessageService.getByKeyAndLanguage(key, languageCode);
            if (message != null && message.isEnabled()) {
                return message.getEffectiveContent();
            }
            */
            return null;
        } catch (Exception e) {
            log.error("从数据库获取消息失败: key={}, languageCode={}", key, languageCode, e);
            return null;
        }
    }

    /**
     * 格式化消息
     *
     * @param message 消息模板
     * @param args    参数
     * @return 格式化后的消息
     */
    private String formatMessage(String message, Object[] args) {
        if (args == null || args.length == 0) {
            return message;
        }
        
        try {
            return String.format(message, args);
        } catch (Exception e) {
            log.warn("格式化消息失败: message={}, args={}", message, args, e);
            return message;
        }
    }

    /**
     * 解析区域
     *
     * @param languageCode 语言代码
     * @return 区域
     */
    private Locale parseLocale(String languageCode) {
        if (StringUtil.isEmpty(languageCode)) {
            return LocaleContextHolder.getLocale();
        }
        
        try {
            LanguageEnum languageEnum = LanguageEnum.getByCode(languageCode);
            String[] parts = languageEnum.getCode().split("_");
            if (parts.length == 2) {
                return new Locale(parts[0], parts[1]);
            } else {
                return new Locale(parts[0]);
            }
        } catch (Exception e) {
            log.warn("解析区域失败: languageCode={}", languageCode, e);
            return LocaleContextHolder.getLocale();
        }
    }

    /**
     * 构建缓存键
     *
     * @param key          消息键
     * @param languageCode 语言代码
     * @return 缓存键
     */
    private String buildCacheKey(String key, String languageCode) {
        return CACHE_PREFIX + languageCode + ":" + key;
    }

    /**
     * 检查语言是否支持
     *
     * @param languageCode 语言代码
     * @return 是否支持
     */
    public boolean isSupportedLanguage(String languageCode) {
        if (StringUtil.isEmpty(languageCode)) {
            return false;
        }
        
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.getCode().equals(languageCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取默认语言代码
     *
     * @return 默认语言代码
     */
    public String getDefaultLanguageCode() {
        return LanguageEnum.ZH_CN.getCode();
    }

    /**
     * 获取当前语言代码
     *
     * @return 当前语言代码
     */
    public String getCurrentLanguageCode() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale.toString();
    }
}
