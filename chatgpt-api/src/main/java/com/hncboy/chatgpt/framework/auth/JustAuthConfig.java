package com.hncboy.chatgpt.framework.auth;

import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JustAuth第三方登录配置
 *
 * <AUTHOR>
 * @date 2023/3/22 13:30
 */
@Configuration
public class JustAuthConfig {

    @Value("${justauth.wechat.client-id:}")
    private String wechatClientId;

    @Value("${justauth.wechat.client-secret:}")
    private String wechatClientSecret;

    @Value("${justauth.wechat.redirect-uri:}")
    private String wechatRedirectUri;

    @Value("${justauth.google.client-id:}")
    private String googleClientId;

    @Value("${justauth.google.client-secret:}")
    private String googleClientSecret;

    @Value("${justauth.google.redirect-uri:}")
    private String googleRedirectUri;

    @Value("${justauth.facebook.client-id:}")
    private String facebookClientId;

    @Value("${justauth.facebook.client-secret:}")
    private String facebookClientSecret;

    @Value("${justauth.facebook.redirect-uri:}")
    private String facebookRedirectUri;

    /**
     * 微信登录请求
     *
     * @return AuthWeChatMpRequest
     */
    @Bean
    public AuthWeChatMpRequest authWeChatMpRequest() {
        return new AuthWeChatMpRequest(AuthConfig.builder()
                .clientId(wechatClientId)
                .clientSecret(wechatClientSecret)
                .redirectUri(wechatRedirectUri)
                .build());
    }

    /**
     * Google登录请求
     *
     * @return AuthGoogleRequest
     */
    @Bean
    public AuthGoogleRequest authGoogleRequest() {
        return new AuthGoogleRequest(AuthConfig.builder()
                .clientId(googleClientId)
                .clientSecret(googleClientSecret)
                .redirectUri(googleRedirectUri)
                .build());
    }

    /**
     * Facebook登录请求
     *
     * @return AuthFacebookRequest
     */
    @Bean
    public AuthFacebookRequest authFacebookRequest() {
        return new AuthFacebookRequest(AuthConfig.builder()
                .clientId(facebookClientId)
                .clientSecret(facebookClientSecret)
                .redirectUri(facebookRedirectUri)
                .build());
    }
}
