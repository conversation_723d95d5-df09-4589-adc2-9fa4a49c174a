package com.hncboy.chatgpt.framework.i18n;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Locale;

/**
 * 国际化配置
 *
 * <AUTHOR>
 * @date 2023/3/22 13:45
 */
@Configuration
public class I18nConfig implements WebMvcConfigurer {

    /**
     * 国际化资源配置
     *
     * @return MessageSource
     */
    @Bean
    public ResourceBundleMessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        // 设置国际化资源文件路径
        messageSource.setBasenames("i18n/messages");
        // 设置默认编码
        messageSource.setDefaultEncoding("UTF-8");
        // 设置缓存时间(秒)，-1表示永久缓存
        messageSource.setCacheSeconds(3600);
        // 设置当找不到资源时是否使用默认消息
        messageSource.setUseCodeAsDefaultMessage(true);
        return messageSource;
    }

    /**
     * 区域解析器
     *
     * @return LocaleResolver
     */
    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        // 设置默认区域为中文简体
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return localeResolver;
    }

    /**
     * 区域变更拦截器
     *
     * @return LocaleChangeInterceptor
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        // 设置请求参数名，默认为locale
        interceptor.setParamName("lang");
        return interceptor;
    }

    /**
     * 添加拦截器
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
    }
}
