package com.hncboy.chatgpt.framework.auth;

import com.hncboy.chatgpt.common.enums.LoginTypeEnum;
import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import com.hncboy.chatgpt.db.entity.user.UserJointLoginDO;
import com.hncboy.chatgpt.db.service.user.UserBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthFacebookRequest;
import me.zhyd.oauth.request.AuthGoogleRequest;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.request.AuthWeChatMpRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证服务
 *
 * <AUTHOR>
 * @date 2023/3/22 13:35
 */
@Slf4j
@Service
public class AuthService {

    @Resource
    private AuthWeChatMpRequest authWeChatMpRequest;

    @Resource
    private AuthGoogleRequest authGoogleRequest;

    @Resource
    private AuthFacebookRequest authFacebookRequest;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    private final Map<String, AuthRequest> authRequestMap = new HashMap<>();

    /**
     * 初始化认证请求映射
     */
    public void initAuthRequestMap() {
        authRequestMap.put(LoginTypeEnum.WECHAT.getCode(), authWeChatMpRequest);
        authRequestMap.put(LoginTypeEnum.GOOGLE.getCode(), authGoogleRequest);
        authRequestMap.put(LoginTypeEnum.FACEBOOK.getCode(), authFacebookRequest);
    }

    /**
     * 获取授权URL
     *
     * @param loginType 登录类型
     * @param state     状态参数
     * @return 授权URL
     */
    public Result<String> getAuthUrl(String loginType, String state) {
        log.info("获取授权URL: loginType={}, state={}", loginType, state);
        
        try {
            AuthRequest authRequest = getAuthRequest(loginType);
            if (authRequest == null) {
                return Result.error("不支持的登录类型: " + loginType);
            }
            
            String authUrl = authRequest.authorize(state);
            log.info("生成授权URL成功: loginType={}, authUrl={}", loginType, authUrl);
            
            return Result.success(authUrl);
        } catch (Exception e) {
            log.error("获取授权URL失败: loginType={}", loginType, e);
            return Result.error("获取授权URL失败: " + e.getMessage());
        }
    }

    /**
     * 处理授权回调
     *
     * @param loginType 登录类型
     * @param callback  回调参数
     * @return 用户信息
     */
    public Result<UserBaseInfoDO> handleAuthCallback(String loginType, AuthCallback callback) {
        log.info("处理授权回调: loginType={}, callback={}", loginType, callback);
        
        try {
            AuthRequest authRequest = getAuthRequest(loginType);
            if (authRequest == null) {
                return Result.error("不支持的登录类型: " + loginType);
            }
            
            AuthResponse<AuthUser> response = authRequest.login(callback);
            if (!response.ok()) {
                log.error("第三方登录失败: loginType={}, response={}", loginType, response);
                return Result.error("第三方登录失败: " + response.getMsg());
            }
            
            AuthUser authUser = response.getData();
            log.info("第三方登录成功: loginType={}, authUser={}", loginType, authUser);
            
            // 查找或创建用户
            UserBaseInfoDO user = findOrCreateUser(loginType, authUser);
            
            return Result.success(user);
        } catch (Exception e) {
            log.error("处理授权回调失败: loginType={}", loginType, e);
            return Result.error("处理授权回调失败: " + e.getMessage());
        }
    }

    /**
     * 获取认证请求对象
     *
     * @param loginType 登录类型
     * @return 认证请求对象
     */
    private AuthRequest getAuthRequest(String loginType) {
        if (authRequestMap.isEmpty()) {
            initAuthRequestMap();
        }
        return authRequestMap.get(loginType);
    }

    /**
     * 查找或创建用户
     *
     * @param loginType 登录类型
     * @param authUser  第三方用户信息
     * @return 用户信息
     */
    private UserBaseInfoDO findOrCreateUser(String loginType, AuthUser authUser) {
        // 根据第三方用户ID查找现有用户
        // 这里需要实现具体的用户查找和创建逻辑
        
        // 示例实现
        UserBaseInfoDO user = new UserBaseInfoDO();
        user.setAccount(authUser.getUsername());
        user.setNickName(authUser.getNickname());
        user.setAvatarUrl(authUser.getAvatar());
        user.setEmail(authUser.getEmail());
        user.setStatus(0); // 正常状态
        user.setFirstStatus("1"); // 首次登录
        user.setLoginTime(LocalDateTime.now());
        
        // 保存用户信息
        userBaseInfoService.save(user);
        
        // 保存第三方登录信息
        UserJointLoginDO jointLogin = new UserJointLoginDO();
        jointLogin.setUserId(user.getId());
        jointLogin.setLoginType(loginType);
        jointLogin.setThirdPartyId(authUser.getUuid());
        jointLogin.setThirdPartyUsername(authUser.getUsername());
        jointLogin.setThirdPartyEmail(authUser.getEmail());
        jointLogin.setThirdPartyAvatar(authUser.getAvatar());
        jointLogin.setAccessToken(authUser.getToken().getAccessToken());
        jointLogin.setRefreshToken(authUser.getToken().getRefreshToken());
        jointLogin.setExpiresTime(LocalDateTime.now().plusSeconds(authUser.getToken().getExpireIn()));
        jointLogin.setUnionId(authUser.getRawUserInfo().toString());
        jointLogin.setStatus(1); // 启用状态
        jointLogin.updateLoginInfo();
        
        // 这里需要保存jointLogin到数据库
        
        log.info("创建新用户成功: userId={}, loginType={}", user.getId(), loginType);
        
        return user;
    }

    /**
     * 撤销授权
     *
     * @param loginType 登录类型
     * @param token     访问令牌
     * @return 撤销结果
     */
    public Result<Void> revokeAuth(String loginType, String token) {
        log.info("撤销授权: loginType={}, token={}", loginType, token);
        
        try {
            AuthRequest authRequest = getAuthRequest(loginType);
            if (authRequest == null) {
                return Result.error("不支持的登录类型: " + loginType);
            }
            
            AuthResponse<String> response = authRequest.revoke(AuthUser.builder().token(
                me.zhyd.oauth.model.AuthToken.builder().accessToken(token).build()
            ).build().getToken());
            
            if (response.ok()) {
                log.info("撤销授权成功: loginType={}", loginType);
                return Result.success();
            } else {
                log.error("撤销授权失败: loginType={}, response={}", loginType, response);
                return Result.error("撤销授权失败: " + response.getMsg());
            }
        } catch (Exception e) {
            log.error("撤销授权异常: loginType={}", loginType, e);
            return Result.error("撤销授权异常: " + e.getMessage());
        }
    }
}
