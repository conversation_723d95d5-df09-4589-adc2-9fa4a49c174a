package com.hncboy.chatgpt.framework.pay;

import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.common.bean.PayOrder;
import com.egzosn.pay.common.bean.PayOutMessage;
import com.egzosn.pay.common.bean.TransactionType;
import com.hncboy.chatgpt.common.enums.PayChannelEnum;
import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.common.util.JsonUtil;
import com.hncboy.chatgpt.common.util.StringUtil;
import com.hncboy.chatgpt.db.entity.pay.PayOrderDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一支付服务
 *
 * <AUTHOR>
 * @date 2023/3/22 14:00
 */
@Slf4j
@Service
public class UnifiedPayService {

    @Resource
    private Map<String, PayService> payServiceMap;

    /**
     * 创建支付订单
     *
     * @param payOrderDO 支付订单
     * @return 支付结果
     */
    public Result<String> createPayOrder(PayOrderDO payOrderDO) {
        log.info("创建支付订单: orderNo={}, channel={}, amount={}", 
                payOrderDO.getOrderNo(), payOrderDO.getPayChannel(), payOrderDO.getPayAmount());
        
        try {
            // 获取支付服务
            PayService payService = getPayService(payOrderDO.getPayChannel());
            if (payService == null) {
                return Result.error("不支持的支付渠道: " + payOrderDO.getPayChannel());
            }
            
            // 构建支付订单
            PayOrder payOrder = buildPayOrder(payOrderDO);
            
            // 创建支付订单
            Map<String, Object> orderInfo = payService.orderInfo(payOrder);
            
            // 根据支付方式返回不同的结果
            String result = handlePayResult(payOrderDO.getPayMethod(), orderInfo);
            
            log.info("创建支付订单成功: orderNo={}, result={}", payOrderDO.getOrderNo(), result);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建支付订单失败: orderNo={}", payOrderDO.getOrderNo(), e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     *
     * @param orderNo    订单号
     * @param payChannel 支付渠道
     * @return 查询结果
     */
    public Result<Map<String, Object>> queryPayOrder(String orderNo, String payChannel) {
        log.info("查询支付订单状态: orderNo={}, channel={}", orderNo, payChannel);
        
        try {
            PayService payService = getPayService(payChannel);
            if (payService == null) {
                return Result.error("不支持的支付渠道: " + payChannel);
            }
            
            Map<String, Object> result = payService.query(orderNo, null);
            
            log.info("查询支付订单状态成功: orderNo={}, result={}", orderNo, result);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询支付订单状态失败: orderNo={}", orderNo, e);
            return Result.error("查询支付订单状态失败: " + e.getMessage());
        }
    }

    /**
     * 关闭支付订单
     *
     * @param orderNo    订单号
     * @param payChannel 支付渠道
     * @return 关闭结果
     */
    public Result<Map<String, Object>> closePayOrder(String orderNo, String payChannel) {
        log.info("关闭支付订单: orderNo={}, channel={}", orderNo, payChannel);
        
        try {
            PayService payService = getPayService(payChannel);
            if (payService == null) {
                return Result.error("不支持的支付渠道: " + payChannel);
            }
            
            Map<String, Object> result = payService.close(orderNo, null);
            
            log.info("关闭支付订单成功: orderNo={}, result={}", orderNo, result);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("关闭支付订单失败: orderNo={}", orderNo, e);
            return Result.error("关闭支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付回调
     *
     * @param payChannel 支付渠道
     * @param params     回调参数
     * @return 处理结果
     */
    public PayOutMessage handlePayCallback(String payChannel, Map<String, Object> params) {
        log.info("处理支付回调: channel={}, params={}", payChannel, params);
        
        try {
            PayService payService = getPayService(payChannel);
            if (payService == null) {
                log.error("不支持的支付渠道: {}", payChannel);
                return PayOutMessage.failure("不支持的支付渠道");
            }
            
            // 验证回调签名
            if (!payService.verify(params)) {
                log.error("支付回调签名验证失败: channel={}, params={}", payChannel, params);
                return PayOutMessage.failure("签名验证失败");
            }
            
            // 处理支付结果
            boolean success = processPaymentResult(payChannel, params);
            
            if (success) {
                log.info("处理支付回调成功: channel={}", payChannel);
                return PayOutMessage.success("处理成功");
            } else {
                log.error("处理支付回调失败: channel={}", payChannel);
                return PayOutMessage.failure("处理失败");
            }
        } catch (Exception e) {
            log.error("处理支付回调异常: channel={}", payChannel, e);
            return PayOutMessage.failure("处理异常: " + e.getMessage());
        }
    }

    /**
     * 获取支付服务
     *
     * @param payChannel 支付渠道
     * @return 支付服务
     */
    private PayService getPayService(String payChannel) {
        return payServiceMap.get(payChannel);
    }

    /**
     * 构建支付订单
     *
     * @param payOrderDO 支付订单DO
     * @return 支付订单
     */
    private PayOrder buildPayOrder(PayOrderDO payOrderDO) {
        PayOrder payOrder = new PayOrder();
        payOrder.setTradeNo(payOrderDO.getOrderNo());
        payOrder.setPrice(payOrderDO.getPayAmount());
        payOrder.setSubject(payOrderDO.getTitle());
        payOrder.setBody(payOrderDO.getDescription());
        
        // 根据支付方式设置交易类型
        TransactionType transactionType = getTransactionType(payOrderDO.getPayMethod());
        payOrder.setTransactionType(transactionType);
        
        return payOrder;
    }

    /**
     * 获取交易类型
     *
     * @param payMethod 支付方式
     * @return 交易类型
     */
    private TransactionType getTransactionType(String payMethod) {
        switch (payMethod) {
            case "QR":
                return TransactionType.NATIVE;
            case "H5":
                return TransactionType.MWEB;
            case "APP":
                return TransactionType.APP;
            default:
                return TransactionType.NATIVE;
        }
    }

    /**
     * 处理支付结果
     *
     * @param payMethod 支付方式
     * @param orderInfo 订单信息
     * @return 处理结果
     */
    private String handlePayResult(String payMethod, Map<String, Object> orderInfo) {
        switch (payMethod) {
            case "QR":
                // 返回二维码URL
                return (String) orderInfo.get("code_url");
            case "H5":
                // 返回H5支付URL
                return (String) orderInfo.get("mweb_url");
            case "APP":
                // 返回APP支付参数
                return JsonUtil.toJsonString(orderInfo);
            default:
                return JsonUtil.toJsonString(orderInfo);
        }
    }

    /**
     * 处理支付结果
     *
     * @param payChannel 支付渠道
     * @param params     回调参数
     * @return 处理结果
     */
    private boolean processPaymentResult(String payChannel, Map<String, Object> params) {
        try {
            // 获取订单号
            String orderNo = getOrderNoFromParams(payChannel, params);
            if (StringUtil.isEmpty(orderNo)) {
                log.error("无法从回调参数中获取订单号: channel={}, params={}", payChannel, params);
                return false;
            }
            
            // 获取支付状态
            boolean isPaid = getPayStatusFromParams(payChannel, params);
            
            // 更新订单状态
            // 这里需要调用订单服务更新订单状态
            log.info("更新订单状态: orderNo={}, isPaid={}", orderNo, isPaid);
            
            return true;
        } catch (Exception e) {
            log.error("处理支付结果失败: channel={}, params={}", payChannel, params, e);
            return false;
        }
    }

    /**
     * 从回调参数中获取订单号
     *
     * @param payChannel 支付渠道
     * @param params     回调参数
     * @return 订单号
     */
    private String getOrderNoFromParams(String payChannel, Map<String, Object> params) {
        switch (payChannel) {
            case "ALIPAY":
                return (String) params.get("out_trade_no");
            case "WECHAT":
                return (String) params.get("out_trade_no");
            default:
                return null;
        }
    }

    /**
     * 从回调参数中获取支付状态
     *
     * @param payChannel 支付渠道
     * @param params     回调参数
     * @return 支付状态
     */
    private boolean getPayStatusFromParams(String payChannel, Map<String, Object> params) {
        switch (payChannel) {
            case "ALIPAY":
                return "TRADE_SUCCESS".equals(params.get("trade_status"));
            case "WECHAT":
                return "SUCCESS".equals(params.get("result_code"));
            default:
                return false;
        }
    }
}
