package com.hncboy.chatgpt.framework.cache;

import com.hncboy.chatgpt.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 缓存服务
 *
 * <AUTHOR>
 * @date 2023/3/22 13:40
 */
@Slf4j
@Service
public class CacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 设置缓存
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("设置缓存成功: key={}", key);
        } catch (Exception e) {
            log.error("设置缓存失败: key={}", key, e);
        }
    }

    /**
     * 设置缓存并指定过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期时间
     * @param unit    时间单位
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("设置缓存成功: key={}, timeout={}, unit={}", key, timeout, unit);
        } catch (Exception e) {
            log.error("设置缓存失败: key={}", key, e);
        }
    }

    /**
     * 获取缓存
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("获取缓存: key={}, value={}", key, value);
            return value;
        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取缓存并转换为指定类型
     *
     * @param key   键
     * @param clazz 目标类型
     * @param <T>   泛型
     * @return 值
     */
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value == null) {
                return null;
            }
            if (clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            // 尝试JSON转换
            if (value instanceof String) {
                return JsonUtil.parseObject((String) value, clazz);
            }
            return JsonUtil.parseObject(JsonUtil.toJsonString(value), clazz);
        } catch (Exception e) {
            log.error("获取缓存失败: key={}, clazz={}", key, clazz, e);
            return null;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 键
     * @return 是否删除成功
     */
    public boolean delete(String key) {
        try {
            Boolean result = redisTemplate.delete(key);
            log.debug("删除缓存: key={}, result={}", key, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("删除缓存失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 批量删除缓存
     *
     * @param keys 键集合
     * @return 删除的数量
     */
    public long delete(Collection<String> keys) {
        try {
            Long result = redisTemplate.delete(keys);
            log.debug("批量删除缓存: keys={}, result={}", keys, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("批量删除缓存失败: keys={}", keys, e);
            return 0;
        }
    }

    /**
     * 判断键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public boolean exists(String key) {
        try {
            Boolean result = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("判断键是否存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 设置过期时间
     *
     * @param key     键
     * @param timeout 过期时间
     * @param unit    时间单位
     * @return 是否设置成功
     */
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            log.debug("设置过期时间: key={}, timeout={}, unit={}, result={}", key, timeout, unit, result);
            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.error("设置过期时间失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 获取过期时间
     *
     * @param key 键
     * @return 过期时间(秒)
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            log.error("获取过期时间失败: key={}", key, e);
            return -1;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 递增值
     * @return 递增后的值
     */
    public long increment(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().increment(key, delta);
            log.debug("递增: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("递增失败: key={}, delta={}", key, delta, e);
            return 0;
        }
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 递减值
     * @return 递减后的值
     */
    public long decrement(String key, long delta) {
        try {
            Long result = redisTemplate.opsForValue().decrement(key, delta);
            log.debug("递减: key={}, delta={}, result={}", key, delta, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("递减失败: key={}, delta={}", key, delta, e);
            return 0;
        }
    }

    /**
     * 设置Hash值
     *
     * @param key     键
     * @param hashKey Hash键
     * @param value   值
     */
    public void hSet(String key, String hashKey, Object value) {
        try {
            redisTemplate.opsForHash().put(key, hashKey, value);
            log.debug("设置Hash值: key={}, hashKey={}", key, hashKey);
        } catch (Exception e) {
            log.error("设置Hash值失败: key={}, hashKey={}", key, hashKey, e);
        }
    }

    /**
     * 获取Hash值
     *
     * @param key     键
     * @param hashKey Hash键
     * @return 值
     */
    public Object hGet(String key, String hashKey) {
        try {
            Object value = redisTemplate.opsForHash().get(key, hashKey);
            log.debug("获取Hash值: key={}, hashKey={}, value={}", key, hashKey, value);
            return value;
        } catch (Exception e) {
            log.error("获取Hash值失败: key={}, hashKey={}", key, hashKey, e);
            return null;
        }
    }

    /**
     * 获取Hash所有值
     *
     * @param key 键
     * @return Hash映射
     */
    public Map<Object, Object> hGetAll(String key) {
        try {
            Map<Object, Object> map = redisTemplate.opsForHash().entries(key);
            log.debug("获取Hash所有值: key={}, size={}", key, map.size());
            return map;
        } catch (Exception e) {
            log.error("获取Hash所有值失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 删除Hash值
     *
     * @param key      键
     * @param hashKeys Hash键数组
     * @return 删除的数量
     */
    public long hDelete(String key, Object... hashKeys) {
        try {
            Long result = redisTemplate.opsForHash().delete(key, hashKeys);
            log.debug("删除Hash值: key={}, hashKeys={}, result={}", key, hashKeys, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("删除Hash值失败: key={}, hashKeys={}", key, hashKeys, e);
            return 0;
        }
    }

    /**
     * 添加到Set
     *
     * @param key    键
     * @param values 值数组
     * @return 添加的数量
     */
    public long sAdd(String key, Object... values) {
        try {
            Long result = redisTemplate.opsForSet().add(key, values);
            log.debug("添加到Set: key={}, values={}, result={}", key, values, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("添加到Set失败: key={}, values={}", key, values, e);
            return 0;
        }
    }

    /**
     * 获取Set所有成员
     *
     * @param key 键
     * @return Set成员
     */
    public Set<Object> sMembers(String key) {
        try {
            Set<Object> members = redisTemplate.opsForSet().members(key);
            log.debug("获取Set所有成员: key={}, size={}", key, members != null ? members.size() : 0);
            return members;
        } catch (Exception e) {
            log.error("获取Set所有成员失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 从Set中移除
     *
     * @param key    键
     * @param values 值数组
     * @return 移除的数量
     */
    public long sRemove(String key, Object... values) {
        try {
            Long result = redisTemplate.opsForSet().remove(key, values);
            log.debug("从Set中移除: key={}, values={}, result={}", key, values, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("从Set中移除失败: key={}, values={}", key, values, e);
            return 0;
        }
    }

    /**
     * 添加到List左侧
     *
     * @param key   键
     * @param value 值
     * @return List长度
     */
    public long lLeftPush(String key, Object value) {
        try {
            Long result = redisTemplate.opsForList().leftPush(key, value);
            log.debug("添加到List左侧: key={}, value={}, result={}", key, value, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("添加到List左侧失败: key={}, value={}", key, value, e);
            return 0;
        }
    }

    /**
     * 添加到List右侧
     *
     * @param key   键
     * @param value 值
     * @return List长度
     */
    public long lRightPush(String key, Object value) {
        try {
            Long result = redisTemplate.opsForList().rightPush(key, value);
            log.debug("添加到List右侧: key={}, value={}, result={}", key, value, result);
            return result != null ? result : 0;
        } catch (Exception e) {
            log.error("添加到List右侧失败: key={}, value={}", key, value, e);
            return 0;
        }
    }

    /**
     * 获取List范围内的元素
     *
     * @param key   键
     * @param start 开始位置
     * @param end   结束位置
     * @return List元素
     */
    public List<Object> lRange(String key, long start, long end) {
        try {
            List<Object> list = redisTemplate.opsForList().range(key, start, end);
            log.debug("获取List范围内的元素: key={}, start={}, end={}, size={}", key, start, end, list != null ? list.size() : 0);
            return list;
        } catch (Exception e) {
            log.error("获取List范围内的元素失败: key={}, start={}, end={}", key, start, end, e);
            return null;
        }
    }

    /**
     * 从List左侧弹出
     *
     * @param key 键
     * @return 弹出的值
     */
    public Object lLeftPop(String key) {
        try {
            Object value = redisTemplate.opsForList().leftPop(key);
            log.debug("从List左侧弹出: key={}, value={}", key, value);
            return value;
        } catch (Exception e) {
            log.error("从List左侧弹出失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 从List右侧弹出
     *
     * @param key 键
     * @return 弹出的值
     */
    public Object lRightPop(String key) {
        try {
            Object value = redisTemplate.opsForList().rightPop(key);
            log.debug("从List右侧弹出: key={}, value={}", key, value);
            return value;
        } catch (Exception e) {
            log.error("从List右侧弹出失败: key={}", key, e);
            return null;
        }
    }
}
