package com.hncboy.chatgpt.biz.common.controller;

import com.hncboy.chatgpt.common.result.Result;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfoDO;
import com.hncboy.chatgpt.db.service.user.UserBaseInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @date 2023/3/22 12:10
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@Api(tags = "用户管理")
public class UserController {

    @Resource
    private UserBaseInfoService userBaseInfoService;

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    @ApiOperation("获取用户信息")
    public Result<UserBaseInfoDO> getUserInfo(@ApiParam("用户ID") @PathVariable Integer userId) {
        log.info("获取用户信息: userId={}", userId);
        
        UserBaseInfoDO user = userBaseInfoService.getById(userId);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        return Result.success(user);
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param user   用户信息
     * @return 更新结果
     */
    @PutMapping("/{userId}")
    @ApiOperation("更新用户信息")
    public Result<Void> updateUserInfo(@ApiParam("用户ID") @PathVariable Integer userId,
                                       @RequestBody UserBaseInfoDO user) {
        log.info("更新用户信息: userId={}", userId);
        
        user.setId(userId);
        boolean success = userBaseInfoService.updateById(user);
        
        return Result.result(success, "更新成功", "更新失败");
    }

    /**
     * 根据账号和用户类型查询用户
     *
     * @param account  账号
     * @param userType 用户类型
     * @return 用户信息
     */
    @GetMapping("/account")
    @ApiOperation("根据账号查询用户")
    public Result<UserBaseInfoDO> getUserByAccount(@ApiParam("账号") @RequestParam String account,
                                                   @ApiParam("用户类型") @RequestParam String userType) {
        log.info("根据账号查询用户: account={}, userType={}", account, userType);
        
        UserBaseInfoDO user = userBaseInfoService.getByAccountAndType(account, userType);
        if (user == null) {
            return Result.error("用户不存在");
        }
        
        return Result.success(user);
    }
}
