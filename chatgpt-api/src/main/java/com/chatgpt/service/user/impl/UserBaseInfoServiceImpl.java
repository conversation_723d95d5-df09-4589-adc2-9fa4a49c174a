package com.chatgpt.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chatgpt.entity.user.UserBaseInfo;
import com.chatgpt.mapper.user.UserBaseInfoMapper;
import com.chatgpt.service.user.UserBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户基础信息服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Service
public class UserBaseInfoServiceImpl extends ServiceImpl<UserBaseInfoMapper, UserBaseInfo> implements UserBaseInfoService {

    @Resource
    private UserBaseInfoMapper userBaseInfoMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public UserBaseInfo getByAccountAndType(String account, String userType) {
        log.info("根据账号和用户类型查询用户: account={}, userType={}", account, userType);
        if (!StringUtils.hasText(account) || !StringUtils.hasText(userType)) {
            log.warn("账号或用户类型为空");
            return null;
        }
        return userBaseInfoMapper.selectByAccountAndType(account, userType);
    }

    @Override
    public UserBaseInfo getByPhone(String phone) {
        log.info("根据手机号查询用户: phone={}", phone);
        if (!StringUtils.hasText(phone)) {
            log.warn("手机号为空");
            return null;
        }
        return userBaseInfoMapper.selectByPhone(phone);
    }

    @Override
    public UserBaseInfo getByEmail(String email) {
        log.info("根据邮箱查询用户: email={}", email);
        if (!StringUtils.hasText(email)) {
            log.warn("邮箱为空");
            return null;
        }
        return userBaseInfoMapper.selectByEmail(email);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(Integer userId, LocalDateTime loginTime, String ip, String address) {
        log.info("更新用户登录信息: userId={}, loginTime={}, ip={}, address={}", userId, loginTime, ip, address);
        if (userId == null) {
            log.warn("用户ID为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.updateLoginInfo(userId, loginTime, ip, address);
            log.info("更新用户登录信息结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户登录信息异常", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePoints(Integer userId, Integer points) {
        log.info("更新用户积分: userId={}, points={}", userId, points);
        if (userId == null || points == null) {
            log.warn("用户ID或积分为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.updatePoints(userId, points);
            log.info("更新用户积分结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户积分异常", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTarotCoins(Integer userId, Integer tarotCoins) {
        log.info("更新用户塔罗币: userId={}, tarotCoins={}", userId, tarotCoins);
        if (userId == null || tarotCoins == null) {
            log.warn("用户ID或塔罗币为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.updateTarotCoins(userId, tarotCoins);
            log.info("更新用户塔罗币结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户塔罗币异常", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUseTimes(Integer userId, Integer useNum, Integer freeNum) {
        log.info("更新用户使用次数: userId={}, useNum={}, freeNum={}", userId, useNum, freeNum);
        if (userId == null) {
            log.warn("用户ID为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.updateUseTimes(userId, 
                useNum != null ? useNum : 0, 
                freeNum != null ? freeNum : 0);
            log.info("更新用户使用次数结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户使用次数异常", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVipEndTime(Integer userId, LocalDateTime vipEndTime) {
        log.info("更新用户VIP到期时间: userId={}, vipEndTime={}", userId, vipEndTime);
        if (userId == null) {
            log.warn("用户ID为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.updateVipEndTime(userId, vipEndTime);
            log.info("更新用户VIP到期时间结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户VIP到期时间异常", e);
            throw e;
        }
    }

    @Override
    public List<UserBaseInfo> listByParentId(Integer parentId) {
        log.info("根据上级用户ID查询下级用户列表: parentId={}", parentId);
        if (parentId == null) {
            log.warn("上级用户ID为空");
            return null;
        }
        return userBaseInfoMapper.selectByParentId(parentId);
    }

    @Override
    public Long countUsers(String userType) {
        log.info("统计用户总数: userType={}", userType);
        return userBaseInfoMapper.countUsers(userType);
    }

    @Override
    public Long countVipUsers(String userType) {
        log.info("统计VIP用户数: userType={}", userType);
        return userBaseInfoMapper.countVipUsers(userType);
    }

    @Override
    public IPage<UserBaseInfo> pageUsers(Page<UserBaseInfo> page, String userType, Integer status, String keyword) {
        log.info("分页查询用户列表: page={}, userType={}, status={}, keyword={}", 
            page.getCurrent(), userType, status, keyword);
        return userBaseInfoMapper.selectUserPage(page, userType, status, keyword);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Integer> userIds, Integer status) {
        log.info("批量更新用户状态: userIds={}, status={}", userIds, status);
        if (userIds == null || userIds.isEmpty() || status == null) {
            log.warn("用户ID列表或状态为空");
            return false;
        }
        try {
            int result = userBaseInfoMapper.batchUpdateStatus(userIds, status);
            log.info("批量更新用户状态结果: {}", result > 0 ? "成功" : "失败");
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新用户状态异常", e);
            throw e;
        }
    }

    @Override
    public boolean existsByAccountAndType(String account, String userType) {
        log.info("检查用户是否存在: account={}, userType={}", account, userType);
        return getByAccountAndType(account, userType) != null;
    }

    @Override
    public boolean existsByPhone(String phone) {
        log.info("检查手机号是否存在: phone={}", phone);
        return getByPhone(phone) != null;
    }

    @Override
    public boolean existsByEmail(String email) {
        log.info("检查邮箱是否存在: email={}", email);
        return getByEmail(email) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(UserBaseInfo userBaseInfo) {
        log.info("创建新用户: account={}, userType={}", userBaseInfo.getAccount(), userBaseInfo.getUserType());
        if (userBaseInfo == null) {
            log.warn("用户信息为空");
            return false;
        }
        try {
            // 加密密码
            if (StringUtils.hasText(userBaseInfo.getPassword())) {
                userBaseInfo.setPassword(passwordEncoder.encode(userBaseInfo.getPassword()));
            }
            
            // 设置默认值
            if (userBaseInfo.getPoints() == null) {
                userBaseInfo.setPoints(0);
            }
            if (userBaseInfo.getTarotCoins() == null) {
                userBaseInfo.setTarotCoins(0);
            }
            if (userBaseInfo.getStatus() == null) {
                userBaseInfo.setStatus(0);
            }
            if (!StringUtils.hasText(userBaseInfo.getLanguage())) {
                userBaseInfo.setLanguage("zh_CN");
            }
            if (!StringUtils.hasText(userBaseInfo.getCurrency())) {
                userBaseInfo.setCurrency("CNY");
            }
            if (!StringUtils.hasText(userBaseInfo.getTimezone())) {
                userBaseInfo.setTimezone("Asia/Shanghai");
            }
            
            boolean result = save(userBaseInfo);
            log.info("创建新用户结果: {}", result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("创建新用户异常", e);
            throw e;
        }
    }

    @Override
    public boolean validatePassword(Integer userId, String password) {
        log.info("验证用户密码: userId={}", userId);
        if (userId == null || !StringUtils.hasText(password)) {
            log.warn("用户ID或密码为空");
            return false;
        }
        UserBaseInfo user = getById(userId);
        if (user == null || !StringUtils.hasText(user.getPassword())) {
            log.warn("用户不存在或密码为空");
            return false;
        }
        return passwordEncoder.matches(password, user.getPassword());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(Integer userId, String newPassword) {
        log.info("更新用户密码: userId={}", userId);
        if (userId == null || !StringUtils.hasText(newPassword)) {
            log.warn("用户ID或新密码为空");
            return false;
        }
        try {
            UserBaseInfo user = new UserBaseInfo();
            user.setId(userId);
            user.setPassword(passwordEncoder.encode(newPassword));
            boolean result = updateById(user);
            log.info("更新用户密码结果: {}", result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("更新用户密码异常", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String resetPassword(Integer userId) {
        log.info("重置用户密码: userId={}", userId);
        if (userId == null) {
            log.warn("用户ID为空");
            return null;
        }
        try {
            String newPassword = UUID.randomUUID().toString().substring(0, 8);
            boolean result = updatePassword(userId, newPassword);
            if (result) {
                log.info("重置用户密码成功");
                return newPassword;
            } else {
                log.warn("重置用户密码失败");
                return null;
            }
        } catch (Exception e) {
            log.error("重置用户密码异常", e);
            throw e;
        }
    }

    @Override
    public Map<String, Object> getUserStats(Integer userId) {
        log.info("获取用户统计信息: userId={}", userId);
        Map<String, Object> stats = new HashMap<>();
        if (userId == null) {
            log.warn("用户ID为空");
            return stats;
        }
        
        UserBaseInfo user = getById(userId);
        if (user != null) {
            stats.put("points", user.getPoints());
            stats.put("tarotCoins", user.getTarotCoins());
            stats.put("totalAvailableTimes", user.getTotalAvailableTimes());
            stats.put("isVip", user.isVip());
            stats.put("isNormal", user.isNormal());
            stats.put("displayName", user.getDisplayName());
        }
        
        return stats;
    }
}
