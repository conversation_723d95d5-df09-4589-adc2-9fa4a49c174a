package com.chatgpt.service.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chatgpt.entity.user.UserBaseInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户基础信息服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface UserBaseInfoService extends IService<UserBaseInfo> {

    /**
     * 根据账号和用户类型查询用户
     * 
     * @param account 账号
     * @param userType 用户类型
     * @return 用户信息
     */
    UserBaseInfo getByAccountAndType(String account, String userType);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    UserBaseInfo getByPhone(String phone);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    UserBaseInfo getByEmail(String email);

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param ip IP地址
     * @param address 地址
     * @return 是否成功
     */
    boolean updateLoginInfo(Integer userId, LocalDateTime loginTime, String ip, String address);

    /**
     * 更新用户积分
     * 
     * @param userId 用户ID
     * @param points 积分变动量(正数增加，负数减少)
     * @return 是否成功
     */
    boolean updatePoints(Integer userId, Integer points);

    /**
     * 更新用户塔罗币
     * 
     * @param userId 用户ID
     * @param tarotCoins 塔罗币变动量(正数增加，负数减少)
     * @return 是否成功
     */
    boolean updateTarotCoins(Integer userId, Integer tarotCoins);

    /**
     * 更新用户使用次数
     * 
     * @param userId 用户ID
     * @param useNum 使用次数变动量
     * @param freeNum 免费次数变动量
     * @return 是否成功
     */
    boolean updateUseTimes(Integer userId, Integer useNum, Integer freeNum);

    /**
     * 更新用户VIP到期时间
     * 
     * @param userId 用户ID
     * @param vipEndTime VIP到期时间
     * @return 是否成功
     */
    boolean updateVipEndTime(Integer userId, LocalDateTime vipEndTime);

    /**
     * 根据上级用户ID查询下级用户列表
     * 
     * @param parentId 上级用户ID
     * @return 下级用户列表
     */
    List<UserBaseInfo> listByParentId(Integer parentId);

    /**
     * 统计用户总数
     * 
     * @param userType 用户类型(可选)
     * @return 用户总数
     */
    Long countUsers(String userType);

    /**
     * 统计VIP用户数
     * 
     * @param userType 用户类型(可选)
     * @return VIP用户数
     */
    Long countVipUsers(String userType);

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param userType 用户类型(可选)
     * @param status 用户状态(可选)
     * @param keyword 关键词(可选，搜索账号、昵称、手机号、邮箱)
     * @return 用户分页列表
     */
    IPage<UserBaseInfo> pageUsers(Page<UserBaseInfo> page, String userType, Integer status, String keyword);

    /**
     * 批量更新用户状态
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Integer> userIds, Integer status);

    /**
     * 检查用户是否存在
     * 
     * @param account 账号
     * @param userType 用户类型
     * @return 是否存在
     */
    boolean existsByAccountAndType(String account, String userType);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 创建新用户
     * 
     * @param userBaseInfo 用户信息
     * @return 是否成功
     */
    boolean createUser(UserBaseInfo userBaseInfo);

    /**
     * 验证用户密码
     * 
     * @param userId 用户ID
     * @param password 密码
     * @return 是否正确
     */
    boolean validatePassword(Integer userId, String password);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否成功
     */
    boolean updatePassword(Integer userId, String newPassword);

    /**
     * 重置用户密码
     * 
     * @param userId 用户ID
     * @return 新密码
     */
    String resetPassword(Integer userId);

    /**
     * 获取用户统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> getUserStats(Integer userId);
}
