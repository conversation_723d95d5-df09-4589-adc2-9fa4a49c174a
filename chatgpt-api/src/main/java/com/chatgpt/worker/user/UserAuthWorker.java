package com.chatgpt.worker.user;

import com.chatgpt.entity.user.UserBaseInfo;
import com.chatgpt.entity.user.UserJointLogin;
import com.chatgpt.framework.auth.JustAuthConfig;
import com.chatgpt.service.user.UserBaseInfoService;
import com.chatgpt.service.user.UserJointLoginService;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户认证业务逻辑处理器
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Component
public class UserAuthWorker {

    @Resource
    private UserBaseInfoService userBaseInfoService;

    @Resource
    private UserJointLoginService userJointLoginService;

    @Resource
    private Map<String, AuthRequest> authRequestMap;

    /**
     * 第三方登录处理
     * 
     * @param loginType 登录类型
     * @param callback 回调参数
     * @param userType 用户类型
     * @return 登录结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AuthResult thirdPartyLogin(String loginType, AuthCallback callback, String userType) {
        log.info("处理第三方登录: loginType={}, userType={}", loginType, userType);
        
        try {
            // 获取对应的第三方登录请求处理器
            AuthRequest authRequest = authRequestMap.get(loginType.toLowerCase());
            if (authRequest == null) {
                log.warn("不支持的登录类型: {}", loginType);
                return AuthResult.fail("不支持的登录类型");
            }

            // 获取第三方用户信息
            AuthResponse<AuthUser> response = authRequest.login(callback);
            if (!response.ok()) {
                log.warn("第三方登录失败: {}", response.getMsg());
                return AuthResult.fail("第三方登录失败: " + response.getMsg());
            }

            AuthUser authUser = response.getData();
            log.info("获取第三方用户信息成功: uuid={}, username={}", authUser.getUuid(), authUser.getUsername());

            // 查询是否已存在联合登录记录
            UserJointLogin existJointLogin = userJointLoginService.getByLoginTypeAndThirdPartyId(
                loginType.toUpperCase(), authUser.getUuid());

            UserBaseInfo userBaseInfo;
            if (existJointLogin != null) {
                // 已存在，更新登录信息
                log.info("用户已存在，更新登录信息: userId={}", existJointLogin.getUserId());
                userBaseInfo = userBaseInfoService.getById(existJointLogin.getUserId());
                if (userBaseInfo == null) {
                    log.error("用户基础信息不存在: userId={}", existJointLogin.getUserId());
                    return AuthResult.fail("用户信息异常");
                }

                // 更新联合登录信息
                updateJointLoginInfo(existJointLogin, authUser);
                userJointLoginService.updateById(existJointLogin);

                // 更新用户登录信息
                userBaseInfoService.updateLoginInfo(userBaseInfo.getId(), LocalDateTime.now(), 
                    callback.getState(), null);
            } else {
                // 不存在，创建新用户
                log.info("创建新用户: thirdPartyId={}", authUser.getUuid());
                userBaseInfo = createNewUser(authUser, userType);
                if (userBaseInfo == null) {
                    log.error("创建用户失败");
                    return AuthResult.fail("创建用户失败");
                }

                // 创建联合登录记录
                UserJointLogin newJointLogin = createJointLogin(userBaseInfo.getId(), loginType.toUpperCase(), authUser);
                userJointLoginService.save(newJointLogin);
            }

            log.info("第三方登录成功: userId={}, account={}", userBaseInfo.getId(), userBaseInfo.getAccount());
            return AuthResult.success(userBaseInfo);

        } catch (Exception e) {
            log.error("第三方登录异常", e);
            return AuthResult.fail("登录异常: " + e.getMessage());
        }
    }

    /**
     * 手机号登录处理
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param userType 用户类型
     * @return 登录结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AuthResult phoneLogin(String phone, String code, String userType) {
        log.info("处理手机号登录: phone={}, userType={}", phone, userType);

        if (!StringUtils.hasText(phone) || !StringUtils.hasText(code)) {
            log.warn("手机号或验证码为空");
            return AuthResult.fail("手机号或验证码不能为空");
        }

        try {
            // TODO: 验证短信验证码
            if (!validateSmsCode(phone, code)) {
                log.warn("验证码错误: phone={}", phone);
                return AuthResult.fail("验证码错误");
            }

            // 查询用户是否存在
            UserBaseInfo userBaseInfo = userBaseInfoService.getByPhone(phone);
            if (userBaseInfo == null) {
                // 不存在则创建新用户
                log.info("创建新用户: phone={}", phone);
                userBaseInfo = new UserBaseInfo();
                userBaseInfo.setAccount(phone);
                userBaseInfo.setPhone(phone);
                userBaseInfo.setUserType(userType);
                userBaseInfo.setStatus(0);
                
                if (!userBaseInfoService.createUser(userBaseInfo)) {
                    log.error("创建用户失败: phone={}", phone);
                    return AuthResult.fail("创建用户失败");
                }
            } else {
                // 更新登录信息
                userBaseInfoService.updateLoginInfo(userBaseInfo.getId(), LocalDateTime.now(), null, null);
            }

            log.info("手机号登录成功: userId={}, phone={}", userBaseInfo.getId(), phone);
            return AuthResult.success(userBaseInfo);

        } catch (Exception e) {
            log.error("手机号登录异常", e);
            return AuthResult.fail("登录异常: " + e.getMessage());
        }
    }

    /**
     * 邮箱登录处理
     * 
     * @param email 邮箱
     * @param password 密码
     * @param userType 用户类型
     * @return 登录结果
     */
    public AuthResult emailLogin(String email, String password, String userType) {
        log.info("处理邮箱登录: email={}, userType={}", email, userType);

        if (!StringUtils.hasText(email) || !StringUtils.hasText(password)) {
            log.warn("邮箱或密码为空");
            return AuthResult.fail("邮箱或密码不能为空");
        }

        try {
            // 查询用户
            UserBaseInfo userBaseInfo = userBaseInfoService.getByEmail(email);
            if (userBaseInfo == null) {
                log.warn("用户不存在: email={}", email);
                return AuthResult.fail("用户不存在");
            }

            // 验证密码
            if (!userBaseInfoService.validatePassword(userBaseInfo.getId(), password)) {
                log.warn("密码错误: email={}", email);
                return AuthResult.fail("密码错误");
            }

            // 检查用户状态
            if (!userBaseInfo.isNormal()) {
                log.warn("用户状态异常: email={}, status={}", email, userBaseInfo.getStatus());
                return AuthResult.fail("用户状态异常");
            }

            // 更新登录信息
            userBaseInfoService.updateLoginInfo(userBaseInfo.getId(), LocalDateTime.now(), null, null);

            log.info("邮箱登录成功: userId={}, email={}", userBaseInfo.getId(), email);
            return AuthResult.success(userBaseInfo);

        } catch (Exception e) {
            log.error("邮箱登录异常", e);
            return AuthResult.fail("登录异常: " + e.getMessage());
        }
    }

    /**
     * 更新联合登录信息
     * 
     * @param jointLogin 联合登录记录
     * @param authUser 第三方用户信息
     */
    private void updateJointLoginInfo(UserJointLogin jointLogin, AuthUser authUser) {
        jointLogin.setThirdPartyUsername(authUser.getUsername());
        jointLogin.setThirdPartyEmail(authUser.getEmail());
        jointLogin.setThirdPartyAvatar(authUser.getAvatar());
        jointLogin.setAccessToken(authUser.getToken().getAccessToken());
        jointLogin.setRefreshToken(authUser.getToken().getRefreshToken());
        if (authUser.getToken().getExpireIn() != null) {
            jointLogin.setExpiresTime(LocalDateTime.now().plusSeconds(authUser.getToken().getExpireIn()));
        }
        jointLogin.updateLoginInfo();
    }

    /**
     * 创建新用户
     * 
     * @param authUser 第三方用户信息
     * @param userType 用户类型
     * @return 用户信息
     */
    private UserBaseInfo createNewUser(AuthUser authUser, String userType) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setAccount(authUser.getUuid());
        userBaseInfo.setUserType(userType);
        userBaseInfo.setNickName(authUser.getNickname());
        userBaseInfo.setEmail(authUser.getEmail());
        userBaseInfo.setAvatarUrl(authUser.getAvatar());
        userBaseInfo.setStatus(0);
        
        if (userBaseInfoService.createUser(userBaseInfo)) {
            return userBaseInfo;
        }
        return null;
    }

    /**
     * 创建联合登录记录
     * 
     * @param userId 用户ID
     * @param loginType 登录类型
     * @param authUser 第三方用户信息
     * @return 联合登录记录
     */
    private UserJointLogin createJointLogin(Integer userId, String loginType, AuthUser authUser) {
        UserJointLogin jointLogin = new UserJointLogin();
        jointLogin.setUserId(userId);
        jointLogin.setLoginType(loginType);
        jointLogin.setThirdPartyId(authUser.getUuid());
        jointLogin.setThirdPartyUsername(authUser.getUsername());
        jointLogin.setThirdPartyEmail(authUser.getEmail());
        jointLogin.setThirdPartyAvatar(authUser.getAvatar());
        jointLogin.setAccessToken(authUser.getToken().getAccessToken());
        jointLogin.setRefreshToken(authUser.getToken().getRefreshToken());
        if (authUser.getToken().getExpireIn() != null) {
            jointLogin.setExpiresTime(LocalDateTime.now().plusSeconds(authUser.getToken().getExpireIn()));
        }
        jointLogin.setStatus(1);
        jointLogin.setLoginCount(1);
        jointLogin.setLastLoginTime(LocalDateTime.now());
        return jointLogin;
    }

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 是否正确
     */
    private boolean validateSmsCode(String phone, String code) {
        // TODO: 实现短信验证码验证逻辑
        // 这里暂时返回true，实际应该调用短信服务验证
        return true;
    }

    /**
     * 认证结果类
     */
    public static class AuthResult {
        private boolean success;
        private String message;
        private UserBaseInfo user;

        private AuthResult(boolean success, String message, UserBaseInfo user) {
            this.success = success;
            this.message = message;
            this.user = user;
        }

        public static AuthResult success(UserBaseInfo user) {
            return new AuthResult(true, "登录成功", user);
        }

        public static AuthResult fail(String message) {
            return new AuthResult(false, message, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public UserBaseInfo getUser() { return user; }
    }
}
