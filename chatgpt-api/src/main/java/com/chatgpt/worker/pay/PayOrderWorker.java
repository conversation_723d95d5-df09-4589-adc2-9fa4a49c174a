package com.chatgpt.worker.pay;

import com.chatgpt.entity.pay.PayOrder;
import com.chatgpt.entity.product.Product;
import com.chatgpt.entity.user.UserBaseInfo;
import com.chatgpt.service.pay.PayOrderService;
import com.chatgpt.service.product.ProductService;
import com.chatgpt.service.user.UserBaseInfoService;
import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.common.bean.PayOrder as PayJavaOrder;
import com.egzosn.pay.common.bean.PayOutMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 支付订单业务逻辑处理器
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Component
public class PayOrderWorker {

    @Resource
    private PayOrderService payOrderService;

    @Resource
    private ProductService productService;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    @Resource
    private Map<String, PayService> payServiceMap;

    /**
     * 创建支付订单
     * 
     * @param userId 用户ID
     * @param productId 产品ID
     * @param bizScene 业务场景
     * @param payChannel 支付渠道
     * @param payMethod 支付方式
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 支付订单创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public PayOrderResult createPayOrder(Integer userId, Long productId, String bizScene, 
                                        String payChannel, String payMethod, 
                                        String clientIp, String userAgent) {
        log.info("创建支付订单: userId={}, productId={}, bizScene={}, payChannel={}", 
            userId, productId, bizScene, payChannel);

        try {
            // 验证用户
            UserBaseInfo user = userBaseInfoService.getById(userId);
            if (user == null || !user.isNormal()) {
                log.warn("用户不存在或状态异常: userId={}", userId);
                return PayOrderResult.fail("用户状态异常");
            }

            // 验证产品
            Product product = productService.getById(productId);
            if (product == null || !product.isOnSale()) {
                log.warn("产品不存在或已下架: productId={}", productId);
                return PayOrderResult.fail("产品不存在或已下架");
            }

            // 检查产品业务场景匹配
            if (!bizScene.equals(product.getBizScene())) {
                log.warn("产品业务场景不匹配: productBizScene={}, requestBizScene={}", 
                    product.getBizScene(), bizScene);
                return PayOrderResult.fail("产品业务场景不匹配");
            }

            // 检查库存
            if (product.getStockQuantity() != null && product.getStockQuantity() >= 0 
                && product.getStockQuantity() <= 0) {
                log.warn("产品库存不足: productId={}, stock={}", productId, product.getStockQuantity());
                return PayOrderResult.fail("产品库存不足");
            }

            // 检查用户购买限制
            if (product.getLimitPerUser() != null && product.getLimitPerUser() > 0) {
                long userBoughtCount = payOrderService.countUserBoughtProduct(userId, productId);
                if (userBoughtCount >= product.getLimitPerUser()) {
                    log.warn("用户购买数量超限: userId={}, productId={}, bought={}, limit={}", 
                        userId, productId, userBoughtCount, product.getLimitPerUser());
                    return PayOrderResult.fail("购买数量超限");
                }
            }

            // 创建支付订单
            PayOrder payOrder = buildPayOrder(user, product, bizScene, payChannel, payMethod, clientIp, userAgent);
            if (!payOrderService.save(payOrder)) {
                log.error("保存支付订单失败");
                return PayOrderResult.fail("创建订单失败");
            }

            // 调用支付接口
            PayService payService = payServiceMap.get(payChannel);
            if (payService == null) {
                log.error("不支持的支付渠道: {}", payChannel);
                return PayOrderResult.fail("不支持的支付渠道");
            }

            PayJavaOrder payJavaOrder = buildPayJavaOrder(payOrder, payMethod);
            Map<String, Object> payResult = payService.orderInfo(payJavaOrder);

            // 更新订单支付参数
            payOrder.setPayParams(payResult.toString());
            payOrderService.updateById(payOrder);

            log.info("创建支付订单成功: orderNo={}", payOrder.getOrderNo());
            return PayOrderResult.success(payOrder, payResult);

        } catch (Exception e) {
            log.error("创建支付订单异常", e);
            return PayOrderResult.fail("创建订单异常: " + e.getMessage());
        }
    }

    /**
     * 处理支付回调
     * 
     * @param payChannel 支付渠道
     * @param callbackData 回调数据
     * @return 回调处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public PayCallbackResult handlePayCallback(String payChannel, Map<String, Object> callbackData) {
        log.info("处理支付回调: payChannel={}", payChannel);

        try {
            PayService payService = payServiceMap.get(payChannel);
            if (payService == null) {
                log.error("不支持的支付渠道: {}", payChannel);
                return PayCallbackResult.fail("不支持的支付渠道");
            }

            // 验证回调签名
            if (!payService.verify(callbackData)) {
                log.warn("支付回调签名验证失败: payChannel={}", payChannel);
                return PayCallbackResult.fail("签名验证失败");
            }

            // 解析回调数据
            String orderNo = extractOrderNo(callbackData);
            String thirdPartyOrderNo = extractThirdPartyOrderNo(callbackData);
            BigDecimal paidAmount = extractPaidAmount(callbackData);
            String payStatus = extractPayStatus(callbackData);

            if (!StringUtils.hasText(orderNo)) {
                log.warn("回调数据中缺少订单号");
                return PayCallbackResult.fail("订单号缺失");
            }

            // 查询订单
            PayOrder payOrder = payOrderService.getByOrderNo(orderNo);
            if (payOrder == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                return PayCallbackResult.fail("订单不存在");
            }

            // 检查订单状态
            if (payOrder.isPaid()) {
                log.info("订单已支付，忽略重复回调: orderNo={}", orderNo);
                return PayCallbackResult.success("订单已支付");
            }

            // 更新订单状态
            if ("SUCCESS".equals(payStatus) || "PAID".equals(payStatus)) {
                // 支付成功
                payOrderService.updatePayStatus(payOrder.getId(), PayOrder.PayStatus.PAID.getCode(),
                    thirdPartyOrderNo, paidAmount, LocalDateTime.now(), callbackData.toString());

                // 处理支付成功后的业务逻辑
                handlePaySuccess(payOrder);

                log.info("支付成功处理完成: orderNo={}", orderNo);
                return PayCallbackResult.success("支付成功");
            } else {
                // 支付失败
                payOrderService.updatePayStatus(payOrder.getId(), PayOrder.PayStatus.CANCELLED.getCode(),
                    thirdPartyOrderNo, null, null, callbackData.toString());

                log.info("支付失败: orderNo={}, status={}", orderNo, payStatus);
                return PayCallbackResult.success("支付失败");
            }

        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return PayCallbackResult.fail("回调处理异常: " + e.getMessage());
        }
    }

    /**
     * 查询订单支付状态
     * 
     * @param orderNo 订单号
     * @return 订单状态查询结果
     */
    public PayQueryResult queryPayStatus(String orderNo) {
        log.info("查询订单支付状态: orderNo={}", orderNo);

        try {
            PayOrder payOrder = payOrderService.getByOrderNo(orderNo);
            if (payOrder == null) {
                log.warn("订单不存在: orderNo={}", orderNo);
                return PayQueryResult.fail("订单不存在");
            }

            // 如果订单已支付，直接返回
            if (payOrder.isPaid()) {
                return PayQueryResult.success(payOrder, "已支付");
            }

            // 如果订单已过期，更新状态
            if (payOrder.isExpired()) {
                payOrderService.updatePayStatus(payOrder.getId(), PayOrder.PayStatus.EXPIRED.getCode(),
                    null, null, null, null);
                payOrder.setStatus(PayOrder.PayStatus.EXPIRED.getCode());
                return PayQueryResult.success(payOrder, "已过期");
            }

            // 调用第三方接口查询
            PayService payService = payServiceMap.get(payOrder.getPayChannel());
            if (payService != null && StringUtils.hasText(payOrder.getThirdPartyOrderNo())) {
                // TODO: 调用第三方查询接口
                // Map<String, Object> queryResult = payService.query(payOrder.getThirdPartyOrderNo());
                // 根据查询结果更新订单状态
            }

            return PayQueryResult.success(payOrder, "查询成功");

        } catch (Exception e) {
            log.error("查询订单支付状态异常", e);
            return PayQueryResult.fail("查询异常: " + e.getMessage());
        }
    }

    /**
     * 构建支付订单
     */
    private PayOrder buildPayOrder(UserBaseInfo user, Product product, String bizScene, 
                                  String payChannel, String payMethod, String clientIp, String userAgent) {
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo(generateOrderNo());
        payOrder.setUserId(user.getId());
        payOrder.setProductId(product.getId());
        payOrder.setProductType(product.getProductType());
        payOrder.setBizScene(bizScene);
        payOrder.setPayChannel(payChannel);
        payOrder.setPayMethod(payMethod);
        payOrder.setProductCurrency(product.getCurrency());
        payOrder.setPayCurrency(product.getCurrency());
        payOrder.setProductAmount(product.getSalePrice());
        payOrder.setPayAmount(product.getSalePrice());
        payOrder.setExchangeRate(BigDecimal.ONE);
        payOrder.setTitle(product.getProductName());
        payOrder.setDescription(product.getDescription());
        payOrder.setStatus(PayOrder.PayStatus.PENDING.getCode());
        payOrder.setExpireTime(LocalDateTime.now().plusMinutes(30)); // 30分钟过期
        payOrder.setClientIp(clientIp);
        payOrder.setUserAgent(userAgent);
        return payOrder;
    }

    /**
     * 构建pay-java-parent订单对象
     */
    private PayJavaOrder buildPayJavaOrder(PayOrder payOrder, String payMethod) {
        PayJavaOrder payJavaOrder = new PayJavaOrder();
        payJavaOrder.setOutTradeNo(payOrder.getOrderNo());
        payJavaOrder.setSubject(payOrder.getTitle());
        payJavaOrder.setBody(payOrder.getDescription());
        payJavaOrder.setPrice(payOrder.getPayAmount());
        payJavaOrder.setTransactionType(getTransactionType(payMethod));
        return payJavaOrder;
    }

    /**
     * 获取交易类型
     */
    private com.egzosn.pay.common.bean.TransactionType getTransactionType(String payMethod) {
        switch (payMethod) {
            case "QR":
                return com.egzosn.pay.common.bean.TransactionType.NATIVE;
            case "H5":
                return com.egzosn.pay.common.bean.TransactionType.MWEB;
            case "APP":
                return com.egzosn.pay.common.bean.TransactionType.APP;
            case "JSAPI":
                return com.egzosn.pay.common.bean.TransactionType.JSAPI;
            default:
                return com.egzosn.pay.common.bean.TransactionType.NATIVE;
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "PAY" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 处理支付成功后的业务逻辑
     */
    private void handlePaySuccess(PayOrder payOrder) {
        log.info("处理支付成功业务逻辑: orderNo={}", payOrder.getOrderNo());
        
        try {
            // 获取产品信息
            Product product = productService.getById(payOrder.getProductId());
            if (product == null) {
                log.warn("产品不存在: productId={}", payOrder.getProductId());
                return;
            }

            // 根据产品类型处理不同的业务逻辑
            switch (product.getProductType()) {
                case "POINTS":
                    // 增加积分
                    userBaseInfoService.updatePoints(payOrder.getUserId(), product.getTotalValue());
                    break;
                case "COINS":
                    // 增加塔罗币
                    userBaseInfoService.updateTarotCoins(payOrder.getUserId(), product.getTotalValue());
                    break;
                case "VIP":
                    // 延长VIP时间
                    UserBaseInfo user = userBaseInfoService.getById(payOrder.getUserId());
                    LocalDateTime vipEndTime = user.getVipEndTime();
                    if (vipEndTime == null || vipEndTime.isBefore(LocalDateTime.now())) {
                        vipEndTime = LocalDateTime.now();
                    }
                    vipEndTime = vipEndTime.plusDays(product.getVipDays());
                    userBaseInfoService.updateVipEndTime(payOrder.getUserId(), vipEndTime);
                    break;
                case "TIMES":
                    // 增加使用次数
                    userBaseInfoService.updateUseTimes(payOrder.getUserId(), product.getProductValue(), product.getBonusValue());
                    break;
                default:
                    log.warn("未知的产品类型: {}", product.getProductType());
                    break;
            }

            // 更新产品销量
            productService.updateSoldQuantity(product.getId(), 1);

            log.info("支付成功业务逻辑处理完成: orderNo={}", payOrder.getOrderNo());

        } catch (Exception e) {
            log.error("处理支付成功业务逻辑异常", e);
            // 这里可以考虑发送告警或者记录到异常表
        }
    }

    // 从回调数据中提取信息的方法
    private String extractOrderNo(Map<String, Object> callbackData) {
        // TODO: 根据不同支付渠道提取订单号
        return (String) callbackData.get("out_trade_no");
    }

    private String extractThirdPartyOrderNo(Map<String, Object> callbackData) {
        // TODO: 根据不同支付渠道提取第三方订单号
        return (String) callbackData.get("trade_no");
    }

    private BigDecimal extractPaidAmount(Map<String, Object> callbackData) {
        // TODO: 根据不同支付渠道提取支付金额
        Object amount = callbackData.get("total_amount");
        return amount != null ? new BigDecimal(amount.toString()) : null;
    }

    private String extractPayStatus(Map<String, Object> callbackData) {
        // TODO: 根据不同支付渠道提取支付状态
        return (String) callbackData.get("trade_status");
    }

    // 结果类定义
    public static class PayOrderResult {
        private boolean success;
        private String message;
        private PayOrder payOrder;
        private Map<String, Object> payData;

        private PayOrderResult(boolean success, String message, PayOrder payOrder, Map<String, Object> payData) {
            this.success = success;
            this.message = message;
            this.payOrder = payOrder;
            this.payData = payData;
        }

        public static PayOrderResult success(PayOrder payOrder, Map<String, Object> payData) {
            return new PayOrderResult(true, "创建成功", payOrder, payData);
        }

        public static PayOrderResult fail(String message) {
            return new PayOrderResult(false, message, null, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public PayOrder getPayOrder() { return payOrder; }
        public Map<String, Object> getPayData() { return payData; }
    }

    public static class PayCallbackResult {
        private boolean success;
        private String message;

        private PayCallbackResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static PayCallbackResult success(String message) {
            return new PayCallbackResult(true, message);
        }

        public static PayCallbackResult fail(String message) {
            return new PayCallbackResult(false, message);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }

    public static class PayQueryResult {
        private boolean success;
        private String message;
        private PayOrder payOrder;

        private PayQueryResult(boolean success, String message, PayOrder payOrder) {
            this.success = success;
            this.message = message;
            this.payOrder = payOrder;
        }

        public static PayQueryResult success(PayOrder payOrder, String message) {
            return new PayQueryResult(true, message, payOrder);
        }

        public static PayQueryResult fail(String message) {
            return new PayQueryResult(false, message, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public PayOrder getPayOrder() { return payOrder; }
    }
}
