package com.chatgpt.controller.user;

import com.chatgpt.common.result.Result;
import com.chatgpt.worker.user.UserAuthWorker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 用户认证控制器
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@Api(tags = "用户认证接口")
public class UserAuthController {

    @Resource
    private UserAuthWorker userAuthWorker;

    /**
     * 第三方登录回调处理
     * 
     * @param loginType 登录类型 wechat/google/facebook
     * @param callback 第三方回调参数
     * @param userType 用户类型 zns/tarot/chatoi
     * @return 登录结果
     */
    @PostMapping("/oauth/{loginType}/callback")
    @ApiOperation("第三方登录回调")
    public Result<?> oauthCallback(
            @ApiParam("登录类型") @PathVariable String loginType,
            @ApiParam("回调参数") @RequestBody AuthCallback callback,
            @ApiParam("用户类型") @RequestParam(defaultValue = "zns") String userType) {
        
        log.info("第三方登录回调: loginType={}, userType={}", loginType, userType);
        
        try {
            UserAuthWorker.AuthResult authResult = userAuthWorker.thirdPartyLogin(loginType, callback, userType);
            
            if (authResult.isSuccess()) {
                log.info("第三方登录成功: userId={}", authResult.getUser().getId());
                return Result.success(authResult.getUser(), "登录成功");
            } else {
                log.warn("第三方登录失败: {}", authResult.getMessage());
                return Result.error(authResult.getMessage());
            }
            
        } catch (Exception e) {
            log.error("第三方登录异常", e);
            return Result.error("登录异常，请稍后重试");
        }
    }

    /**
     * 手机号登录
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param userType 用户类型
     * @return 登录结果
     */
    @PostMapping("/phone/login")
    @ApiOperation("手机号登录")
    public Result<?> phoneLogin(
            @ApiParam("手机号") @RequestParam String phone,
            @ApiParam("验证码") @RequestParam String code,
            @ApiParam("用户类型") @RequestParam(defaultValue = "zns") String userType) {
        
        log.info("手机号登录: phone={}, userType={}", phone, userType);
        
        try {
            UserAuthWorker.AuthResult authResult = userAuthWorker.phoneLogin(phone, code, userType);
            
            if (authResult.isSuccess()) {
                log.info("手机号登录成功: userId={}", authResult.getUser().getId());
                return Result.success(authResult.getUser(), "登录成功");
            } else {
                log.warn("手机号登录失败: {}", authResult.getMessage());
                return Result.error(authResult.getMessage());
            }
            
        } catch (Exception e) {
            log.error("手机号登录异常", e);
            return Result.error("登录异常，请稍后重试");
        }
    }

    /**
     * 邮箱登录
     * 
     * @param email 邮箱
     * @param password 密码
     * @param userType 用户类型
     * @return 登录结果
     */
    @PostMapping("/email/login")
    @ApiOperation("邮箱登录")
    public Result<?> emailLogin(
            @ApiParam("邮箱") @RequestParam String email,
            @ApiParam("密码") @RequestParam String password,
            @ApiParam("用户类型") @RequestParam(defaultValue = "zns") String userType) {
        
        log.info("邮箱登录: email={}, userType={}", email, userType);
        
        try {
            UserAuthWorker.AuthResult authResult = userAuthWorker.emailLogin(email, password, userType);
            
            if (authResult.isSuccess()) {
                log.info("邮箱登录成功: userId={}", authResult.getUser().getId());
                return Result.success(authResult.getUser(), "登录成功");
            } else {
                log.warn("邮箱登录失败: {}", authResult.getMessage());
                return Result.error(authResult.getMessage());
            }
            
        } catch (Exception e) {
            log.error("邮箱登录异常", e);
            return Result.error("登录异常，请稍后重试");
        }
    }

    /**
     * 获取第三方登录授权URL
     * 
     * @param loginType 登录类型
     * @param userType 用户类型
     * @return 授权URL
     */
    @GetMapping("/oauth/{loginType}/authorize")
    @ApiOperation("获取第三方登录授权URL")
    public Result<?> getAuthorizeUrl(
            @ApiParam("登录类型") @PathVariable String loginType,
            @ApiParam("用户类型") @RequestParam(defaultValue = "zns") String userType) {
        
        log.info("获取第三方登录授权URL: loginType={}, userType={}", loginType, userType);
        
        try {
            // TODO: 实现获取授权URL的逻辑
            String authorizeUrl = "https://example.com/oauth/" + loginType + "/authorize";
            
            return Result.success(authorizeUrl, "获取授权URL成功");
            
        } catch (Exception e) {
            log.error("获取授权URL异常", e);
            return Result.error("获取授权URL失败");
        }
    }

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @return 发送结果
     */
    @PostMapping("/sms/send")
    @ApiOperation("发送短信验证码")
    public Result<?> sendSmsCode(@ApiParam("手机号") @RequestParam String phone) {
        
        log.info("发送短信验证码: phone={}", phone);
        
        try {
            // TODO: 实现发送短信验证码的逻辑
            
            return Result.success("验证码发送成功");
            
        } catch (Exception e) {
            log.error("发送短信验证码异常", e);
            return Result.error("发送验证码失败");
        }
    }

    /**
     * 用户注销
     * 
     * @param request HTTP请求
     * @return 注销结果
     */
    @PostMapping("/logout")
    @ApiOperation("用户注销")
    public Result<?> logout(HttpServletRequest request) {
        
        log.info("用户注销");
        
        try {
            // TODO: 实现用户注销逻辑，清除session、token等
            
            return Result.success("注销成功");
            
        } catch (Exception e) {
            log.error("用户注销异常", e);
            return Result.error("注销失败");
        }
    }

    /**
     * 刷新用户token
     * 
     * @param refreshToken 刷新token
     * @return 新的token
     */
    @PostMapping("/token/refresh")
    @ApiOperation("刷新用户token")
    public Result<?> refreshToken(@ApiParam("刷新token") @RequestParam String refreshToken) {
        
        log.info("刷新用户token");
        
        try {
            // TODO: 实现token刷新逻辑
            
            return Result.success("token刷新成功");
            
        } catch (Exception e) {
            log.error("刷新token异常", e);
            return Result.error("刷新token失败");
        }
    }
}
