package com.chatgpt.framework.auth;

import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.request.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * JustAuth第三方登录配置
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Configuration
public class JustAuthConfig {

    @Value("${justauth.wechat.client-id:}")
    private String wechatClientId;

    @Value("${justauth.wechat.client-secret:}")
    private String wechatClientSecret;

    @Value("${justauth.wechat.redirect-uri:}")
    private String wechatRedirectUri;

    @Value("${justauth.google.client-id:}")
    private String googleClientId;

    @Value("${justauth.google.client-secret:}")
    private String googleClientSecret;

    @Value("${justauth.google.redirect-uri:}")
    private String googleRedirectUri;

    @Value("${justauth.facebook.client-id:}")
    private String facebookClientId;

    @Value("${justauth.facebook.client-secret:}")
    private String facebookClientSecret;

    @Value("${justauth.facebook.redirect-uri:}")
    private String facebookRedirectUri;

    /**
     * 创建微信登录请求
     * 
     * @return 微信登录请求
     */
    @Bean
    public AuthWeChatOpenRequest weChatOpenRequest() {
        return new AuthWeChatOpenRequest(AuthConfig.builder()
                .clientId(wechatClientId)
                .clientSecret(wechatClientSecret)
                .redirectUri(wechatRedirectUri)
                .build());
    }

    /**
     * 创建Google登录请求
     * 
     * @return Google登录请求
     */
    @Bean
    public AuthGoogleRequest googleRequest() {
        return new AuthGoogleRequest(AuthConfig.builder()
                .clientId(googleClientId)
                .clientSecret(googleClientSecret)
                .redirectUri(googleRedirectUri)
                .build());
    }

    /**
     * 创建Facebook登录请求
     * 
     * @return Facebook登录请求
     */
    @Bean
    public AuthFacebookRequest facebookRequest() {
        return new AuthFacebookRequest(AuthConfig.builder()
                .clientId(facebookClientId)
                .clientSecret(facebookClientSecret)
                .redirectUri(facebookRedirectUri)
                .build());
    }

    /**
     * 获取所有第三方登录请求的映射
     * 
     * @return 第三方登录请求映射
     */
    @Bean
    public Map<String, AuthRequest> authRequestMap() {
        Map<String, AuthRequest> authRequestMap = new HashMap<>();
        authRequestMap.put("wechat", weChatOpenRequest());
        authRequestMap.put("google", googleRequest());
        authRequestMap.put("facebook", facebookRequest());
        return authRequestMap;
    }
}
