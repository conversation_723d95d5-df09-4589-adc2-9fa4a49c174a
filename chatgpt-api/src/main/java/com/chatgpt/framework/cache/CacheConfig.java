package com.chatgpt.framework.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 多级缓存配置
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Configuration
@EnableCaching
public class CacheConfig {

    @Value("${cache.caffeine.maximum-size:10000}")
    private long caffeineMaximumSize;

    @Value("${cache.caffeine.expire-after-write:300}")
    private long caffeineExpireAfterWrite;

    @Value("${cache.redis.default-ttl:3600}")
    private long redisDefaultTtl;

    @Value("${cache.redis.null-values:false}")
    private boolean redisAllowNullValues;

    /**
     * Caffeine本地缓存管理器
     * 
     * @return Caffeine缓存管理器
     */
    @Bean("caffeineCacheManager")
    public CacheManager caffeineCacheManager() {
        log.info("初始化Caffeine缓存管理器");
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(caffeineMaximumSize)
                .expireAfterWrite(caffeineExpireAfterWrite, TimeUnit.SECONDS)
                .recordStats());
        return cacheManager;
    }

    /**
     * Redis分布式缓存管理器
     * 
     * @param redisConnectionFactory Redis连接工厂
     * @return Redis缓存管理器
     */
    @Bean("redisCacheManager")
    @Primary
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        log.info("初始化Redis缓存管理器");
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(redisDefaultTtl))
                .disableCachingNullValues()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        if (redisAllowNullValues) {
            config = config.disableCachingNullValues();
        }

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(config)
                .build();
    }

    /**
     * 用户信息缓存
     * 
     * @return 用户信息缓存
     */
    @Bean("userInfoCache")
    public Cache<String, Object> userInfoCache() {
        log.info("初始化用户信息缓存");
        return Caffeine.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .recordStats()
                .build();
    }

    /**
     * 系统配置缓存
     * 
     * @return 系统配置缓存
     */
    @Bean("sysConfigCache")
    public Cache<String, Object> sysConfigCache() {
        log.info("初始化系统配置缓存");
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * 国际化消息缓存
     * 
     * @return 国际化消息缓存
     */
    @Bean("i18nMessageCache")
    public Cache<String, Object> i18nMessageCache() {
        log.info("初始化国际化消息缓存");
        return Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(2, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * AI模型配置缓存
     * 
     * @return AI模型配置缓存
     */
    @Bean("aiModelCache")
    public Cache<String, Object> aiModelCache() {
        log.info("初始化AI模型配置缓存");
        return Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * 支付渠道配置缓存
     * 
     * @return 支付渠道配置缓存
     */
    @Bean("payChannelCache")
    public Cache<String, Object> payChannelCache() {
        log.info("初始化支付渠道配置缓存");
        return Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * 塔罗牌数据缓存
     * 
     * @return 塔罗牌数据缓存
     */
    @Bean("tarotDataCache")
    public Cache<String, Object> tarotDataCache() {
        log.info("初始化塔罗牌数据缓存");
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(6, TimeUnit.HOURS)
                .recordStats()
                .build();
    }

    /**
     * 缓存类型枚举
     */
    public enum CacheType {
        USER_INFO("userInfo", "用户信息"),
        SYS_CONFIG("sysConfig", "系统配置"),
        I18N_MESSAGE("i18nMessage", "国际化消息"),
        AI_MODEL("aiModel", "AI模型"),
        PAY_CHANNEL("payChannel", "支付渠道"),
        TAROT_DATA("tarotData", "塔罗牌数据");

        private final String code;
        private final String name;

        CacheType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static CacheType fromCode(String code) {
            for (CacheType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
