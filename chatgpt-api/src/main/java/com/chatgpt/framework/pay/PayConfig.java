package com.chatgpt.framework.pay;

import com.egzosn.pay.ali.api.AliPayConfigStorage;
import com.egzosn.pay.ali.api.AliPayService;
import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.wx.api.WxPayConfigStorage;
import com.egzosn.pay.wx.api.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一支付框架配置
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Configuration
public class PayConfig {

    // 支付宝配置
    @Value("${pay.alipay.app-id:}")
    private String alipayAppId;

    @Value("${pay.alipay.private-key:}")
    private String alipayPrivateKey;

    @Value("${pay.alipay.public-key:}")
    private String alipayPublicKey;

    @Value("${pay.alipay.notify-url:}")
    private String alipayNotifyUrl;

    @Value("${pay.alipay.return-url:}")
    private String alipayReturnUrl;

    @Value("${pay.alipay.sign-type:RSA2}")
    private String alipaySignType;

    @Value("${pay.alipay.sandbox:false}")
    private boolean alipaySandbox;

    // 微信支付配置
    @Value("${pay.wechat.app-id:}")
    private String wechatAppId;

    @Value("${pay.wechat.mch-id:}")
    private String wechatMchId;

    @Value("${pay.wechat.key:}")
    private String wechatKey;

    @Value("${pay.wechat.notify-url:}")
    private String wechatNotifyUrl;

    @Value("${pay.wechat.return-url:}")
    private String wechatReturnUrl;

    @Value("${pay.wechat.sandbox:false}")
    private boolean wechatSandbox;

    /**
     * 创建支付宝支付服务
     * 
     * @return 支付宝支付服务
     */
    @Bean
    public AliPayService aliPayService() {
        log.info("初始化支付宝支付服务");
        AliPayConfigStorage aliPayConfigStorage = new AliPayConfigStorage();
        aliPayConfigStorage.setAppId(alipayAppId);
        aliPayConfigStorage.setKeyPrivate(alipayPrivateKey);
        aliPayConfigStorage.setKeyPublic(alipayPublicKey);
        aliPayConfigStorage.setNotifyUrl(alipayNotifyUrl);
        aliPayConfigStorage.setReturnUrl(alipayReturnUrl);
        aliPayConfigStorage.setSignType(alipaySignType);
        aliPayConfigStorage.setTest(alipaySandbox);
        
        return new AliPayService(aliPayConfigStorage);
    }

    /**
     * 创建微信支付服务
     * 
     * @return 微信支付服务
     */
    @Bean
    public WxPayService wxPayService() {
        log.info("初始化微信支付服务");
        WxPayConfigStorage wxPayConfigStorage = new WxPayConfigStorage();
        wxPayConfigStorage.setAppId(wechatAppId);
        wxPayConfigStorage.setMchId(wechatMchId);
        wxPayConfigStorage.setKeyPrivate(wechatKey);
        wxPayConfigStorage.setNotifyUrl(wechatNotifyUrl);
        wxPayConfigStorage.setReturnUrl(wechatReturnUrl);
        wxPayConfigStorage.setTest(wechatSandbox);
        
        return new WxPayService(wxPayConfigStorage);
    }

    /**
     * 获取所有支付服务的映射
     * 
     * @return 支付服务映射
     */
    @Bean
    public Map<String, PayService> payServiceMap() {
        log.info("初始化支付服务映射");
        Map<String, PayService> payServiceMap = new HashMap<>();
        payServiceMap.put("ALIPAY", aliPayService());
        payServiceMap.put("WECHAT", wxPayService());
        return payServiceMap;
    }

    /**
     * 支付渠道枚举
     */
    public enum PayChannel {
        ALIPAY("ALIPAY", "支付宝"),
        WECHAT("WECHAT", "微信支付"),
        MOMO("MOMO", "越南momo"),
        SEPAY("SEPAY", "越南sepay");

        private final String code;
        private final String name;

        PayChannel(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PayChannel fromCode(String code) {
            for (PayChannel channel : values()) {
                if (channel.code.equals(code)) {
                    return channel;
                }
            }
            return null;
        }
    }

    /**
     * 支付方式枚举
     */
    public enum PayMethod {
        QR("QR", "扫码支付"),
        H5("H5", "网页支付"),
        APP("APP", "应用支付"),
        JSAPI("JSAPI", "公众号支付");

        private final String code;
        private final String name;

        PayMethod(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PayMethod fromCode(String code) {
            for (PayMethod method : values()) {
                if (method.code.equals(code)) {
                    return method;
                }
            }
            return null;
        }
    }
}
