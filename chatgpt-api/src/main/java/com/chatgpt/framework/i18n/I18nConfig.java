package com.chatgpt.framework.i18n;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import java.util.Locale;

/**
 * 国际化配置
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Slf4j
@Configuration
public class I18nConfig implements WebMvcConfigurer {

    /**
     * 默认语言解析器
     * 
     * @return 语言解析器
     */
    @Bean
    public LocaleResolver localeResolver() {
        log.info("初始化语言解析器");
        SessionLocaleResolver localeResolver = new SessionLocaleResolver();
        // 设置默认语言为中文
        localeResolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return localeResolver;
    }

    /**
     * 语言切换拦截器
     * 
     * @return 语言切换拦截器
     */
    @Bean
    public LocaleChangeInterceptor localeChangeInterceptor() {
        log.info("初始化语言切换拦截器");
        LocaleChangeInterceptor interceptor = new LocaleChangeInterceptor();
        // 设置请求参数名，默认为locale
        interceptor.setParamName("lang");
        return interceptor;
    }

    /**
     * 添加拦截器
     * 
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(localeChangeInterceptor());
    }

    /**
     * 支持的语言枚举
     */
    public enum SupportedLanguage {
        ZH_CN("zh_CN", "中文简体", Locale.SIMPLIFIED_CHINESE),
        ZH_TW("zh_TW", "中文繁体", Locale.TRADITIONAL_CHINESE),
        EN_US("en_US", "English", Locale.US),
        VI_VN("vi_VN", "Tiếng Việt", new Locale("vi", "VN")),
        JA_JP("ja_JP", "日本語", Locale.JAPAN),
        KO_KR("ko_KR", "한국어", Locale.KOREA);

        private final String code;
        private final String name;
        private final Locale locale;

        SupportedLanguage(String code, String name, Locale locale) {
            this.code = code;
            this.name = name;
            this.locale = locale;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public Locale getLocale() {
            return locale;
        }

        public static SupportedLanguage fromCode(String code) {
            for (SupportedLanguage language : values()) {
                if (language.code.equals(code)) {
                    return language;
                }
            }
            return ZH_CN; // 默认返回中文简体
        }

        public static SupportedLanguage fromLocale(Locale locale) {
            for (SupportedLanguage language : values()) {
                if (language.locale.equals(locale)) {
                    return language;
                }
            }
            return ZH_CN; // 默认返回中文简体
        }
    }

    /**
     * 国际化模块枚举
     */
    public enum I18nModule {
        COMMON("COMMON", "通用"),
        TAROT("TAROT", "塔罗"),
        AI("AI", "AI"),
        CHAT("CHAT", "对话"),
        DRAW("DRAW", "绘画"),
        WRITE("WRITE", "写作"),
        MUSIC("MUSIC", "音乐"),
        PAY("PAY", "支付"),
        WITHDRAW("WITHDRAW", "提现"),
        USER("USER", "用户"),
        SYSTEM("SYSTEM", "系统");

        private final String code;
        private final String name;

        I18nModule(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static I18nModule fromCode(String code) {
            for (I18nModule module : values()) {
                if (module.code.equals(code)) {
                    return module;
                }
            }
            return COMMON; // 默认返回通用模块
        }
    }
}
