package com.chatgpt.mapper.pay;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chatgpt.entity.pay.PayOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一支付订单Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Mapper
public interface PayOrderMapper extends BaseMapper<PayOrder> {

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 支付订单
     */
    @Select("SELECT * FROM pay_order WHERE order_no = #{orderNo} AND deleted = 0")
    PayOrder selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据第三方订单号查询订单
     * 
     * @param thirdPartyOrderNo 第三方订单号
     * @return 支付订单
     */
    @Select("SELECT * FROM pay_order WHERE third_party_order_no = #{thirdPartyOrderNo} AND deleted = 0")
    PayOrder selectByThirdPartyOrderNo(@Param("thirdPartyOrderNo") String thirdPartyOrderNo);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @param status 订单状态(可选)
     * @return 订单列表
     */
    @Select("<script>" +
            "SELECT * FROM pay_order WHERE user_id = #{userId} AND deleted = 0 " +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    List<PayOrder> selectByUserId(@Param("userId") Integer userId, @Param("status") String status);

    /**
     * 更新订单支付状态
     * 
     * @param id 订单ID
     * @param status 订单状态
     * @param thirdPartyOrderNo 第三方订单号
     * @param paidAmount 实际支付金额
     * @param payTime 支付时间
     * @param payResult 支付结果
     * @return 更新行数
     */
    @Update("UPDATE pay_order SET status = #{status}, third_party_order_no = #{thirdPartyOrderNo}, " +
            "paid_amount = #{paidAmount}, pay_time = #{payTime}, pay_result = #{payResult}, " +
            "update_time = NOW() WHERE id = #{id}")
    int updatePayStatus(@Param("id") Long id,
                       @Param("status") String status,
                       @Param("thirdPartyOrderNo") String thirdPartyOrderNo,
                       @Param("paidAmount") BigDecimal paidAmount,
                       @Param("payTime") LocalDateTime payTime,
                       @Param("payResult") String payResult);

    /**
     * 查询过期的待支付订单
     * 
     * @return 过期订单列表
     */
    @Select("SELECT * FROM pay_order WHERE status = 'PENDING' AND expire_time < NOW() AND deleted = 0")
    List<PayOrder> selectExpiredOrders();

    /**
     * 批量更新过期订单状态
     * 
     * @return 更新行数
     */
    @Update("UPDATE pay_order SET status = 'EXPIRED', update_time = NOW() " +
            "WHERE status = 'PENDING' AND expire_time < NOW() AND deleted = 0")
    int batchUpdateExpiredOrders();

    /**
     * 统计订单数量
     * 
     * @param bizScene 业务场景(可选)
     * @param payChannel 支付渠道(可选)
     * @param status 订单状态(可选)
     * @param startTime 开始时间(可选)
     * @param endTime 结束时间(可选)
     * @return 订单数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM pay_order WHERE deleted = 0 " +
            "<if test='bizScene != null and bizScene != \"\"'>" +
            "AND biz_scene = #{bizScene} " +
            "</if>" +
            "<if test='payChannel != null and payChannel != \"\"'>" +
            "AND pay_channel = #{payChannel} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "AND create_time <= #{endTime} " +
            "</if>" +
            "</script>")
    Long countOrders(@Param("bizScene") String bizScene,
                    @Param("payChannel") String payChannel,
                    @Param("status") String status,
                    @Param("startTime") LocalDateTime startTime,
                    @Param("endTime") LocalDateTime endTime);

    /**
     * 统计订单金额
     * 
     * @param bizScene 业务场景(可选)
     * @param payChannel 支付渠道(可选)
     * @param status 订单状态(可选)
     * @param startTime 开始时间(可选)
     * @param endTime 结束时间(可选)
     * @return 订单总金额
     */
    @Select("<script>" +
            "SELECT COALESCE(SUM(pay_amount), 0) FROM pay_order WHERE deleted = 0 " +
            "<if test='bizScene != null and bizScene != \"\"'>" +
            "AND biz_scene = #{bizScene} " +
            "</if>" +
            "<if test='payChannel != null and payChannel != \"\"'>" +
            "AND pay_channel = #{payChannel} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "AND create_time <= #{endTime} " +
            "</if>" +
            "</script>")
    BigDecimal sumOrderAmount(@Param("bizScene") String bizScene,
                             @Param("payChannel") String payChannel,
                             @Param("status") String status,
                             @Param("startTime") LocalDateTime startTime,
                             @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询订单列表
     * 
     * @param page 分页参数
     * @param userId 用户ID(可选)
     * @param bizScene 业务场景(可选)
     * @param payChannel 支付渠道(可选)
     * @param status 订单状态(可选)
     * @param orderNo 订单号(可选)
     * @return 订单分页列表
     */
    @Select("<script>" +
            "SELECT * FROM pay_order WHERE deleted = 0 " +
            "<if test='userId != null'>" +
            "AND user_id = #{userId} " +
            "</if>" +
            "<if test='bizScene != null and bizScene != \"\"'>" +
            "AND biz_scene = #{bizScene} " +
            "</if>" +
            "<if test='payChannel != null and payChannel != \"\"'>" +
            "AND pay_channel = #{payChannel} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND status = #{status} " +
            "</if>" +
            "<if test='orderNo != null and orderNo != \"\"'>" +
            "AND order_no LIKE CONCAT('%', #{orderNo}, '%') " +
            "</if>" +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<PayOrder> selectOrderPage(Page<PayOrder> page,
                                   @Param("userId") Integer userId,
                                   @Param("bizScene") String bizScene,
                                   @Param("payChannel") String payChannel,
                                   @Param("status") String status,
                                   @Param("orderNo") String orderNo);

    /**
     * 统计各支付渠道的订单数量和金额
     * 
     * @param bizScene 业务场景(可选)
     * @param startTime 开始时间(可选)
     * @param endTime 结束时间(可选)
     * @return 支付渠道统计
     */
    @Select("<script>" +
            "SELECT pay_channel, COUNT(*) as order_count, COALESCE(SUM(pay_amount), 0) as total_amount " +
            "FROM pay_order WHERE deleted = 0 AND status = 'PAID' " +
            "<if test='bizScene != null and bizScene != \"\"'>" +
            "AND biz_scene = #{bizScene} " +
            "</if>" +
            "<if test='startTime != null'>" +
            "AND create_time >= #{startTime} " +
            "</if>" +
            "<if test='endTime != null'>" +
            "AND create_time <= #{endTime} " +
            "</if>" +
            "GROUP BY pay_channel" +
            "</script>")
    List<java.util.Map<String, Object>> countByPayChannel(@Param("bizScene") String bizScene,
                                                          @Param("startTime") LocalDateTime startTime,
                                                          @Param("endTime") LocalDateTime endTime);
}
