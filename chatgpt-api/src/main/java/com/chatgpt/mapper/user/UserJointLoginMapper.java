package com.chatgpt.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chatgpt.entity.user.UserJointLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户联合登录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Mapper
public interface UserJointLoginMapper extends BaseMapper<UserJointLogin> {

    /**
     * 根据登录类型和第三方ID查询
     * 
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = #{loginType} AND third_party_id = #{thirdPartyId} AND deleted = 0")
    UserJointLogin selectByLoginTypeAndThirdPartyId(@Param("loginType") String loginType, 
                                                   @Param("thirdPartyId") String thirdPartyId);

    /**
     * 根据用户ID查询所有登录方式
     * 
     * @param userId 用户ID
     * @return 登录方式列表
     */
    @Select("SELECT * FROM user_joint_login WHERE user_id = #{userId} AND deleted = 0 ORDER BY create_time DESC")
    List<UserJointLogin> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据unionId查询
     * 
     * @param unionId 联合ID
     * @return 联合登录信息
     */
    @Select("SELECT * FROM user_joint_login WHERE union_id = #{unionId} AND deleted = 0")
    UserJointLogin selectByUnionId(@Param("unionId") String unionId);

    /**
     * 更新登录信息
     * 
     * @param id 主键ID
     * @param accessToken 访问令牌
     * @param refreshToken 刷新令牌
     * @param expiresTime 过期时间
     * @return 更新行数
     */
    @Update("UPDATE user_joint_login SET access_token = #{accessToken}, refresh_token = #{refreshToken}, " +
            "expires_time = #{expiresTime}, last_login_time = NOW(), login_count = login_count + 1, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateTokenInfo(@Param("id") Long id,
                       @Param("accessToken") String accessToken,
                       @Param("refreshToken") String refreshToken,
                       @Param("expiresTime") LocalDateTime expiresTime);

    /**
     * 更新最后登录时间和登录次数
     * 
     * @param id 主键ID
     * @return 更新行数
     */
    @Update("UPDATE user_joint_login SET last_login_time = NOW(), login_count = login_count + 1, " +
            "update_time = NOW() WHERE id = #{id}")
    int updateLoginInfo(@Param("id") Long id);

    /**
     * 根据推荐人ID查询被推荐用户列表
     * 
     * @param referrerId 推荐人ID
     * @return 被推荐用户列表
     */
    @Select("SELECT * FROM user_joint_login WHERE referrer_id = #{referrerId} AND deleted = 0 ORDER BY create_time DESC")
    List<UserJointLogin> selectByReferrerId(@Param("referrerId") Long referrerId);

    /**
     * 更新幸运币
     * 
     * @param id 主键ID
     * @param luckyCoins 幸运币变动量
     * @return 更新行数
     */
    @Update("UPDATE user_joint_login SET lucky_coins = lucky_coins + #{luckyCoins}, update_time = NOW() WHERE id = #{id}")
    int updateLuckyCoins(@Param("id") Long id, @Param("luckyCoins") Long luckyCoins);

    /**
     * 统计各登录类型的用户数量
     * 
     * @return 登录类型统计
     */
    @Select("SELECT login_type, COUNT(*) as count FROM user_joint_login WHERE deleted = 0 GROUP BY login_type")
    List<java.util.Map<String, Object>> countByLoginType();

    /**
     * 查询过期的令牌
     * 
     * @return 过期令牌列表
     */
    @Select("SELECT * FROM user_joint_login WHERE expires_time < NOW() AND deleted = 0")
    List<UserJointLogin> selectExpiredTokens();

    /**
     * 清理过期令牌
     * 
     * @return 清理行数
     */
    @Update("UPDATE user_joint_login SET access_token = NULL, refresh_token = NULL, expires_time = NULL, " +
            "update_time = NOW() WHERE expires_time < NOW() AND deleted = 0")
    int clearExpiredTokens();
}
