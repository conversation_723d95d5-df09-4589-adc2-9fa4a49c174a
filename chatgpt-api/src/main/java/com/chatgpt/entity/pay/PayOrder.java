package com.chatgpt.entity.pay;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 统一支付订单实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pay_order")
public class PayOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 第三方订单号
     */
    @TableField("third_party_order_no")
    private String thirdPartyOrderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 业务场景 tarot:塔罗 zns:智能社 chatoi:对话
     */
    @TableField("biz_scene")
    private String bizScene;

    /**
     * 支付渠道 ALIPAY:支付宝 WECHAT:微信 MOMO:越南momo SEPAY:越南sepay
     */
    @TableField("pay_channel")
    private String payChannel;

    /**
     * 支付方式 QR:扫码 H5:网页 APP:应用
     */
    @TableField("pay_method")
    private String payMethod;

    /**
     * 产品币种(商品定价币种)
     */
    @TableField("product_currency")
    private String productCurrency;

    /**
     * 支付币种(实际支付币种)
     */
    @TableField("pay_currency")
    private String payCurrency;

    /**
     * 产品金额(产品币种)
     */
    @TableField("product_amount")
    private BigDecimal productAmount;

    /**
     * 支付金额(支付币种)
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 汇率(产品币种到支付币种)
     */
    @TableField("exchange_rate")
    private BigDecimal exchangeRate;

    /**
     * 实际支付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    /**
     * 订单标题
     */
    @TableField("title")
    private String title;

    /**
     * 订单描述
     */
    @TableField("description")
    private String description;

    /**
     * 订单状态 PENDING:待支付 PAID:已支付 CANCELLED:已取消 EXPIRED:已过期 REFUNDED:已退款
     */
    @TableField("status")
    private String status;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 异步通知地址
     */
    @TableField("notify_url")
    private String notifyUrl;

    /**
     * 同步跳转地址
     */
    @TableField("return_url")
    private String returnUrl;

    /**
     * 支付参数(JSON)
     */
    @TableField("pay_params")
    private String payParams;

    /**
     * 支付结果(JSON)
     */
    @TableField("pay_result")
    private String payResult;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查订单是否已支付
     * 
     * @return true:已支付 false:未支付
     */
    public boolean isPaid() {
        return PayStatus.PAID.getCode().equals(status);
    }

    /**
     * 检查订单是否已过期
     * 
     * @return true:已过期 false:未过期
     */
    public boolean isExpired() {
        return expireTime != null && expireTime.isBefore(LocalDateTime.now());
    }

    /**
     * 检查订单是否可以支付
     * 
     * @return true:可以支付 false:不可以支付
     */
    public boolean canPay() {
        return PayStatus.PENDING.getCode().equals(status) && !isExpired();
    }

    /**
     * 支付状态枚举
     */
    public enum PayStatus {
        PENDING("PENDING", "待支付"),
        PAID("PAID", "已支付"),
        CANCELLED("CANCELLED", "已取消"),
        EXPIRED("EXPIRED", "已过期"),
        REFUNDED("REFUNDED", "已退款");

        private final String code;
        private final String name;

        PayStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PayStatus fromCode(String code) {
            for (PayStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 支付渠道枚举
     */
    public enum PayChannel {
        ALIPAY("ALIPAY", "支付宝"),
        WECHAT("WECHAT", "微信"),
        MOMO("MOMO", "越南momo"),
        SEPAY("SEPAY", "越南sepay");

        private final String code;
        private final String name;

        PayChannel(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PayChannel fromCode(String code) {
            for (PayChannel channel : values()) {
                if (channel.code.equals(code)) {
                    return channel;
                }
            }
            return null;
        }
    }

    /**
     * 业务场景枚举
     */
    public enum BizScene {
        TAROT("tarot", "塔罗"),
        ZNS("zns", "智能社"),
        CHATOI("chatoi", "对话");

        private final String code;
        private final String name;

        BizScene(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static BizScene fromCode(String code) {
            for (BizScene scene : values()) {
                if (scene.code.equals(code)) {
                    return scene;
                }
            }
            return null;
        }
    }
}
