package com.chatgpt.entity.tarot;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 塔罗解读记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tarot_reading_record")
public class TarotReadingRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 牌阵ID
     */
    @TableField("spread_id")
    private Long spreadId;

    /**
     * 问题
     */
    @TableField("question")
    private String question;

    /**
     * 抽牌结果(JSON格式)
     */
    @TableField("draw_result")
    private String drawResult;

    /**
     * AI解读结果
     */
    @TableField("answer")
    private String answer;

    /**
     * 解读模式 0:抽牌 1:自选
     */
    @TableField("interpretation_mode")
    private Integer interpretationMode;

    /**
     * 解读状态 0:未回答 1:已回答
     */
    @TableField("status")
    private Integer status;

    /**
     * 微信openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查是否已回答
     * 
     * @return true:已回答 false:未回答
     */
    public boolean isAnswered() {
        return status != null && status == 1;
    }

    /**
     * 检查是否为抽牌模式
     * 
     * @return true:抽牌模式 false:自选模式
     */
    public boolean isDrawMode() {
        return interpretationMode != null && interpretationMode == 0;
    }

    /**
     * 检查是否为自选模式
     * 
     * @return true:自选模式 false:抽牌模式
     */
    public boolean isSelectMode() {
        return interpretationMode != null && interpretationMode == 1;
    }

    /**
     * 解读模式枚举
     */
    public enum InterpretationMode {
        DRAW(0, "抽牌"),
        SELECT(1, "自选");

        private final Integer code;
        private final String name;

        InterpretationMode(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static InterpretationMode fromCode(Integer code) {
            for (InterpretationMode mode : values()) {
                if (mode.code.equals(code)) {
                    return mode;
                }
            }
            return null;
        }
    }

    /**
     * 解读状态枚举
     */
    public enum ReadingStatus {
        PENDING(0, "未回答"),
        ANSWERED(1, "已回答");

        private final Integer code;
        private final String name;

        ReadingStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static ReadingStatus fromCode(Integer code) {
            for (ReadingStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
