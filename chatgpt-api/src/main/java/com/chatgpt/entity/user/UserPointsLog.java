package com.chatgpt.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户积分日志实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_points_log")
public class UserPointsLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 关联订单
     */
    @TableField("rel_order")
    private String relOrder;

    /**
     * 变动数量
     */
    @TableField("points")
    private Integer points;

    /**
     * 变动类型 EARN:获得 CONSUME:消费
     */
    @TableField("points_type")
    private String pointsType;

    /**
     * 币种类型 POINTS:塔罗币 COINS:积分
     */
    @TableField("currency_type")
    private String currencyType;

    /**
     * 业务类型 SIGN:签到 PAY:支付 REFUND:退款 TAROT:塔罗解读
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 检查是否为获得积分
     * 
     * @return true:获得 false:消费
     */
    public boolean isEarn() {
        return PointsType.EARN.getCode().equals(pointsType);
    }

    /**
     * 检查是否为消费积分
     * 
     * @return true:消费 false:获得
     */
    public boolean isConsume() {
        return PointsType.CONSUME.getCode().equals(pointsType);
    }

    /**
     * 变动类型枚举
     */
    public enum PointsType {
        EARN("EARN", "获得"),
        CONSUME("CONSUME", "消费");

        private final String code;
        private final String name;

        PointsType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static PointsType fromCode(String code) {
            for (PointsType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 币种类型枚举
     */
    public enum CurrencyType {
        POINTS("POINTS", "塔罗币"),
        COINS("COINS", "积分");

        private final String code;
        private final String name;

        CurrencyType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static CurrencyType fromCode(String code) {
            for (CurrencyType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        SIGN("SIGN", "签到"),
        PAY("PAY", "支付"),
        REFUND("REFUND", "退款"),
        TAROT("TAROT", "塔罗解读"),
        CHAT("CHAT", "AI对话"),
        DRAW("DRAW", "AI绘画"),
        WRITE("WRITE", "AI写作"),
        MUSIC("MUSIC", "AI音乐");

        private final String code;
        private final String name;

        BusinessType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static BusinessType fromCode(String code) {
            for (BusinessType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
