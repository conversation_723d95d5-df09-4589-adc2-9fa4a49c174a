package com.chatgpt.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户签到记录实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_check_in_record")
public class UserCheckInRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 签到日期 yyyyMMDD
     */
    @TableField("check_in_date")
    private String checkInDate;

    /**
     * 签到星期
     */
    @TableField("week")
    private String week;

    /**
     * 签到类型 TAROT:塔罗牌 ZNS:智能社 CHATOI:对话
     */
    @TableField("type")
    private String type;

    /**
     * 是否补签 0:否 1:是
     */
    @TableField("is_make_up")
    private String isMakeUp;

    /**
     * 奖励数量
     */
    @TableField("awarded")
    private Integer awarded;

    /**
     * 奖励类型 POINTS:积分 COINS:代币
     */
    @TableField("award_type")
    private String awardType;

    /**
     * 连续签到天数
     */
    @TableField("continuous_days")
    private Integer continuousDays;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 检查是否为补签
     * 
     * @return true:补签 false:正常签到
     */
    public boolean isMakeUpSign() {
        return "1".equals(isMakeUp);
    }

    /**
     * 签到类型枚举
     */
    public enum CheckInType {
        TAROT("TAROT", "塔罗牌"),
        ZNS("ZNS", "智能社"),
        CHATOI("CHATOI", "对话");

        private final String code;
        private final String name;

        CheckInType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static CheckInType fromCode(String code) {
            for (CheckInType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 奖励类型枚举
     */
    public enum AwardType {
        POINTS("POINTS", "积分"),
        COINS("COINS", "代币");

        private final String code;
        private final String name;

        AwardType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static AwardType fromCode(String code) {
            for (AwardType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
