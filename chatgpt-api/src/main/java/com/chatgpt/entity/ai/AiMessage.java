package com.chatgpt.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一消息实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_message")
public class AiMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父消息ID
     */
    @TableField("parent_msg_id")
    private Long parentMsgId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 房间ID
     */
    @TableField("room_id")
    private Long roomId;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐
     */
    @TableField("ability_type")
    private String abilityType;

    /**
     * 消息类型 1:请求 2:回复
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 消息内容
     */
    @TableField("content")
    private String content;

    /**
     * 提示词(绘画专用)
     */
    @TableField("prompt")
    private String prompt;

    /**
     * 任务ID(绘画专用)
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 事件(绘画专用)
     */
    @TableField("action")
    private String action;

    /**
     * 请求参数(写作专用,JSON格式)
     */
    @TableField("inputs")
    private String inputs;

    /**
     * 主题(写作专用)
     */
    @TableField("topic")
    private String topic;

    /**
     * 模型ID
     */
    @TableField("model_gid")
    private String modelGid;

    /**
     * 智能体ID
     */
    @TableField("agent_id")
    private Integer agentId;

    /**
     * 智能体名称
     */
    @TableField("agent_name")
    private String agentName;

    /**
     * 应用标题
     */
    @TableField("agent_title")
    private String agentTitle;

    /**
     * 站点ID
     */
    @TableField("site_id")
    private Integer siteId;

    /**
     * 站点名称
     */
    @TableField("site_name")
    private String siteName;

    /**
     * 站点URL
     */
    @TableField("site_url")
    private String siteUrl;

    /**
     * 累计Tokens
     */
    @TableField("total_tokens")
    private Long totalTokens;

    /**
     * 消耗点数
     */
    @TableField("consume")
    private Integer consume;

    /**
     * 状态 0:初始化 1:完成
     */
    @TableField("status")
    private Integer status;

    /**
     * 进度(绘画专用)
     */
    @TableField("progress")
    private Integer progress;

    /**
     * 图片URL(绘画专用)
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * IP
     */
    @TableField("ip")
    private String ip;

    /**
     * 用户openId
     */
    @TableField("open_id")
    private String openId;

    /**
     * 第一个字符出现时间
     */
    @TableField("first_char_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime firstCharTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 检查是否为用户请求消息
     * 
     * @return true:用户请求 false:AI回复
     */
    public boolean isUserRequest() {
        return messageType != null && messageType == 1;
    }

    /**
     * 检查是否为AI回复消息
     * 
     * @return true:AI回复 false:用户请求
     */
    public boolean isAiReply() {
        return messageType != null && messageType == 2;
    }

    /**
     * 检查消息是否已完成
     * 
     * @return true:已完成 false:处理中
     */
    public boolean isCompleted() {
        return status != null && status == 1;
    }

    /**
     * AI能力类型枚举
     */
    public enum AbilityType {
        CHAT("CHAT", "对话"),
        DRAW("DRAW", "绘画"),
        WRITE("WRITE", "写作"),
        MUSIC("MUSIC", "音乐");

        private final String code;
        private final String name;

        AbilityType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static AbilityType fromCode(String code) {
            for (AbilityType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        REQUEST(1, "请求"),
        REPLY(2, "回复");

        private final Integer code;
        private final String name;

        MessageType(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static MessageType fromCode(Integer code) {
            for (MessageType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        INIT(0, "初始化"),
        COMPLETED(1, "完成");

        private final Integer code;
        private final String name;

        MessageStatus(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static MessageStatus fromCode(Integer code) {
            for (MessageStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}
