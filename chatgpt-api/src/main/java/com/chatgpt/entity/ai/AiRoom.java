package com.chatgpt.entity.ai;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI统一房间实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ai_room")
public class AiRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 房间名称
     */
    @TableField("title")
    private String title;

    /**
     * 房间简介
     */
    @TableField("description")
    private String description;

    /**
     * 能力类型 CHAT:对话 DRAW:绘画 MUSIC:音乐
     */
    @TableField("ability_type")
    private String abilityType;

    /**
     * 系统回答
     */
    @TableField("sys_content")
    private String sysContent;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private Integer roleId;

    /**
     * 房间图片
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 微信用户ID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 会话ID
     */
    @TableField("conversation_id")
    private String conversationId;

    /**
     * IP
     */
    @TableField("ip")
    private String ip;

    /**
     * 是否公开
     */
    @TableField("open")
    private String open;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 检查房间是否公开
     * 
     * @return true:公开 false:私有
     */
    public boolean isPublic() {
        return "1".equals(open);
    }

    /**
     * AI能力类型枚举
     */
    public enum AbilityType {
        CHAT("CHAT", "对话"),
        DRAW("DRAW", "绘画"),
        MUSIC("MUSIC", "音乐");

        private final String code;
        private final String name;

        AbilityType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static AbilityType fromCode(String code) {
            for (AbilityType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
