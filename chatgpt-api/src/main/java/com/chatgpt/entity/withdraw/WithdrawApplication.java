package com.chatgpt.entity.withdraw;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请实体类
 * 
 * <AUTHOR>
 * @since 2025-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw_application")
public class WithdrawApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 申请单号
     */
    @TableField("application_no")
    private String applicationNo;

    /**
     * 提现类型 ALIPAY:支付宝 WECHAT:微信 BANK:银行卡
     */
    @TableField("withdraw_type")
    private String withdrawType;

    /**
     * 提现账户
     */
    @TableField("withdraw_account")
    private String withdrawAccount;

    /**
     * 账户姓名
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 银行名称(银行卡提现)
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 开户行(银行卡提现)
     */
    @TableField("bank_branch")
    private String bankBranch;

    /**
     * 提现金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 实际到账金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 状态 PENDING:待审核 APPROVED:已通过 REJECTED:已拒绝 PROCESSING:处理中 COMPLETED:已完成 FAILED:失败
     */
    @TableField("status")
    private String status;

    /**
     * 申请原因
     */
    @TableField("apply_reason")
    private String applyReason;

    /**
     * 拒绝原因
     */
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 处理备注
     */
    @TableField("process_remark")
    private String processRemark;

    /**
     * 处理人
     */
    @TableField("process_user")
    private String processUser;

    /**
     * 处理时间
     */
    @TableField("process_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /**
     * 第三方订单号
     */
    @TableField("third_party_order_no")
    private String thirdPartyOrderNo;

    /**
     * 第三方处理结果(JSON)
     */
    @TableField("third_party_result")
    private String thirdPartyResult;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否删除 0:否 1:是
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 检查申请是否待审核
     * 
     * @return true:待审核 false:非待审核
     */
    public boolean isPending() {
        return WithdrawStatus.PENDING.getCode().equals(status);
    }

    /**
     * 检查申请是否已完成
     * 
     * @return true:已完成 false:未完成
     */
    public boolean isCompleted() {
        return WithdrawStatus.COMPLETED.getCode().equals(status);
    }

    /**
     * 检查申请是否可以取消
     * 
     * @return true:可以取消 false:不可以取消
     */
    public boolean canCancel() {
        return WithdrawStatus.PENDING.getCode().equals(status) || 
               WithdrawStatus.APPROVED.getCode().equals(status);
    }

    /**
     * 提现状态枚举
     */
    public enum WithdrawStatus {
        PENDING("PENDING", "待审核"),
        APPROVED("APPROVED", "已通过"),
        REJECTED("REJECTED", "已拒绝"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "失败");

        private final String code;
        private final String name;

        WithdrawStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static WithdrawStatus fromCode(String code) {
            for (WithdrawStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 提现类型枚举
     */
    public enum WithdrawType {
        ALIPAY("ALIPAY", "支付宝"),
        WECHAT("WECHAT", "微信"),
        BANK("BANK", "银行卡");

        private final String code;
        private final String name;

        WithdrawType(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static WithdrawType fromCode(String code) {
            for (WithdrawType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
